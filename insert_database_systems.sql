-- إدراج أنظمة إدارة قاعدة البيانات في جدول S_ERP_SYSTEM
-- SHP ERP - Database Administration Systems

-- التحقق من وجود النظام الرئيسي أولاً
SELECT COUNT(*) FROM S_ERP_SYSTEM WHERE SYSTEM_ID = 201;

-- إدراج النظام الرئيسي لإدارة قاعدة البيانات Oracle
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
) VALUES (
    201,
    'نظام إدارة قاعدة البيانات Oracle',
    'DBADM',
    1,  -- تحت النظام الرئيسي ERP
    201,
    'Y',
    SYSDATE,
    'SYSTEM',
    '🗃️',
    'نظام شامل لإدارة قاعدة البيانات Oracle مع جميع الأدوات المتقدمة',
    'database_admin_window.py'
);

-- إدراج الأنظمة الفرعية لإدارة قاعدة البيانات

-- 1. إدارة الاتصالات
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
) VALUES (
    202,
    'إدارة الاتصالات',
    'DBCON',
    201,  -- تحت نظام إدارة قاعدة البيانات
    1,
    'Y',
    SYSDATE,
    'SYSTEM',
    '🔗',
    'إدارة اتصالات قاعدة البيانات وإعداد الاتصالات الجديدة',
    'database_connection_dialog.py'
);

-- 2. محرر SQL المتقدم
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
) VALUES (
    203,
    'محرر SQL المتقدم',
    'SQLED',
    201,  -- تحت نظام إدارة قاعدة البيانات
    2,
    'Y',
    SYSDATE,
    'SYSTEM',
    '📝',
    'محرر SQL متقدم مع تلوين الصيغة وتنفيذ الاستعلامات',
    'advanced_sql_editor.py'
);

-- 3. النسخ الاحتياطي
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
) VALUES (
    204,
    'النسخ الاحتياطي',
    'DBBAK',
    201,  -- تحت نظام إدارة قاعدة البيانات
    3,
    'Y',
    SYSDATE,
    'SYSTEM',
    '💾',
    'نظام النسخ الاحتياطي المتقدم مع جدولة وإدارة النسخ',
    'advanced_backup_system.py'
);

-- 4. إحصائيات قاعدة البيانات
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
) VALUES (
    205,
    'إحصائيات قاعدة البيانات',
    'DBSTA',
    201,  -- تحت نظام إدارة قاعدة البيانات
    4,
    'Y',
    SYSDATE,
    'SYSTEM',
    '📊',
    'عرض إحصائيات شاملة لقاعدة البيانات والأداء',
    'database_admin_window.py'
);

-- 5. إدارة الجداول
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
) VALUES (
    206,
    'إدارة الجداول',
    'DBTBL',
    201,  -- تحت نظام إدارة قاعدة البيانات
    5,
    'Y',
    SYSDATE,
    'SYSTEM',
    '📋',
    'إدارة شاملة للجداول مع عرض البيانات والبنية والتصدير',
    'database_admin_window.py'
);

-- 6. سجلات النظام
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
) VALUES (
    207,
    'سجلات النظام',
    'DBLOGS',
    201,  -- تحت نظام إدارة قاعدة البيانات
    6,
    'Y',
    SYSDATE,
    'SYSTEM',
    '📜',
    'عرض وإدارة سجلات النظام وسجلات قاعدة البيانات',
    'database_admin_window.py'
);

-- تأكيد الإدراج
COMMIT;

-- التحقق من الإدراج
SELECT 
    SYSTEM_ID,
    SYSTEM_NAME,
    SYSTEM_CODE,
    PARENT_ID,
    SYSTEM_ORDER,
    IS_ACTIVE,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION
FROM S_ERP_SYSTEM 
WHERE PARENT_ID = 201 OR SYSTEM_ID = 201
ORDER BY SYSTEM_ORDER;

-- عرض الهيكل الشجري للأنظمة
SELECT 
    LEVEL,
    LPAD(' ', (LEVEL-1)*4) || SYSTEM_ICON || ' ' || SYSTEM_NAME AS SYSTEM_TREE,
    SYSTEM_CODE,
    SYSTEM_ID
FROM S_ERP_SYSTEM
START WITH SYSTEM_ID = 201
CONNECT BY PRIOR SYSTEM_ID = PARENT_ID
ORDER SIBLINGS BY SYSTEM_ORDER;

-- إحصائيات الأنظمة المدرجة
SELECT 
    'النظام الرئيسي' AS TYPE,
    COUNT(*) AS COUNT
FROM S_ERP_SYSTEM 
WHERE SYSTEM_ID = 201
UNION ALL
SELECT 
    'الأنظمة الفرعية' AS TYPE,
    COUNT(*) AS COUNT
FROM S_ERP_SYSTEM 
WHERE PARENT_ID = 201;

PROMPT 'تم إدراج أنظمة إدارة قاعدة البيانات بنجاح في جدول S_ERP_SYSTEM';
PROMPT 'النظام الرئيسي: نظام إدارة قاعدة البيانات Oracle (ID: 201)';
PROMPT 'الأنظمة الفرعية: 6 أنظمة (IDs: 202-207)';
PROMPT 'يمكنك الآن رؤية هذه الأنظمة في قائمة الأنظمة في التطبيق';
