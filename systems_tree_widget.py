#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Systems Tree Widget
مكون شجرة الأنظمة الرئيسية
"""

import sys
from typing import List, Dict, Any, Optional
from PySide6.QtWidgets import (
    QWidget, QTreeWidget, QTreeWidgetItem, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QFrame, QSplitter, QTextEdit,
    QGroupBox, QApplication, QHeaderView
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QIcon, QPalette, QColor

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class SystemsTreeWidget(QWidget):
    """مكون شجرة الأنظمة"""
    
    # إشارات
    system_selected = Signal(dict)  # عند اختيار نظام
    system_activated = Signal(dict)  # عند تفعيل نظام (double click)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.systems_data = []
        self.systems_tree_data = {}
        self.current_system = None
        
        self.init_ui()
        self.setup_styles()
        
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي للعرض الصحيح"""
        if not ARABIC_SUPPORT or not text:
            return text
            
        try:
            reshaped_text = arabic_reshaper.reshape(
                text, 
                configuration={
                    'delete_harakat': False,
                    'support_zwj': True,
                    'use_unshaped_instead_of_isolated': False
                }
            )
            return get_display(reshaped_text, base_dir='R')
        except Exception:
            return text
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # عنوان المكون
        title_label = QLabel(self.format_arabic_text("🌳 شجرة الأنظمة الرئيسية"))
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        search_label = QLabel(self.format_arabic_text("🔍 البحث:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(self.format_arabic_text("ابحث في الأنظمة..."))
        self.search_input.textChanged.connect(self.filter_systems)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        main_layout.addLayout(search_layout)
        
        # المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # شجرة الأنظمة
        self.create_systems_tree()
        splitter.addWidget(self.tree_widget)
        
        # لوحة التفاصيل
        self.create_details_panel()
        splitter.addWidget(self.details_panel)
        
        # تعيين النسب
        splitter.setSizes([300, 200])
        
        # شريط الحالة
        self.status_label = QLabel(self.format_arabic_text("جاري التحميل..."))
        main_layout.addWidget(self.status_label)
        
    def create_systems_tree(self):
        """إنشاء شجرة الأنظمة"""
        self.tree_widget = QTreeWidget()
        self.tree_widget.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الأعمدة
        headers = [
            self.format_arabic_text("اسم النظام"),
            self.format_arabic_text("الرمز"),
            self.format_arabic_text("الرقم"),
            self.format_arabic_text("الترتيب")
        ]
        self.tree_widget.setHeaderLabels(headers)
        
        # إعداد خصائص الشجرة
        self.tree_widget.setAlternatingRowColors(True)
        self.tree_widget.setRootIsDecorated(True)
        self.tree_widget.setExpandsOnDoubleClick(False)
        
        # ضبط عرض الأعمدة
        header = self.tree_widget.header()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # اسم النظام
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الرمز
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الرقم
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الترتيب
        
        # ربط الإشارات
        self.tree_widget.itemSelectionChanged.connect(self.on_selection_changed)
        self.tree_widget.itemDoubleClicked.connect(self.on_item_double_clicked)
        
    def create_details_panel(self):
        """إنشاء لوحة التفاصيل"""
        self.details_panel = QGroupBox(self.format_arabic_text("تفاصيل النظام"))
        self.details_panel.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self.details_panel)
        
        # معلومات النظام
        self.system_info = QTextEdit()
        self.system_info.setReadOnly(True)
        self.system_info.setMaximumHeight(150)
        layout.addWidget(self.system_info)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.expand_all_btn = QPushButton(self.format_arabic_text("توسيع الكل"))
        self.expand_all_btn.clicked.connect(self.expand_all)
        buttons_layout.addWidget(self.expand_all_btn)
        
        self.collapse_all_btn = QPushButton(self.format_arabic_text("طي الكل"))
        self.collapse_all_btn.clicked.connect(self.collapse_all)
        buttons_layout.addWidget(self.collapse_all_btn)
        
        self.refresh_btn = QPushButton(self.format_arabic_text("تحديث"))
        self.refresh_btn.clicked.connect(self.refresh_data)
        buttons_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(buttons_layout)
        
    def setup_styles(self):
        """إعداد الأنماط"""
        # نمط الشجرة
        tree_style = """
        QTreeWidget {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 11px;
        }
        QTreeWidget::item {
            padding: 5px;
            border-bottom: 1px solid #e9ecef;
        }
        QTreeWidget::item:selected {
            background-color: #007bff;
            color: white;
        }
        QTreeWidget::item:hover {
            background-color: #e3f2fd;
        }
        """
        self.tree_widget.setStyleSheet(tree_style)
        
        # نمط لوحة التفاصيل
        details_style = """
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        """
        self.details_panel.setStyleSheet(details_style)
        
    def load_systems_data(self, systems_data: List[Dict[str, Any]], tree_data: Dict[str, Any]):
        """تحميل بيانات الأنظمة"""
        self.systems_data = systems_data
        self.systems_tree_data = tree_data
        
        self.populate_tree()
        self.update_status()
        
    def populate_tree(self):
        """ملء الشجرة بالبيانات"""
        self.tree_widget.clear()
        
        if not self.systems_data:
            return
            
        # الحصول على الأنظمة الجذر
        root_systems = self.get_root_systems()
        
        # إضافة الأنظمة الجذر
        for system in root_systems:
            root_item = self.create_tree_item(system)
            self.tree_widget.addTopLevelItem(root_item)
            self.add_children_recursive(root_item, system['SYSTEM_ID'])
            
        # توسيع المستوى الأول
        self.tree_widget.expandToDepth(0)
        
    def get_root_systems(self) -> List[Dict[str, Any]]:
        """الحصول على الأنظمة الجذر"""
        root_systems = []
        for system in self.systems_data:
            parent_id = system.get('PARENT_SYSTEM_ID')
            if not parent_id or parent_id == 0:
                root_systems.append(system)
        
        # ترتيب حسب SYSTEM_ORDER
        root_systems.sort(key=lambda x: x.get('SYSTEM_ORDER', 0) or 0)
        return root_systems
        
    def get_child_systems(self, parent_id: int) -> List[Dict[str, Any]]:
        """الحصول على الأنظمة الفرعية"""
        children = []
        for system in self.systems_data:
            if system.get('PARENT_SYSTEM_ID') == parent_id:
                children.append(system)
        
        # ترتيب حسب SYSTEM_ORDER
        children.sort(key=lambda x: x.get('SYSTEM_ORDER', 0) or 0)
        return children
        
    def create_tree_item(self, system: Dict[str, Any]) -> QTreeWidgetItem:
        """إنشاء عنصر في الشجرة"""
        system_name = system.get('SYSTEM_NAME', '')
        system_code = system.get('SYSTEM_CODE', '')
        system_id = str(system.get('SYSTEM_ID', ''))
        system_order = str(system.get('SYSTEM_ORDER', '') or '')
        
        # تنسيق النص العربي
        formatted_name = self.format_arabic_text(system_name)
        
        item = QTreeWidgetItem([formatted_name, system_code, system_id, system_order])
        item.setData(0, Qt.UserRole, system)  # حفظ بيانات النظام
        
        # تعيين أيقونة حسب نوع النظام
        try:
            from PySide6.QtWidgets import QStyle
            if self.get_child_systems(system.get('SYSTEM_ID', 0)):
                item.setIcon(0, self.style().standardIcon(QStyle.SP_DirIcon))
            else:
                item.setIcon(0, self.style().standardIcon(QStyle.SP_FileIcon))
        except:
            # في حالة عدم توفر الأيقونات، استخدم نص بديل
            if self.get_child_systems(system.get('SYSTEM_ID', 0)):
                item.setText(0, f"📁 {formatted_name}")
            else:
                item.setText(0, f"📄 {formatted_name}")
            # إعادة تعيين النص الأصلي
            item.setText(0, formatted_name)
            
        return item
        
    def add_children_recursive(self, parent_item: QTreeWidgetItem, parent_id: int):
        """إضافة الأطفال بشكل تكراري"""
        children = self.get_child_systems(parent_id)
        
        for child_system in children:
            child_item = self.create_tree_item(child_system)
            parent_item.addChild(child_item)
            
            # إضافة أطفال الطفل
            self.add_children_recursive(child_item, child_system['SYSTEM_ID'])
            
    def filter_systems(self, search_text: str):
        """تصفية الأنظمة حسب النص"""
        if not search_text:
            # إظهار جميع العناصر
            self.show_all_items()
            return
            
        # إخفاء جميع العناصر أولاً
        self.hide_all_items()
        
        # البحث وإظهار العناصر المطابقة
        self.search_and_show_items(search_text.lower())
        
    def show_all_items(self):
        """إظهار جميع العناصر"""
        for i in range(self.tree_widget.topLevelItemCount()):
            self.show_item_recursive(self.tree_widget.topLevelItem(i))
            
    def hide_all_items(self):
        """إخفاء جميع العناصر"""
        for i in range(self.tree_widget.topLevelItemCount()):
            self.hide_item_recursive(self.tree_widget.topLevelItem(i))
            
    def show_item_recursive(self, item: QTreeWidgetItem):
        """إظهار العنصر وأطفاله"""
        item.setHidden(False)
        for i in range(item.childCount()):
            self.show_item_recursive(item.child(i))
            
    def hide_item_recursive(self, item: QTreeWidgetItem):
        """إخفاء العنصر وأطفاله"""
        item.setHidden(True)
        for i in range(item.childCount()):
            self.hide_item_recursive(item.child(i))
            
    def search_and_show_items(self, search_text: str):
        """البحث وإظهار العناصر المطابقة"""
        for i in range(self.tree_widget.topLevelItemCount()):
            self.search_item_recursive(self.tree_widget.topLevelItem(i), search_text)
            
    def search_item_recursive(self, item: QTreeWidgetItem, search_text: str) -> bool:
        """البحث في العنصر وأطفاله"""
        # فحص النص في العنصر الحالي
        item_text = item.text(0).lower() + " " + item.text(1).lower()
        matches = search_text in item_text
        
        # فحص الأطفال
        child_matches = False
        for i in range(item.childCount()):
            if self.search_item_recursive(item.child(i), search_text):
                child_matches = True
                
        # إظهار العنصر إذا كان يطابق أو له أطفال تطابق
        if matches or child_matches:
            item.setHidden(False)
            # إظهار الآباء أيضاً
            parent = item.parent()
            while parent:
                parent.setHidden(False)
                parent = parent.parent()
            return True
        else:
            item.setHidden(True)
            return False
            
    def on_selection_changed(self):
        """معالج تغيير الاختيار"""
        current_item = self.tree_widget.currentItem()
        if current_item:
            system_data = current_item.data(0, Qt.UserRole)
            if system_data:
                self.current_system = system_data
                self.update_details(system_data)
                self.system_selected.emit(system_data)
                
    def on_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """معالج النقر المزدوج"""
        system_data = item.data(0, Qt.UserRole)
        if system_data:
            self.system_activated.emit(system_data)
            
    def update_details(self, system: Dict[str, Any]):
        """تحديث لوحة التفاصيل"""
        details_text = f"""
<h3>{self.format_arabic_text('معلومات النظام')}</h3>
<table border="1" cellpadding="5" cellspacing="0" style="width:100%">
<tr><td><b>{self.format_arabic_text('رقم النظام')}:</b></td><td>{system.get('SYSTEM_ID', '')}</td></tr>
<tr><td><b>{self.format_arabic_text('رمز النظام')}:</b></td><td>{system.get('SYSTEM_CODE', '')}</td></tr>
<tr><td><b>{self.format_arabic_text('اسم النظام')}:</b></td><td>{self.format_arabic_text(system.get('SYSTEM_NAME', ''))}</td></tr>
<tr><td><b>{self.format_arabic_text('النظام الأب')}:</b></td><td>{system.get('PARENT_SYSTEM_ID', '') or self.format_arabic_text('نظام جذر')}</td></tr>
<tr><td><b>{self.format_arabic_text('الترتيب')}:</b></td><td>{system.get('SYSTEM_ORDER', '') or ''}</td></tr>
<tr><td><b>{self.format_arabic_text('رقم الشاشة')}:</b></td><td>{system.get('FORM_NO', '') or ''}</td></tr>
</table>
        """
        self.system_info.setHtml(details_text)
        
    def update_status(self):
        """تحديث شريط الحالة"""
        count = len(self.systems_data)
        root_count = len(self.get_root_systems())
        status_text = self.format_arabic_text(f"تم تحميل {count} نظام ({root_count} نظام جذر)")
        self.status_label.setText(status_text)
        
    def expand_all(self):
        """توسيع جميع العقد"""
        self.tree_widget.expandAll()
        
    def collapse_all(self):
        """طي جميع العقد"""
        self.tree_widget.collapseAll()
        
    def refresh_data(self):
        """تحديث البيانات"""
        # إشارة لطلب تحديث البيانات من المصدر
        self.status_label.setText(self.format_arabic_text("جاري التحديث..."))
        
        # يمكن إضافة منطق التحديث هنا
        QTimer.singleShot(1000, lambda: self.status_label.setText(
            self.format_arabic_text("تم التحديث بنجاح")
        ))

# اختبار المكون
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # بيانات تجريبية
    test_data = [
        {"SYSTEM_ID": 1, "SYSTEM_CODE": "ERP", "SYSTEM_NAME": "نظام ERP", "PARENT_SYSTEM_ID": None, "SYSTEM_ORDER": 1},
        {"SYSTEM_ID": 10, "SYSTEM_CODE": "STP", "SYSTEM_NAME": "نظام الاعدادات العامة", "PARENT_SYSTEM_ID": 1, "SYSTEM_ORDER": 10},
        {"SYSTEM_ID": 20, "SYSTEM_CODE": "ADM", "SYSTEM_NAME": "نظام ادارة النظام و المستخدمين", "PARENT_SYSTEM_ID": 1, "SYSTEM_ORDER": 20},
    ]
    
    widget = SystemsTreeWidget()
    widget.load_systems_data(test_data, {})
    widget.show()
    
    sys.exit(app.exec())
