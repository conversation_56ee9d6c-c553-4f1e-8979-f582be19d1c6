#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Enhanced Launcher
مشغل محسن لنظام SHP ERP
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QSplashScreen, QLabel
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QFont

def show_splash_screen():
    """عرض شاشة البداية"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # إنشاء شاشة البداية
    splash_label = QLabel()
    splash_label.setFixedSize(400, 300)
    splash_label.setAlignment(Qt.AlignCenter)
    splash_label.setStyleSheet("""
        QLabel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1B5E20, stop:0.5 #4CAF50, stop:1 #1B5E20);
            color: white;
            border-radius: 15px;
            font-size: 24px;
            font-weight: bold;
        }
    """)
    splash_label.setText("SHP ERP\n\nنظام تخطيط موارد المؤسسة\n\nجاري التحميل...")
    
    # عرض الشاشة
    splash_label.setWindowFlags(Qt.SplashScreen | Qt.WindowStaysOnTopHint)
    splash_label.show()
    
    return app, splash_label

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_modules = [
        'PySide6',
        'cx_Oracle',
        'arabic_reshaper',
        'python_bidi'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'python_bidi':
                import bidi
            else:
                __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    return missing_modules

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🔍 اختبار قاعدة البيانات...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        if db.connect():
            print("✅ الاتصال بقاعدة البيانات ناجح")
            
            # اختبار الجدول
            result = db.execute_query("SELECT COUNT(*) as COUNT FROM S_ERP_SYSTEM")
            if result:
                count = result[0]['COUNT']
                print(f"📊 عدد الأنظمة: {count}")
            
            db.disconnect()
            return True
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def launch_main_application():
    """تشغيل التطبيق الرئيسي"""
    print("\n🚀 تشغيل التطبيق الرئيسي...")
    
    try:
        from main_window import SHPERPMainWindow
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إعداد التطبيق
        app.setLayoutDirection(Qt.RightToLeft)
        font = QFont("Tahoma", 11)
        app.setFont(font)
        
        # إنشاء النافذة الرئيسية
        main_window = SHPERPMainWindow()
        main_window.showMaximized()
        
        print("✅ تم تشغيل النظام بنجاح")
        print("🖥️ النافذة الرئيسية مفتوحة")
        
        return app, main_window
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - نظام تخطيط موارد المؤسسة البحرية")
    print("=" * 60)
    print("🔧 إصدار محسن مع قاعدة بيانات Oracle")
    print("=" * 60)
    
    # عرض شاشة البداية
    app, splash = show_splash_screen()
    
    # معالجة الأحداث
    app.processEvents()
    
    # فحص المتطلبات
    missing = check_requirements()
    
    if missing:
        print(f"\n⚠️ المتطلبات المفقودة: {', '.join(missing)}")
        print("💡 قم بتثبيت المتطلبات باستخدام: pip install -r requirements.txt")
        
        # إغلاق شاشة البداية
        splash.close()
        return
    
    # اختبار قاعدة البيانات
    db_ok = test_database()
    
    if not db_ok:
        print("\n⚠️ مشكلة في قاعدة البيانات")
        print("💡 تأكد من تشغيل خادم Oracle وصحة بيانات الاتصال")
        
        # إغلاق شاشة البداية
        splash.close()
        return
    
    # تحديث شاشة البداية
    splash.setText("SHP ERP\n\nنظام تخطيط موارد المؤسسة\n\nجاري تشغيل النظام...")
    app.processEvents()
    
    # تأخير قصير
    QTimer.singleShot(2000, lambda: launch_and_run(app, splash))
    
    # تشغيل التطبيق
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n👋 تم إغلاق النظام")

def launch_and_run(app, splash):
    """تشغيل النظام بعد التأخير"""
    # إغلاق شاشة البداية
    splash.close()
    
    # تشغيل التطبيق الرئيسي
    app_instance, main_window = launch_main_application()
    
    if main_window:
        print("\n🎉 النظام جاهز للاستخدام!")
        print("📱 يمكنك الآن استخدام شجرة الأنظمة")
        print("🗃️ البيانات محملة من قاعدة بيانات Oracle")
    else:
        print("\n❌ فشل في تشغيل النظام")

if __name__ == "__main__":
    main()
