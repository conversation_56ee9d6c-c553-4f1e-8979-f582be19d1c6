#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Test Systems Tree Widget
اختبار مكون شجرة الأنظمة
"""

import sys
from PySide6.QtWidgets import QApplication
from systems_tree_widget import SystemsTreeWidget

def test_tree_widget():
    """اختبار مكون الشجرة"""
    print("🔄 اختبار مكون شجرة الأنظمة...")
    
    try:
        app = QApplication(sys.argv)
        
        # بيانات تجريبية
        test_data = [
            {
                "SYSTEM_ID": 1, 
                "SYSTEM_CODE": "ERP", 
                "SYSTEM_NAME": "نظام ERP", 
                "PARENT_SYSTEM_ID": None, 
                "SYSTEM_ORDER": 1,
                "FORM_NO": None
            },
            {
                "SYSTEM_ID": 10, 
                "SYSTEM_CODE": "STP", 
                "SYSTEM_NAME": "نظام الاعدادات العامة", 
                "PARENT_SYSTEM_ID": 1, 
                "SYSTEM_ORDER": 10,
                "FORM_NO": 100
            },
            {
                "SYSTEM_ID": 20, 
                "SYSTEM_CODE": "ADM", 
                "SYSTEM_NAME": "نظام ادارة النظام و المستخدمين", 
                "PARENT_SYSTEM_ID": 1, 
                "SYSTEM_ORDER": 20,
                "FORM_NO": 200
            },
            {
                "SYSTEM_ID": 29, 
                "SYSTEM_CODE": "PGLS", 
                "SYSTEM_NAME": "انظمة الحسابات", 
                "PARENT_SYSTEM_ID": 1, 
                "SYSTEM_ORDER": 29,
                "FORM_NO": None
            },
            {
                "SYSTEM_ID": 30, 
                "SYSTEM_CODE": "GLS", 
                "SYSTEM_NAME": "نظام الاستاذ العام", 
                "PARENT_SYSTEM_ID": 29, 
                "SYSTEM_ORDER": 30,
                "FORM_NO": 300
            }
        ]
        
        # إنشاء مكون الشجرة
        widget = SystemsTreeWidget()
        widget.setWindowTitle("اختبار شجرة الأنظمة")
        widget.resize(800, 600)
        
        # تحميل البيانات
        widget.load_systems_data(test_data, {})
        
        # عرض النافذة
        widget.show()
        
        print("✅ تم إنشاء مكون الشجرة بنجاح")
        print("🖥️ النافذة مفتوحة - اضغط Ctrl+C للإغلاق")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكون الشجرة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tree_widget()
