#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحل النهائي
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox

def test_final_fix():
    """اختبار الحل النهائي"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إعداد التطبيق للنصوص العربية
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
        
        print("✅ تم إعداد التطبيق")
        
        # اختبار تحميل البيانات التجريبية
        print("🔄 اختبار تحميل البيانات التجريبية...")
        
        from erp_systems_manager import ERPSystemsManager
        manager = ERPSystemsManager()
        systems_data = manager.get_sample_systems_data()
        
        print(f"✅ تم تحميل {len(systems_data)} نظام تجريبي")
        
        # البحث عن نظام الإعدادات العامة
        general_settings_found = False
        for system in systems_data:
            if system.get('SYSTEM_CODE') == 'GENSETTINGS':
                general_settings_found = True
                print(f"✅ تم العثور على: {system.get('SYSTEM_NAME')}")
                break
        
        if not general_settings_found:
            print("❌ لم يتم العثور على نظام الإعدادات العامة")
            return False
        
        # اختبار النافذة الرئيسية
        print("🔄 اختبار النافذة الرئيسية...")
        
        from main_window import SHPERPMainWindow
        window = SHPERPMainWindow()
        
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من البيانات في النافذة
        if hasattr(window, 'systems_data') and window.systems_data:
            print(f"✅ النافذة تحتوي على {len(window.systems_data)} نظام")
            
            # البحث عن نظام الإعدادات العامة في النافذة
            found_in_window = False
            for system in window.systems_data:
                if system.get('SYSTEM_CODE') == 'GENSETTINGS':
                    found_in_window = True
                    print(f"✅ نظام الإعدادات العامة موجود في النافذة: {system.get('SYSTEM_NAME')}")
                    break
            
            if not found_in_window:
                print("❌ نظام الإعدادات العامة غير موجود في النافذة")
                return False
        else:
            print("❌ النافذة لا تحتوي على بيانات")
            return False
        
        # اختبار فتح النظام
        print("🔄 اختبار فتح نظام الإعدادات العامة...")
        
        # محاكاة بيانات النظام
        system_data = {
            'SYSTEM_CODE': 'GENSETTINGS',
            'SYSTEM_NAME': 'نظام الإعدادات العامة الشامل'
        }
        
        # اختبار المعالج
        try:
            window.handle_system_selection(system_data)
            print("✅ تم اختبار معالج النقر بنجاح")
        except Exception as e:
            print(f"❌ فشل في اختبار المعالج: {e}")
            return False
        
        # عرض النافذة
        window.show()
        print("✅ تم عرض النافذة الرئيسية")
        
        # عرض رسالة نجاح
        QMessageBox.information(
            window,
            "نجح الاختبار",
            "تم إصلاح المشكلة بنجاح!\n\n"
            "• نظام الإعدادات العامة موجود في الشجرة\n"
            "• النقر على النظام يعمل\n"
            "• النافذة تفتح بشكل صحيح\n\n"
            "يمكنك الآن استخدام النظام!"
        )
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار الحل النهائي")
    print("=" * 50)
    
    success = test_final_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 نجح الحل النهائي!")
        print("✅ نظام الإعدادات العامة يعمل الآن")
        print("\n📋 للاستخدام:")
        print("1️⃣ شغل: python main_window.py")
        print("2️⃣ ابحث عن: نظام الإعدادات العامة الشامل")
        print("3️⃣ انقر على النظام")
        print("4️⃣ ستفتح النافذة! ✨")
    else:
        print("❌ فشل الحل!")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
