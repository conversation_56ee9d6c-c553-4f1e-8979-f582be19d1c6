# 🚢 SHP ERP - نظام تخطيط موارد المؤسسة البحرية

## 📋 نظرة عامة

SHP ERP هو نظام تخطيط موارد المؤسسة المصمم خصيصاً للشركات البحرية والشحن، مبني على قاعدة بيانات Oracle مع واجهة مستخدم عربية متقدمة.

## 🎯 الميزات الرئيسية

### 🗃️ **تكامل قاعدة البيانات Oracle**
- اتصال مباشر بقاعدة بيانات Oracle
- تحميل الأنظمة من جدول `S_ERP_SYSTEM`
- دعم المستخدم `SHIP2025`
- معالجة البيانات الهرمية للأنظمة

### 🌐 **دعم اللغة العربية الكامل**
- واجهة RTL (من اليمين لليسار)
- تنسيق النصوص العربية بشكل صحيح
- خط Tahoma المحسن للعربية
- دعم الأحرف المتصلة والتشكيل

### 🎨 **واجهة مستخدم متقدمة**
- تصميم حديث ومتجاوب
- خلفية فنية متدرجة
- شجرة أنظمة تفاعلية
- شريط أدوات محسن
- لوحة تحكم شاملة

### 🔧 **إدارة الأنظمة**
- عرض الأنظمة في شكل شجرة هرمية
- البحث والتصفية المتقدمة
- إعادة تحميل البيانات
- معلومات تفصيلية للأنظمة

## 📁 **هيكل المشروع**

```
E:\SHP_ERP\
├── main_window.py              # الواجهة الرئيسية
├── database_connection.py     # وحدة الاتصال بقاعدة البيانات
├── erp_systems_manager.py     # مدير أنظمة ERP
├── cnx_config.py             # ملف الإعدادات
├── requirements.txt          # متطلبات المشروع
└── README.md                # دليل المشروع
```

## 🛠️ **المتطلبات**

### **Python Packages:**
```bash
pip install -r requirements.txt
```

### **قاعدة البيانات:**
- Oracle Database
- المستخدم: `SHIP2025`
- الجدول: `S_ERP_SYSTEM`
- الخدمة: `localhost:1521/orcl`

## 🚀 **التشغيل**

### **1. تثبيت المتطلبات:**
```bash
cd E:\SHP_ERP
pip install -r requirements.txt
```

### **2. تشغيل التطبيق:**
```bash
python main_window.py
```

## 🗃️ **هيكل جدول S_ERP_SYSTEM**

| العمود | النوع | الوصف |
|--------|-------|--------|
| SYSTEM_ID | VARCHAR2 | معرف النظام |
| SYSTEM_NAME | VARCHAR2 | اسم النظام (إنجليزي) |
| SYSTEM_NAME_AR | VARCHAR2 | اسم النظام (عربي) |
| PARENT_SYSTEM_ID | VARCHAR2 | معرف النظام الأب |
| SYSTEM_LEVEL | NUMBER | مستوى النظام |
| SYSTEM_ORDER | NUMBER | ترتيب النظام |
| IS_ACTIVE | CHAR(1) | حالة النشاط (Y/N) |
| SYSTEM_ICON | VARCHAR2 | أيقونة النظام |
| SYSTEM_URL | VARCHAR2 | رابط النظام |
| SYSTEM_DESCRIPTION | VARCHAR2 | وصف النظام |
| CREATED_DATE | DATE | تاريخ الإنشاء |
| CREATED_BY | VARCHAR2 | منشئ النظام |

## 🔧 **الوظائف الرئيسية**

### **1. الاتصال بقاعدة البيانات:**
- اتصال آمن بـ Oracle
- معالجة الأخطاء
- إعادة الاتصال التلقائي

### **2. إدارة الأنظمة:**
- تحميل الأنظمة الهرمية
- عرض في شكل شجرة
- البحث والتصفية
- معلومات تفصيلية

### **3. واجهة المستخدم:**
- RTL كامل للعربية
- تصميم متجاوب
- شريط أدوات تفاعلي
- لوحة تحكم متقدمة

## 🎨 **التخصيص**

### **الألوان:**
- الأخضر: `#4CAF50` (الأنظمة النشطة)
- الأزرق: `#2196F3` (التحديد والتركيز)
- البرتقالي: `#FF9800` (التحذيرات)
- الأحمر: `#F44336` (الأخطاء)

### **الخطوط:**
- العربية: `Tahoma`
- الإنجليزية: `Arial`
- الأحجام: 11-16px

## 🔍 **استكشاف الأخطاء**

### **مشاكل الاتصال:**
1. تحقق من إعدادات Oracle
2. تأكد من صحة بيانات المستخدم
3. تحقق من حالة الخدمة

### **مشاكل العربية:**
1. تثبيت `arabic-reshaper`
2. تثبيت `python-bidi`
3. تحقق من خط Tahoma

### **مشاكل الواجهة:**
1. تحديث PySide6
2. تحقق من دقة الشاشة
3. إعادة تشغيل التطبيق

## 📞 **الدعم**

للحصول على الدعم أو الإبلاغ عن مشاكل:
- تحقق من ملفات السجل
- راجع رسائل الخطأ
- تأكد من المتطلبات

## 🔄 **التطوير المستقبلي**

- [ ] إضافة المزيد من الأنظمة
- [ ] تحسين الأداء
- [ ] إضافة التقارير
- [ ] تطوير API
- [ ] دعم قواعد بيانات أخرى

---

**🚢 SHP ERP - حلول تخطيط الموارد البحرية المتقدمة**
