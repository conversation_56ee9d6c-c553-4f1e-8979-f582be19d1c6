#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Enhanced Main Window with Oracle Database Integration
النافذة الرئيسية المحسنة لنظام SHP ERP مع تكامل قاعدة بيانات Oracle
"""

import sys
import math
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTreeWidget, QTreeWidgetItem, QFrame,
    QLineEdit, QComboBox, QDateEdit, QToolBar, QSplitter, QMessageBox,
    QInputDialog, QProgressBar, QStatusBar
)
from PySide6.QtCore import Qt, QDate, QRect, QTimer, QThread, Signal
from PySide6.QtGui import (
    QFont, QAction, QPainter, QLinearGradient, QColor,
    QPen, QP<PERSON>terPath, QBrush, QRadialGradient
)

# استيراد مساعد النصوص العربية
from arabic_text_helper import format_arabic_text, setup_arabic_widget

# استيراد وحدات قاعدة البيانات
try:
    from erp_systems_manager import ERPSystemsManager
    from systems_tree_widget import SystemsTreeWidget
    DATABASE_AVAILABLE = True
except ImportError:
    print("⚠️ وحدة قاعدة البيانات غير متوفرة")
    DATABASE_AVAILABLE = False

class DatabaseConnectionThread(QThread):
    """خيط منفصل للاتصال بقاعدة البيانات"""
    connection_result = Signal(bool, str)
    systems_loaded = Signal(list)
    
    def __init__(self, username="ship2025", password="ys123"):
        super().__init__()
        self.username = username
        self.password = password
        
    def run(self):
        """تشغيل عملية الاتصال"""
        try:
            if not DATABASE_AVAILABLE:
                self.connection_result.emit(False, "وحدة قاعدة البيانات غير متوفرة")
                return

            # إنشاء مدير الأنظمة
            self.systems_manager = ERPSystemsManager()

            # محاولة الاتصال مع timeout
            print("🔄 بدء الاتصال بقاعدة البيانات...")

            if self.systems_manager.connect_to_database():
                print("✅ نجح الاتصال")
                self.connection_result.emit(True, "تم الاتصال بنجاح")

                # تحميل بيانات الأنظمة
                print("📋 تحميل بيانات الأنظمة...")
                systems_data = self.systems_manager.load_systems_data()
                print(f"✅ تم تحميل {len(systems_data)} نظام")
                self.systems_loaded.emit(systems_data)

            else:
                print("❌ فشل الاتصال")
                self.connection_result.emit(False, "فشل الاتصال بقاعدة البيانات")

                # تحميل البيانات التجريبية عند فشل الاتصال
                print("🔄 تحميل البيانات التجريبية...")
                systems_data = self.systems_manager.load_systems_data()
                print(f"✅ تم تحميل {len(systems_data)} نظام تجريبي")
                self.systems_loaded.emit(systems_data)

        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            self.connection_result.emit(False, f"خطأ: {str(e)}")

            # تحميل البيانات التجريبية عند حدوث خطأ
            try:
                print("🔄 تحميل البيانات التجريبية بعد الخطأ...")
                if not hasattr(self, 'systems_manager'):
                    self.systems_manager = ERPSystemsManager()
                systems_data = self.systems_manager.load_systems_data()
                print(f"✅ تم تحميل {len(systems_data)} نظام تجريبي")
                self.systems_loaded.emit(systems_data)
            except Exception as fallback_error:
                print(f"❌ فشل في تحميل البيانات التجريبية: {fallback_error}")

class ArtisticBackgroundWidget(QWidget):
    """ويدجت الخلفية الفنية مع الخطوط المنحنية المتدرجة"""
    
    def __init__(self):
        super().__init__()
        self.setAutoFillBackground(True)
        
    def paintEvent(self, event):
        """رسم الخلفية الفنية"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية بيضاء مع تدرج خفيف
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(255, 255, 255))
        gradient.setColorAt(1, QColor(248, 250, 252))
        painter.fillRect(self.rect(), gradient)
        
        # رسم الخطوط المنحنية الفنية
        self.draw_artistic_curves(painter)
        
    def draw_artistic_curves(self, painter):
        """رسم الخطوط المنحنية الملونة بأسلوب فني"""
        width = self.width()
        height = self.height()
        
        if width <= 0 or height <= 0:
            return
            
        # الخطوط الحمراء المتدرجة (أعلى يمين)
        self.draw_flowing_curves(painter, 
                               start_color=QColor(229, 62, 62, 40),
                               start_x=width * 0.7, start_y=0,
                               direction="down_left", num_curves=8)
        
        # الخطوط الزرقاء المتدرجة (وسط)
        self.draw_flowing_curves(painter,
                               start_color=QColor(49, 130, 206, 35),
                               start_x=width * 0.1, start_y=height * 0.4,
                               direction="horizontal", num_curves=6)
        
        # الخطوط الخضراء المتدرجة (أسفل يسار)
        self.draw_flowing_curves(painter,
                               start_color=QColor(56, 161, 105, 30),
                               start_x=0, start_y=height * 0.8,
                               direction="up_right", num_curves=5)
                               
    def draw_flowing_curves(self, painter, start_color, start_x, start_y, direction, num_curves):
        """رسم مجموعة من المنحنيات المتدفقة"""
        
        for i in range(num_curves):
            # تدرج اللون
            alpha_ratio = 1 - (i / num_curves)
            current_color = QColor(start_color)
            current_color.setAlpha(int(start_color.alpha() * alpha_ratio))
            
            # سمك الخط متدرج
            pen_width = max(1, 3 - (i // 3))
            painter.setPen(QPen(current_color, pen_width))
            
            # إنشاء المسار المنحني
            path = QPainterPath()
            
            # حساب نقاط المنحنى حسب الاتجاه
            if direction == "down_left":
                x1 = start_x - (i * 12)
                y1 = start_y + (i * 6)
                x2 = x1 - 150 - (i * 8)
                y2 = y1 + 120 + (i * 10)
                
                # نقاط التحكم للمنحنى
                ctrl1_x = x1 - 60 + math.sin(i * 0.3) * 15
                ctrl1_y = y1 + 30 + math.cos(i * 0.2) * 10
                ctrl2_x = x2 + 40 + math.sin(i * 0.4) * 20
                ctrl2_y = y2 - 60 + math.cos(i * 0.3) * 15
                
            elif direction == "horizontal":
                x1 = start_x + (i * 10)
                y1 = start_y + math.sin(i * 0.5) * 20
                x2 = x1 + 250 + (i * 6)
                y2 = y1 + math.sin(i * 0.3) * 30
                
                ctrl1_x = x1 + 80 + math.cos(i * 0.4) * 20
                ctrl1_y = y1 - 40 + math.sin(i * 0.6) * 15
                ctrl2_x = x2 - 80 + math.cos(i * 0.3) * 25
                ctrl2_y = y2 + 20 + math.sin(i * 0.4) * 15
                
            else:  # up_right
                x1 = start_x + (i * 15)
                y1 = start_y - (i * 8)
                x2 = x1 + 140 + (i * 10)
                y2 = y1 - 100 - (i * 6)
                
                ctrl1_x = x1 + 50 + math.sin(i * 0.4) * 20
                ctrl1_y = y1 - 25 + math.cos(i * 0.5) * 15
                ctrl2_x = x2 - 40 + math.sin(i * 0.3) * 25
                ctrl2_y = y2 + 50 + math.cos(i * 0.4) * 10
            
            # رسم المنحنى
            path.moveTo(x1, y1)
            path.cubicTo(ctrl1_x, ctrl1_y, ctrl2_x, ctrl2_y, x2, y2)
            painter.drawPath(path)

class SHPERPMainWindow(QMainWindow):
    """النافذة الرئيسية المحسنة لنظام SHP ERP"""
    
    def __init__(self):
        super().__init__()
        self.systems_manager = None
        self.systems_data = []
        self.systems_tree_data = {}
        self.db_connection_thread = None
        self.systems_tree_widget = None

        # تحميل البيانات التجريبية فوراً
        self.load_sample_data_immediately()

        self.init_ui()
        self.setup_animations()

        # محاولة الاتصال بقاعدة البيانات
        self.connect_to_database()

    def load_sample_data_immediately(self):
        """تحميل البيانات التجريبية فوراً"""
        try:
            from erp_systems_manager import ERPSystemsManager
            temp_manager = ERPSystemsManager()
            self.systems_data = temp_manager.get_sample_systems_data()
            print(f"✅ تم تحميل {len(self.systems_data)} نظام تجريبي فوراً")

            # بناء شجرة البيانات
            self.systems_tree_data = temp_manager.build_systems_tree()

        except Exception as e:
            print(f"❌ فشل في تحميل البيانات التجريبية: {e}")
            # بيانات احتياطية مباشرة
            self.systems_data = [
                {
                    'SYSTEM_ID': 1,
                    'SYSTEM_CODE': 'ERP',
                    'SYSTEM_NAME': 'نظام ERP الرئيسي',
                    'PARENT_SYSTEM_ID': None,
                    'SYSTEM_ORDER': 1,
                    'INACTIVE': 0,
                    'FORM_NO': None
                },
                {
                    'SYSTEM_ID': 10,
                    'SYSTEM_CODE': 'GENSETTINGS',
                    'SYSTEM_NAME': 'نظام الإعدادات العامة الشامل',
                    'PARENT_SYSTEM_ID': 1,
                    'SYSTEM_ORDER': 10,
                    'INACTIVE': 0,
                    'FORM_NO': 'general_settings_system.py'
                },
                {
                    'SYSTEM_ID': 101,
                    'SYSTEM_CODE': 'APPEARANCE',
                    'SYSTEM_NAME': 'إعدادات المظهر',
                    'PARENT_SYSTEM_ID': 10,
                    'SYSTEM_ORDER': 1,
                    'INACTIVE': 0,
                    'FORM_NO': 'general_settings_system.py'
                },
                {
                    'SYSTEM_ID': 102,
                    'SYSTEM_CODE': 'COMPANY',
                    'SYSTEM_NAME': 'بيانات الشركة',
                    'PARENT_SYSTEM_ID': 10,
                    'SYSTEM_ORDER': 2,
                    'INACTIVE': 0,
                    'FORM_NO': 'general_settings_system.py'
                }
            ]
            self.systems_tree_data = {}
        
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        return format_arabic_text(text)
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("SHP ERP - نظام تخطيط موارد المؤسسة - قاعدة بيانات Oracle")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1024, 768)

        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        # الويدجت المركزي مع تقسيم
        central_widget = QWidget()
        central_widget.setLayoutDirection(Qt.RightToLeft)
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # إنشاء المكونات
        self.create_toolbar()
        
        # الشريط الجانبي الأيمن (لوحة التحكم)
        right_sidebar = self.create_right_sidebar()
        
        # المنطقة المركزية مع الخلفية الفنية
        central_area = self.create_artistic_central_area()
        
        # الشريط الجانبي الأيسر (القوائم الرئيسية)
        self.left_sidebar = self.create_left_sidebar()
        
        # ترتيب المكونات مع RTL - تبديل المواقع
        main_layout.addWidget(self.left_sidebar)      # القوائم الرئيسية (ستظهر على اليمين مع RTL)
        main_layout.addWidget(central_area, 1)   # المنطقة المركزية
        main_layout.addWidget(right_sidebar)     # لوحة التحكم (ستظهر على اليسار مع RTL)
        
        # تطبيق الأنماط
        self.apply_enhanced_styles()
        
        # شريط الحالة مع شريط التقدم
        self.setup_status_bar()

        # تحديث شجرة الأنظمة بالبيانات المحملة
        self.update_systems_tree()
        
    def setup_status_bar(self):
        """إعداد شريط الحالة مع شريط التقدم"""
        # شريط التقدم للاتصال بقاعدة البيانات
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        
        # إضافة شريط التقدم لشريط الحالة
        self.statusBar().addPermanentWidget(self.progress_bar)
        
        # رسالة الحالة الافتراضية
        self.statusBar().showMessage(self.format_arabic_text("جاري تحميل نظام SHP ERP..."))

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات في خيط منفصل"""
        if not DATABASE_AVAILABLE:
            self.statusBar().showMessage(self.format_arabic_text("⚠️ وحدة قاعدة البيانات غير متوفرة"))
            # إنشاء قوائم افتراضية
            self.create_default_menu()
            return

        # عرض شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد

        # إنشاء خيط الاتصال
        self.db_connection_thread = DatabaseConnectionThread()
        self.db_connection_thread.connection_result.connect(self.on_connection_result)
        self.db_connection_thread.systems_loaded.connect(self.on_systems_loaded)
        self.db_connection_thread.start()

    def on_connection_result(self, success, message):
        """معالج نتيجة الاتصال بقاعدة البيانات"""
        self.progress_bar.setVisible(False)

        if success:
            self.statusBar().showMessage(self.format_arabic_text(f"✅ {message}"))
        else:
            self.statusBar().showMessage(self.format_arabic_text(f"❌ {message} - استخدام البيانات التجريبية"))
            # إنشاء قوائم افتراضية في حالة فشل الاتصال
            self.create_default_menu()

            # تحميل البيانات التجريبية إذا لم يتم تحميلها بعد
            if not hasattr(self, 'systems_data') or not self.systems_data:
                try:
                    from erp_systems_manager import ERPSystemsManager
                    temp_manager = ERPSystemsManager()
                    systems_data = temp_manager.get_sample_systems_data()
                    self.on_systems_loaded(systems_data)
                    print(f"✅ تم تحميل {len(systems_data)} نظام تجريبي كبديل")
                except Exception as e:
                    print(f"❌ فشل في تحميل البيانات التجريبية: {e}")

    def on_systems_loaded(self, systems_data):
        """معالج تحميل بيانات الأنظمة"""
        self.systems_data = systems_data

        # بناء شجرة الأنظمة
        if self.systems_manager:
            self.systems_tree_data = self.systems_manager.build_systems_tree()

        # تحديث شجرة القوائم
        self.update_systems_tree()

        # تحديث شريط الحالة
        count = len(systems_data)
        self.statusBar().showMessage(
            self.format_arabic_text(f"✅ تم تحميل {count} نظام من قاعدة البيانات Oracle")
        )

    def create_default_menu(self):
        """إنشاء قوائم افتراضية في حالة عدم توفر قاعدة البيانات"""
        if not hasattr(self, 'menu_tree'):
            return

        self.menu_tree.clear()

        # قوائم افتراضية
        default_systems = [
            {"icon": "📊", "name": "التقارير الإحصائية", "level": 1},
            {"icon": "🏢", "name": "إدارة الشركات", "level": 1},
            {"icon": "📋", "name": "أوامر الشراء", "level": 1},
            {"icon": "📦", "name": "إدارة المخزون", "level": 1},
            {"icon": "📈", "name": "الحسابات المالية", "level": 1},
            {"icon": "💰", "name": "إدارة الأرصدة", "level": 1},
            {"icon": "🚢", "name": "العمليات البحرية", "level": 1},
            {"icon": "📋", "name": "إدارة الموظفين", "level": 1},
            {"icon": "⚙️", "name": "إعدادات النظام", "level": 1},
            {"icon": "📊", "name": "لوحة المعلومات", "level": 1}
        ]

        for system in default_systems:
            formatted_text = self.format_arabic_text(f"{system['icon']} {system['name']}")
            item = QTreeWidgetItem([formatted_text])
            item.setFont(0, QFont("Tahoma", 11))
            self.menu_tree.addTopLevelItem(item)

    def update_systems_tree(self):
        """تحديث شجرة الأنظمة من قاعدة البيانات"""
        # تحديث مكون الشجرة الجديد
        if self.systems_tree_widget and self.systems_data:
            self.systems_tree_widget.load_systems_data(self.systems_data, self.systems_tree_data)

        # تحديث الشجرة القديمة للتوافق مع الإصدارات السابقة
        if hasattr(self, 'menu_tree') and self.systems_data:
            # مسح الشجرة الحالية
            self.menu_tree.clear()
            # بناء الشجرة من البيانات
            self.build_tree_from_database()

    def build_tree_from_database(self):
        """بناء شجرة الأنظمة من بيانات قاعدة البيانات"""
        if not self.systems_data:
            # إضافة عنصر افتراضي في حالة عدم وجود بيانات
            default_item = QTreeWidgetItem([self.format_arabic_text("⚠️ لا توجد أنظمة متاحة")])
            default_item.setFont(0, QFont("Tahoma", 11))
            self.menu_tree.addTopLevelItem(default_item)
            return

        # تجميع الأنظمة حسب المستوى والأب
        systems_by_parent = {}
        root_systems = []

        for system in self.systems_data:
            parent_id = system.get('PARENT_SYSTEM_ID')
            system_id = system.get('SYSTEM_ID')

            if not parent_id or parent_id == '':
                # نظام جذر
                root_systems.append(system)
            else:
                # نظام فرعي
                if parent_id not in systems_by_parent:
                    systems_by_parent[parent_id] = []
                systems_by_parent[parent_id].append(system)

        # ترتيب الأنظمة الجذر
        root_systems.sort(key=lambda x: x.get('SYSTEM_ORDER', 0))

        # إضافة الأنظمة الجذر للشجرة
        for system in root_systems:
            tree_item = self.create_tree_item(system)
            self.menu_tree.addTopLevelItem(tree_item)

            # إضافة الأنظمة الفرعية
            self.add_child_systems(tree_item, system.get('SYSTEM_ID'), systems_by_parent)

    def create_tree_item(self, system_data):
        """إنشاء عنصر شجرة من بيانات النظام"""
        system_id = system_data.get('SYSTEM_ID', '')
        system_name = system_data.get('SYSTEM_NAME', '')
        system_name_ar = system_data.get('SYSTEM_NAME_AR', '')
        system_icon = system_data.get('SYSTEM_ICON', '📋')

        # استخدام الاسم العربي إذا كان متوفراً، وإلا الاسم الإنجليزي
        display_name = system_name_ar if system_name_ar else system_name
        formatted_name = self.format_arabic_text(f"{system_icon} {display_name}")

        # إنشاء عنصر الشجرة
        tree_item = QTreeWidgetItem([formatted_name])
        tree_item.setFont(0, QFont("Tahoma", 11))

        # حفظ بيانات النظام في العنصر
        tree_item.setData(0, Qt.UserRole, system_data)

        return tree_item

    def add_child_systems(self, parent_item, parent_id, systems_by_parent):
        """إضافة الأنظمة الفرعية لعنصر الشجرة"""
        if parent_id not in systems_by_parent:
            return

        child_systems = systems_by_parent[parent_id]
        child_systems.sort(key=lambda x: x.get('SYSTEM_ORDER', 0))

        for child_system in child_systems:
            child_item = self.create_tree_item(child_system)
            parent_item.addChild(child_item)

            # إضافة الأنظمة الفرعية للطفل (recursively)
            child_id = child_system.get('SYSTEM_ID')
            self.add_child_systems(child_item, child_id, systems_by_parent)


    def launch_database_admin(self, system_data=None):
        """تشغيل نظام إدارة قاعدة البيانات"""
        try:
            # تحديد النظام المراد تشغيله بناءً على رمز النظام
            if system_data:
                # دعم كلا من البنية القديمة والجديدة
                sys_code = system_data.get('SYSTEM_CODE', '') or system_data.get('SYS_CODE', '')
                system_name = system_data.get('SYSTEM_NAME', '') or system_data.get('SYS_NAME', '')

                if sys_code == 'DBADM':
                    # النظام الرئيسي
                    self.open_database_admin_main()
                    display_name = 'النظام الرئيسي لإدارة قاعدة البيانات'
                elif sys_code == 'DBCON':
                    # إدارة الاتصالات
                    self.open_connection_manager()
                    display_name = 'إدارة الاتصالات'
                elif sys_code == 'SQLED':
                    # محرر SQL
                    self.open_sql_editor()
                    display_name = 'محرر SQL المتقدم'
                elif sys_code == 'DBBAK':
                    # النسخ الاحتياطي
                    self.open_backup_system()
                    display_name = 'نظام النسخ الاحتياطي'
                elif sys_code == 'DBSTA':
                    # الإحصائيات
                    self.open_database_statistics()
                    display_name = 'إحصائيات قاعدة البيانات'
                elif sys_code == 'DBTBL':
                    # إدارة الجداول
                    self.open_table_manager()
                    display_name = 'إدارة الجداول'
                else:
                    self.open_database_admin_main()
                    display_name = system_name or 'نظام إدارة قاعدة البيانات'
            else:
                self.open_database_admin_main()
                display_name = 'نظام إدارة قاعدة البيانات'

            self.statusBar().showMessage(
                self.format_arabic_text(f"🚀 تم فتح {display_name}")
            )

        except Exception as e:
            self.statusBar().showMessage(
                self.format_arabic_text(f"❌ خطأ في فتح النظام: {str(e)}")
            )

    def open_database_admin_main(self):
        """فتح النافذة الرئيسية لإدارة قاعدة البيانات"""
        try:
            from database_admin_window import DatabaseAdminWindow

            if not hasattr(self, 'db_admin_window') or not self.db_admin_window:
                self.db_admin_window = DatabaseAdminWindow()

            self.db_admin_window.show()
            self.db_admin_window.raise_()
            self.db_admin_window.activateWindow()

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح النافذة الرئيسية: {str(e)}"))

    def open_connection_manager(self):
        """فتح مدير الاتصالات"""
        try:
            from database_connection_dialog import DatabaseConnectionDialog
            from multi_database_manager import MultiDatabaseManager
            from PySide6.QtWidgets import QDialog, QMessageBox

            manager = MultiDatabaseManager()
            dialog = DatabaseConnectionDialog(self, manager=manager)
            dialog.setWindowTitle(self.format_arabic_text("إدارة الاتصالات - SHP ERP"))

            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", self.format_arabic_text("تم حفظ الاتصال بنجاح"))

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح مدير الاتصالات: {str(e)}"))

    def open_sql_editor(self):
        """فتح محرر SQL المتقدم"""
        try:
            from advanced_sql_editor import AdvancedSQLEditor
            from multi_database_manager import MultiDatabaseManager

            if not hasattr(self, 'sql_editor_window') or not self.sql_editor_window:
                manager = MultiDatabaseManager()
                self.sql_editor_window = AdvancedSQLEditor(manager)
                self.sql_editor_window.setWindowTitle(self.format_arabic_text("محرر SQL المتقدم - SHP ERP"))
                self.sql_editor_window.resize(1000, 700)

                try:
                    self.sql_editor_window.load_connections()
                except:
                    pass  # تجاهل أخطاء تحميل الاتصالات

            self.sql_editor_window.show()
            self.sql_editor_window.raise_()
            self.sql_editor_window.activateWindow()

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح محرر SQL: {str(e)}"))

    def open_backup_system(self):
        """فتح نظام النسخ الاحتياطي"""
        try:
            from database_admin_window import DatabaseAdminWindow

            if not hasattr(self, 'db_backup_window') or not self.db_backup_window:
                self.db_backup_window = DatabaseAdminWindow()

            self.db_backup_window.show()
            self.db_backup_window.raise_()
            self.db_backup_window.activateWindow()

            # التبديل إلى تبويب النسخ الاحتياطي
            if hasattr(self.db_backup_window, 'main_tabs'):
                self.db_backup_window.main_tabs.setCurrentIndex(2)  # تبويب النسخ الاحتياطي

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح نظام النسخ الاحتياطي: {str(e)}"))

    def open_database_statistics(self):
        """فتح إحصائيات قاعدة البيانات"""
        try:
            from database_admin_window import DatabaseAdminWindow

            if not hasattr(self, 'db_stats_window') or not self.db_stats_window:
                self.db_stats_window = DatabaseAdminWindow()

            self.db_stats_window.show()
            self.db_stats_window.raise_()
            self.db_stats_window.activateWindow()

            # التبديل إلى تبويب الإحصائيات
            if hasattr(self.db_stats_window, 'main_tabs'):
                self.db_stats_window.main_tabs.setCurrentIndex(3)  # تبويب الإحصائيات

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح إحصائيات قاعدة البيانات: {str(e)}"))

    def open_table_manager(self):
        """فتح مدير الجداول"""
        try:
            from database_admin_window import DatabaseAdminWindow

            if not hasattr(self, 'db_tables_window') or not self.db_tables_window:
                self.db_tables_window = DatabaseAdminWindow()

            self.db_tables_window.show()
            self.db_tables_window.raise_()
            self.db_tables_window.activateWindow()

            # التبديل إلى تبويب إدارة الجداول
            if hasattr(self.db_tables_window, 'main_tabs'):
                self.db_tables_window.main_tabs.setCurrentIndex(1)  # تبويب إدارة الجداول

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح مدير الجداول: {str(e)}"))

    def launch_general_settings(self, system_data=None):
        """تشغيل نظام الإعدادات العامة"""
        try:
            # تحديد النظام المراد تشغيله بناءً على رمز النظام
            if system_data:
                # دعم كلا من البنية القديمة والجديدة
                sys_code = system_data.get('SYSTEM_CODE', '') or system_data.get('SYS_CODE', '')
                system_name = system_data.get('SYSTEM_NAME', '') or system_data.get('SYS_NAME', '')

                if sys_code in ['GENST', 'GENSETTINGS']:
                    # النظام الرئيسي
                    self.open_general_settings_main()
                    display_name = 'نظام الإعدادات العامة الشامل'
                elif sys_code in ['APPR', 'APPEARANCE']:
                    # إعدادات المظهر
                    self.open_general_settings_section('APPEARANCE')
                    display_name = 'إعدادات المظهر'
                elif sys_code in ['COMP', 'COMPANY']:
                    # بيانات الشركة
                    self.open_general_settings_section('COMPANY')
                    display_name = 'بيانات الشركة'
                elif sys_code in ['CURR', 'CURRENCIES']:
                    # إعدادات العملات
                    self.open_general_settings_section('CURRENCIES')
                    display_name = 'إعدادات العملات'
                elif sys_code in ['FISC', 'FISCALYEAR']:
                    # السنة المالية
                    self.open_general_settings_section('FISCALYEAR')
                    display_name = 'السنة المالية'
                elif sys_code in ['LANG', 'LANGUAGE']:
                    # إعدادات اللغة
                    self.open_general_settings_section('LANGUAGE')
                    display_name = 'إعدادات اللغة'
                elif sys_code in ['SECR', 'SECURITY']:
                    # إعدادات الأمان
                    self.open_general_settings_section('SECURITY')
                    display_name = 'إعدادات الأمان'
                elif sys_code in ['MAIL', 'EMAIL']:
                    # إعدادات البريد
                    self.open_general_settings_section('EMAIL')
                    display_name = 'إعدادات البريد الإلكتروني'
                elif sys_code in ['PRNT', 'PRINTING']:
                    # إعدادات الطباعة
                    self.open_general_settings_section('PRINTING')
                    display_name = 'إعدادات الطباعة'
                elif sys_code in ['REPT', 'REPORTS']:
                    # إعدادات التقارير
                    self.open_general_settings_section('REPORTS')
                    display_name = 'إعدادات التقارير'
                elif sys_code in ['BKUP', 'BACKUPSETTINGS']:
                    # إعدادات النسخ الاحتياطي
                    self.open_general_settings_section('BACKUPSETTINGS')
                    display_name = 'إعدادات النسخ الاحتياطي'
                elif sys_code in ['SYST', 'SYSTEMSETTINGS']:
                    # إعدادات النظام
                    self.open_general_settings_section('SYSTEMSETTINGS')
                    display_name = 'إعدادات النظام'
                elif sys_code in ['NOTF', 'NOTIFICATIONS']:
                    # إعدادات الإشعارات
                    self.open_general_settings_section('NOTIFICATIONS')
                    display_name = 'إعدادات الإشعارات'
                else:
                    self.open_general_settings_main()
                    display_name = system_name or 'نظام الإعدادات العامة'
            else:
                self.open_general_settings_main()
                display_name = 'نظام الإعدادات العامة'

            self.statusBar().showMessage(
                self.format_arabic_text(f"🚀 تم فتح {display_name}")
            )

        except Exception as e:
            self.statusBar().showMessage(
                self.format_arabic_text(f"❌ خطأ في فتح نظام الإعدادات العامة: {str(e)}")
            )

    def open_general_settings_main(self):
        """فتح النافذة الرئيسية لنظام الإعدادات العامة"""
        try:
            from PySide6.QtWidgets import QMessageBox

            # عرض رسالة تأكيد أولاً
            reply = QMessageBox.question(
                self,
                self.format_arabic_text("تأكيد"),
                self.format_arabic_text("هل تريد فتح نظام الإعدادات العامة؟"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            from general_settings_system import GeneralSettingsSystem
            from PySide6.QtCore import Qt

            # إنشاء نافذة جديدة في كل مرة لضمان الظهور
            self.general_settings_window = GeneralSettingsSystem()

            # تعيين النافذة لتكون في المقدمة
            self.general_settings_window.setWindowFlags(
                self.general_settings_window.windowFlags() | Qt.WindowStaysOnTopHint
            )

            # تعيين عنوان واضح
            self.general_settings_window.setWindowTitle("🚀 SHP ERP - نظام الإعدادات العامة الشامل")

            # عرض النافذة
            self.general_settings_window.show()
            self.general_settings_window.raise_()
            self.general_settings_window.activateWindow()

            # إزالة خاصية البقاء في المقدمة بعد ثانية واحدة
            from PySide6.QtCore import QTimer
            def remove_on_top():
                if hasattr(self, 'general_settings_window') and self.general_settings_window:
                    self.general_settings_window.setWindowFlags(
                        self.general_settings_window.windowFlags() & ~Qt.WindowStaysOnTopHint
                    )
                    self.general_settings_window.show()

            QTimer.singleShot(1000, remove_on_top)

            print("✅ تم فتح نافذة الإعدادات العامة")

            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                self.format_arabic_text("نجح"),
                self.format_arabic_text("تم فتح نظام الإعدادات العامة بنجاح!\n\nيجب أن ترى النافذة في المقدمة الآن.")
            )

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح نظام الإعدادات العامة: {str(e)}"))
            print(f"❌ خطأ في فتح الإعدادات العامة: {e}")

    def open_general_settings_section(self, section_code):
        """فتح قسم محدد في نظام الإعدادات العامة"""
        try:
            from general_settings_system import GeneralSettingsSystem
            from PySide6.QtCore import Qt

            # إنشاء نافذة جديدة في كل مرة لضمان الظهور
            self.general_settings_window = GeneralSettingsSystem()

            # فتح القسم المحدد
            self.general_settings_window.open_specific_section(section_code)

            # تعيين النافذة لتكون في المقدمة
            self.general_settings_window.setWindowFlags(
                self.general_settings_window.windowFlags() | Qt.WindowStaysOnTopHint
            )

            # عرض النافذة
            self.general_settings_window.show()
            self.general_settings_window.raise_()
            self.general_settings_window.activateWindow()

            # إزالة خاصية البقاء في المقدمة بعد ثانية واحدة
            from PySide6.QtCore import QTimer
            def remove_on_top():
                if hasattr(self, 'general_settings_window') and self.general_settings_window:
                    self.general_settings_window.setWindowFlags(
                        self.general_settings_window.windowFlags() & ~Qt.WindowStaysOnTopHint
                    )
                    self.general_settings_window.show()

            QTimer.singleShot(1000, remove_on_top)

            print(f"✅ تم فتح قسم الإعدادات: {section_code}")

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح قسم الإعدادات: {str(e)}"))
            print(f"❌ خطأ في فتح قسم الإعدادات: {e}")

    def launch_advanced_settings_system(self, system_data=None):
        """تشغيل نظام الإعدادات الشامل والمتقدم (STP)"""
        try:
            from PySide6.QtWidgets import QMessageBox

            # عرض رسالة تأكيد
            reply = QMessageBox.question(
                self,
                self.format_arabic_text("تأكيد"),
                self.format_arabic_text("هل تريد فتح نظام الإعدادات الشامل والمتقدم؟\n\nسيتم فتح النظام في وضع ملء الشاشة"),
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            from advanced_settings_system import AdvancedSettingsSystem
            from PySide6.QtCore import Qt

            # إنشاء نافذة جديدة في كل مرة لضمان الظهور
            self.advanced_settings_window = AdvancedSettingsSystem()

            # تعيين النافذة لتكون في المقدمة
            self.advanced_settings_window.setWindowFlags(
                self.advanced_settings_window.windowFlags() | Qt.WindowStaysOnTopHint
            )

            # تعيين عنوان واضح
            self.advanced_settings_window.setWindowTitle("🚢 SHP ERP - نظام الإعدادات الشامل والمتقدم (STP)")

            # عرض النافذة في وضع ملء الشاشة
            self.advanced_settings_window.showMaximized()
            self.advanced_settings_window.raise_()
            self.advanced_settings_window.activateWindow()

            # إزالة خاصية البقاء في المقدمة بعد ثانية واحدة
            from PySide6.QtCore import QTimer
            def remove_on_top():
                if hasattr(self, 'advanced_settings_window') and self.advanced_settings_window:
                    self.advanced_settings_window.setWindowFlags(
                        self.advanced_settings_window.windowFlags() & ~Qt.WindowStaysOnTopHint
                    )
                    self.advanced_settings_window.show()

            QTimer.singleShot(1000, remove_on_top)

            print("✅ تم فتح نظام الإعدادات الشامل والمتقدم")

            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                self.format_arabic_text("نجح"),
                self.format_arabic_text("تم فتح نظام الإعدادات الشامل والمتقدم بنجاح!\n\nالنظام يعمل الآن في وضع ملء الشاشة مع جميع الميزات المتقدمة.")
            )

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(f"فشل في فتح نظام الإعدادات الشامل والمتقدم: {str(e)}"))
            print(f"❌ خطأ في فتح نظام الإعدادات الشامل والمتقدم: {e}")

    def on_menu_tree_selected(self, item, column):
        """معالج اختيار نظام من الشجرة القديمة"""
        if not item:
            return

        # الحصول على بيانات النظام
        system_data = item.data(0, Qt.UserRole)
        if not system_data:
            return

        # استدعاء المعالج الموحد
        self.handle_system_selection(system_data)

    def create_toolbar(self):
        """إنشاء شريط الأدوات المحسن"""
        toolbar = self.addToolBar(self.format_arabic_text("الأدوات الرئيسية"))
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        toolbar.setLayoutDirection(Qt.RightToLeft)

        # أدوات قاعدة البيانات
        db_tools = [
            ("🔄", "إعادة تحميل", self.reload_systems),
            ("🔌", "إعادة الاتصال", self.reconnect_database),
            ("ℹ️", "معلومات قاعدة البيانات", self.show_db_info),
        ]

        # الأدوات العامة
        general_tools = [
            ("⚙️", "الإعدادات", None),
            ("🖨️", "طباعة", None),
            ("💾", "حفظ", None),
            ("⭐", "المفضلة", None),
            ("🔧", "الأدوات", None),
            ("📧", "البريد الإلكتروني", None)
        ]

        # إضافة أدوات قاعدة البيانات
        for icon, tooltip, callback in db_tools:
            formatted_text = self.format_arabic_text(f"{icon} {tooltip}")
            action = QAction(formatted_text, self)
            action.setToolTip(self.format_arabic_text(tooltip))
            if callback:
                action.triggered.connect(callback)
            toolbar.addAction(action)

        # فاصل
        toolbar.addSeparator()

        # إضافة الأدوات العامة
        for icon, tooltip, callback in general_tools:
            formatted_text = self.format_arabic_text(f"{icon} {tooltip}")
            action = QAction(formatted_text, self)
            action.setToolTip(self.format_arabic_text(tooltip))
            if callback:
                action.triggered.connect(callback)
            toolbar.addAction(action)

    def reload_systems(self):
        """إعادة تحميل الأنظمة من قاعدة البيانات"""
        if self.db_connection_thread and self.db_connection_thread.isRunning():
            return

        self.statusBar().showMessage(self.format_arabic_text("🔄 جاري إعادة تحميل الأنظمة..."))
        self.connect_to_database()

    def reconnect_database(self):
        """إعادة الاتصال بقاعدة البيانات"""
        if self.db_connection_thread and self.db_connection_thread.isRunning():
            return

        # طلب كلمة المرور من المستخدم
        password, ok = QInputDialog.getText(
            self,
            self.format_arabic_text("كلمة المرور"),
            self.format_arabic_text("أدخل كلمة مرور المستخدم SHIP2025:"),
            QLineEdit.Password
        )

        if ok:
            self.statusBar().showMessage(self.format_arabic_text("🔄 جاري إعادة الاتصال..."))
            self.db_connection_thread = DatabaseConnectionThread("SHIP2025", password)
            self.db_connection_thread.connection_result.connect(self.on_connection_result)
            self.db_connection_thread.systems_loaded.connect(self.on_systems_loaded)
            self.db_connection_thread.start()

    def show_db_info(self):
        """عرض معلومات قاعدة البيانات"""
        if not self.systems_data:
            QMessageBox.information(
                self,
                self.format_arabic_text("معلومات قاعدة البيانات"),
                self.format_arabic_text("لا توجد بيانات متاحة. يرجى الاتصال بقاعدة البيانات أولاً.")
            )
            return

        # إحصائيات الأنظمة
        total_systems = len(self.systems_data)
        levels = set(system.get('SYSTEM_LEVEL', 0) for system in self.systems_data)
        max_level = max(levels) if levels else 0

        # الأنظمة الجذر
        root_systems = [s for s in self.systems_data if not s.get('PARENT_SYSTEM_ID')]

        info_text = self.format_arabic_text(f"""
معلومات قاعدة البيانات Oracle:

📊 إجمالي الأنظمة: {total_systems}
🌳 الأنظمة الجذر: {len(root_systems)}
📈 أقصى مستوى: {max_level}
🔗 المستخدم: SHIP2025
🗃️ الجدول: S_ERP_SYSTEM
        """)

        QMessageBox.information(
            self,
            self.format_arabic_text("معلومات قاعدة البيانات"),
            info_text
        )

    def create_left_sidebar(self):
        """إنشاء الشريط الجانبي الأيسر (القوائم الرئيسية)"""
        sidebar = QFrame()
        sidebar.setFixedWidth(400)  # زيادة العرض من 280 إلى 400 لإظهار الأنظمة الفرعية بوضوح
        sidebar.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        sidebar.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 10, 10, 10)

        # عنوان القسم
        title = QLabel(self.format_arabic_text("🗃️ الأنظمة الرئيسية - Oracle"))
        title.setFont(QFont("Tahoma", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                padding: 12px;
                border-radius: 8px;
                color: #2E7D32;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)

        # شجرة القوائم المحسنة
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setRootIsDecorated(True)
        self.menu_tree.setAlternatingRowColors(True)
        self.menu_tree.setLayoutDirection(Qt.RightToLeft)

        # تحسين عرض الأنظمة الفرعية
        self.menu_tree.setIndentation(25)  # زيادة المسافة البادئة للأنظمة الفرعية
        self.menu_tree.setExpandsOnDoubleClick(True)
        self.menu_tree.setAnimated(True)  # إضافة تأثيرات الحركة

        # ربط إشارة اختيار العنصر
        self.menu_tree.itemClicked.connect(self.on_menu_tree_selected)

        # إضافة رسالة تحميل مؤقتة
        loading_item = QTreeWidgetItem([self.format_arabic_text("🔄 جاري تحميل الأنظمة من قاعدة البيانات...")])
        loading_item.setFont(0, QFont("Tahoma", 11))
        self.menu_tree.addTopLevelItem(loading_item)

        layout.addWidget(self.menu_tree)
        return sidebar

    def create_artistic_central_area(self):
        """إنشاء المنطقة المركزية مع الخلفية الفنية"""
        # استخدام الويدجت الفني كخلفية
        central_area = ArtisticBackgroundWidget()
        central_area.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(central_area)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        # إنشاء مكون شجرة الأنظمة
        if DATABASE_AVAILABLE:
            self.systems_tree_widget = SystemsTreeWidget()
            self.systems_tree_widget.system_selected.connect(self.on_system_selected)
            self.systems_tree_widget.system_activated.connect(self.on_system_activated)
            layout.addWidget(self.systems_tree_widget, 1)  # يأخذ معظم المساحة

        # إضافة مساحة للشعار والنصوص
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setContentsMargins(20, 20, 20, 20)

        # شعار SHP ERP مع تأثيرات (مصغر)
        logo_label = QLabel("SHP ERP")
        logo_font = QFont("Arial", 24, QFont.Bold)
        logo_label.setFont(logo_font)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1B5E20, stop:0.3 #2E7D32, stop:0.7 #4CAF50, stop:1 #1B5E20);
                background: transparent;
                margin: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)
        header_layout.addWidget(logo_label)

        # النص التوضيحي الإنجليزي (مصغر)
        subtitle = QLabel("Ship Enterprise Resource Planning")
        subtitle.setFont(QFont("Arial", 12, QFont.Normal))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: #555555;
                background: transparent;
                margin: 5px;
                font-style: italic;
            }
        """)
        header_layout.addWidget(subtitle)

        # النص العربي (مصغر)
        arabic_text = QLabel(self.format_arabic_text("نظام تخطيط موارد المؤسسة البحرية"))
        arabic_text.setFont(QFont("Tahoma", 11, QFont.Normal))
        arabic_text.setAlignment(Qt.AlignCenter)
        arabic_text.setStyleSheet("""
            QLabel {
                color: #777777;
                background: transparent;
                margin: 5px;
            }
        """)
        header_layout.addWidget(arabic_text)

        # إضافة العنوان في الأعلى
        layout.addLayout(header_layout)

        # معلومات قاعدة البيانات
        db_info = QLabel(self.format_arabic_text("🗃️ متصل بقاعدة بيانات Oracle"))
        db_info.setFont(QFont("Tahoma", 12, QFont.Normal))
        db_info.setAlignment(Qt.AlignCenter)
        db_info.setStyleSheet("""
            QLabel {
                color: #2E7D32;
                background: rgba(232, 245, 232, 0.8);
                padding: 10px;
                border-radius: 6px;
                margin: 10px;
            }
        """)
        layout.addWidget(db_info)

        # مساحة مرنة
        layout.addStretch()

        return central_area

    def on_system_selected(self, system_data):
        """معالج اختيار نظام من الشجرة الجديدة"""
        self.handle_system_selection(system_data)

    def handle_system_selection(self, system_data):
        """معالج موحد لاختيار الأنظمة"""
        # دعم كلا من البنية القديمة والجديدة
        system_name = system_data.get('SYSTEM_NAME', '') or system_data.get('SYS_NAME', '')
        system_code = system_data.get('SYSTEM_CODE', '') or system_data.get('SYS_CODE', '')

        # معالجة خاصة لأنظمة قاعدة البيانات - تشغيل مباشر بالنقر المفرد
        if system_code in ['DBADM', 'DBCON', 'SQLED', 'DBBAK', 'DBSTA', 'DBTBL']:
            self.launch_database_admin(system_data)
            return

        # معالجة خاصة للنظام رقم 10 (STP) - نظام الإعدادات الشامل والمتقدم
        if system_code == 'STP':
            self.launch_advanced_settings_system(system_data)
            return

        # معالجة خاصة لأنظمة الإعدادات العامة - تشغيل مباشر بالنقر المفرد
        if system_code in ['GENST', 'GENSETTINGS', 'APPR', 'APPEARANCE', 'COMP', 'COMPANY',
                          'CURR', 'CURRENCIES', 'FISC', 'FISCALYEAR', 'LANG', 'LANGUAGE',
                          'SECR', 'SECURITY', 'MAIL', 'EMAIL', 'PRNT', 'PRINTING',
                          'REPT', 'REPORTS', 'BKUP', 'BACKUPSETTINGS', 'SYST', 'SYSTEMSETTINGS',
                          'NOTF', 'NOTIFICATIONS']:
            self.launch_general_settings(system_data)
            return

        # تحديث شريط الحالة للأنظمة الأخرى
        status_text = self.format_arabic_text(f"تم اختيار النظام: {system_name} [{system_code}]")
        self.statusBar().showMessage(status_text)

    def on_system_activated(self, system_data):
        """معالج تفعيل نظام من الشجرة (double click)"""
        # دعم كلا من البنية القديمة والجديدة
        system_name = system_data.get('SYSTEM_NAME', '') or system_data.get('SYS_NAME', '')
        system_code = system_data.get('SYSTEM_CODE', '') or system_data.get('SYS_CODE', '')
        form_no = system_data.get('FORM_NO')

        # معالجة خاصة لأنظمة قاعدة البيانات
        if system_code in ['DBADM', 'DBCON', 'SQLED', 'DBBAK', 'DBSTA', 'DBTBL']:
            self.launch_database_admin(system_data)
            return

        # إظهار رسالة تفعيل النظام للأنظمة الأخرى
        from PySide6.QtWidgets import QMessageBox

        msg = QMessageBox()
        msg.setWindowTitle(self.format_arabic_text("تفعيل النظام"))
        msg.setText(self.format_arabic_text(f"سيتم تفعيل النظام: {system_name}"))
        msg.setInformativeText(self.format_arabic_text(f"رمز النظام: {system_code}\nرقم الشاشة: {form_no or 'غير محدد'}"))
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setLayoutDirection(Qt.RightToLeft)

        msg.exec()

        # تحديث شريط الحالة
        status_text = self.format_arabic_text(f"تم تفعيل النظام: {system_name}")
        self.statusBar().showMessage(status_text)

    def create_right_sidebar(self):
        """إنشاء الشريط الجانبي الأيمن (لوحة التحكم)"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        sidebar.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 10, 10, 10)

        # عنوان قسم التحكم
        control_title = QLabel(self.format_arabic_text("🔧 لوحة التحكم"))
        control_title.setFont(QFont("Tahoma", 14, QFont.Bold))
        control_title.setAlignment(Qt.AlignCenter)
        control_title.setStyleSheet("""
            QLabel {
                background-color: #FFF3E0;
                padding: 12px;
                border-radius: 8px;
                color: #F57C00;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(control_title)

        # عناصر التحكم المحسنة
        controls = [
            ("🔍 البحث في الأنظمة:", QLineEdit()),
            ("📅 التاريخ:", QDateEdit(QDate.currentDate())),
            ("📂 نوع النظام:", QComboBox()),
            ("📊 حالة النظام:", QComboBox()),
        ]

        for label_text, widget in controls:
            label = QLabel(self.format_arabic_text(label_text))
            label.setFont(QFont("Tahoma", 11, QFont.Bold))
            label.setStyleSheet("color: #444444; margin-top: 10px;")
            layout.addWidget(label)

            # إعداد RTL للعناصر
            widget.setLayoutDirection(Qt.RightToLeft)

            if isinstance(widget, QComboBox):
                if "نوع النظام" in label_text:
                    items = ["جميع الأنواع", "نظام رئيسي", "نظام فرعي", "تقارير", "إعدادات"]
                    for item in items:
                        widget.addItem(self.format_arabic_text(item))
                elif "حالة النظام" in label_text:
                    items = ["جميع الحالات", "نشط", "غير نشط", "قيد التطوير", "مكتمل"]
                    for item in items:
                        widget.addItem(self.format_arabic_text(item))

            elif isinstance(widget, QLineEdit):
                widget.setPlaceholderText(self.format_arabic_text("ابحث في الأنظمة..."))
                # ربط البحث
                widget.textChanged.connect(self.filter_systems)

            widget.setStyleSheet("""
                QLineEdit, QComboBox, QDateEdit {
                    padding: 8px;
                    border: 2px solid #E0E0E0;
                    border-radius: 6px;
                    background-color: white;
                    margin-bottom: 5px;
                    text-align: right;
                    direction: rtl;
                }
                QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                    border-color: #2196F3;
                }
            """)
            layout.addWidget(widget)

        # مساحة مرنة
        layout.addStretch()

        # أزرار العمليات المحسنة
        buttons_data = [
            ("🔄", "إعادة تحميل", "#4CAF50", self.reload_systems),
            ("🔍", "بحث متقدم", "#2196F3", None),
            ("📤", "تصدير البيانات", "#FF9800", None),
            ("🖨️", "طباعة التقرير", "#9C27B0", None)
        ]

        for icon, text, color, callback in buttons_data:
            btn = QPushButton(self.format_arabic_text(f"{icon} {text}"))
            btn.setFont(QFont("Tahoma", 11, QFont.Bold))
            btn.setMinimumHeight(40)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 8px;
                    margin: 4px;
                }}
                QPushButton:hover {{ background-color: {color}DD; }}
                QPushButton:pressed {{ background-color: {color}BB; }}
            """)

            if callback:
                btn.clicked.connect(callback)

            layout.addWidget(btn)

        return sidebar

    def filter_systems(self, search_text):
        """تصفية الأنظمة حسب النص المدخل"""
        if not hasattr(self, 'menu_tree'):
            return

        # إخفاء/إظهار العناصر حسب النص
        for i in range(self.menu_tree.topLevelItemCount()):
            item = self.menu_tree.topLevelItem(i)
            self.filter_tree_item(item, search_text.lower())

    def filter_tree_item(self, item, search_text):
        """تصفية عنصر الشجرة وأطفاله"""
        if not search_text:
            item.setHidden(False)
            for i in range(item.childCount()):
                self.filter_tree_item(item.child(i), search_text)
            return

        # البحث في النص
        item_text = item.text(0).lower()
        matches = search_text in item_text

        # البحث في الأطفال
        child_matches = False
        for i in range(item.childCount()):
            child_item = item.child(i)
            if self.filter_tree_item(child_item, search_text):
                child_matches = True

        # إظهار العنصر إذا كان يطابق أو له أطفال تطابق
        show_item = matches or child_matches
        item.setHidden(not show_item)

        return show_item

    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        # يمكن إضافة رسوم متحركة هنا لاحقاً
        pass

    def apply_enhanced_styles(self):
        """تطبيق الأنماط المحسنة"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #FAFAFA;
                font-family: "Tahoma";
                font-size: 11px;
            }}

            QFrame {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                margin: 2px;
            }}

            QTreeWidget {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                font-size: 12px;
                padding: 8px;
                outline: none;
                font-family: "Tahoma";
            }}

            QTreeWidget::item {{
                padding: 12px 10px;
                border-bottom: 1px solid #F5F5F5;
                border-radius: 4px;
                margin: 1px;
                text-align: right;
                direction: rtl;
                font-size: 13px;
                min-height: 25px;
            }}

            QTreeWidget::item:hover {{
                background-color: #E8F5E8;
                color: #2E7D32;
                font-weight: bold;
                border-left: 3px solid #4CAF50;
            }}

            QTreeWidget::item:selected {{
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                border-left: 4px solid #2E7D32;
            }}

            QTreeWidget::item:has-children {{
                font-weight: bold;
                color: #1976D2;
            }}

            QTreeWidget::branch:has-children {{
                border-image: none;
                image: url(none);
            }}

            QToolBar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E8F5E8, stop:1 #C8E6C9);
                border: none;
                spacing: 10px;
                padding: 10px;
                font-size: 10px;
                font-family: "Tahoma";
            }}

            QToolBar QToolButton {{
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
                font-family: "Tahoma";
            }}

            QToolBar QToolButton:hover {{
                background-color: rgba(76, 175, 80, 0.15);
                border-color: #4CAF50;
            }}

            QStatusBar {{
                background-color: #F5F5F5;
                border-top: 1px solid #E0E0E0;
                color: #666666;
                font-weight: bold;
                font-family: "Tahoma";
                padding: 5px;
            }}

            QLabel {{
                font-family: "Tahoma";
            }}

            QPushButton {{
                font-family: "Tahoma";
            }}

            QLineEdit {{
                font-family: "Tahoma";
                text-align: right;
            }}

            QComboBox {{
                font-family: "Tahoma";
                text-align: right;
            }}

            QDateEdit {{
                font-family: "Tahoma";
                text-align: right;
            }}

            QProgressBar {{
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                text-align: center;
                font-family: "Tahoma";
            }}

            QProgressBar::chunk {{
                background-color: #4CAF50;
                border-radius: 3px;
            }}
        """)

    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        # إيقاف خيط قاعدة البيانات إذا كان يعمل
        if self.db_connection_thread and self.db_connection_thread.isRunning():
            self.db_connection_thread.quit()
            self.db_connection_thread.wait()

        # قطع الاتصال بقاعدة البيانات
        if self.systems_manager:
            self.systems_manager.disconnect()

        event.accept()

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)

    # إعداد التطبيق للنصوص العربية
    from arabic_text_helper import setup_arabic_application
    setup_arabic_application(app)

    # إنشاء النافذة الرئيسية
    window = SHPERPMainWindow()
    window.showMaximized()  # فتح في وضع ملء الشاشة

    print("🚀 تم تشغيل نظام SHP ERP بنجاح!")
    print("📱 النافذة مفتوحة في وضع ملء الشاشة")
    print("🌐 دعم كامل للغة العربية مُفعل")
    print("🗃️ متكامل مع قاعدة بيانات Oracle")

    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
