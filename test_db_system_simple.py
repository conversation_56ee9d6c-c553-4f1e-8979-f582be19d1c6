#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Simple Database System Test
اختبار مبسط لنظام قاعدة البيانات
"""

import sys
import time

def test_oracle_connection():
    """اختبار الاتصال بقاعدة البيانات Oracle"""
    print("🔄 اختبار الاتصال بقاعدة البيانات Oracle...")
    
    try:
        from oracle_db_manager import get_db_manager
        
        db_manager = get_db_manager()
        
        # اختبار الاتصال البسيط
        success, message = db_manager.test_connection()
        
        if success:
            print(f"✅ نجح الاتصال: {message}")
            
            # اختبار مجموعة الاتصالات
            if db_manager.initialize_connection_pool():
                print("✅ تم إنشاء مجموعة الاتصالات")
                
                # اختبار استعلام بسيط
                success, result = db_manager.execute_query_safe(
                    "SELECT COUNT(*) as TOTAL FROM S_ERP_SYSTEM"
                )
                
                if success and result:
                    total = result[0]['TOTAL']
                    print(f"✅ إجمالي الأنظمة: {total}")
                    
                    # اختبار استعلام الأنظمة النشطة
                    success, active_systems = db_manager.get_systems_data()
                    if success:
                        print(f"✅ الأنظمة النشطة: {len(active_systems)}")
                        
                        # عرض عينة من الأنظمة
                        print("\n📋 عينة من الأنظمة:")
                        for i, system in enumerate(active_systems[:5]):
                            print(f"  {i+1}. {system['SYSTEM_NAME']} [{system['SYSTEM_CODE']}]")
                        
                        if len(active_systems) > 5:
                            print(f"  ... و {len(active_systems) - 5} نظام آخر")
                    
                # إغلاق الاتصالات
                db_manager.close_connections()
                print("✅ تم إغلاق الاتصالات")
                
                return True
            else:
                print("❌ فشل في إنشاء مجموعة الاتصالات")
                return False
        else:
            print(f"❌ فشل الاتصال: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_backup_manager():
    """اختبار مدير النسخ الاحتياطي"""
    print("\n🔄 اختبار مدير النسخ الاحتياطي...")
    
    try:
        from advanced_db_features import BackupManager
        
        backup_manager = BackupManager()
        
        # فحص مجلد النسخ الاحتياطي
        backup_manager.ensure_backup_directory()
        print("✅ تم التأكد من مجلد النسخ الاحتياطي")
        
        # قائمة النسخ الاحتياطية الموجودة
        backups = backup_manager.list_backups()
        print(f"📋 عدد النسخ الاحتياطية الموجودة: {len(backups)}")
        
        if backups:
            print("📁 النسخ الاحتياطية المتاحة:")
            for backup in backups[:3]:  # أول 3 نسخ
                print(f"  • {backup['filename']} - {backup['table_name']} ({backup['backup_type']})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النسخ الاحتياطي: {e}")
        return False

def test_performance_monitor():
    """اختبار مراقب الأداء"""
    print("\n🔄 اختبار مراقب الأداء...")
    
    try:
        from advanced_db_features import PerformanceMonitor
        
        monitor = PerformanceMonitor()
        
        # جمع بيانات الأداء مرة واحدة
        print("📊 جمع بيانات الأداء...")
        monitor.collect_performance_data()
        
        # فحص التاريخ
        if monitor.performance_history:
            latest = monitor.performance_history[-1]
            print(f"✅ آخر قياس: {latest['timestamp']}")
            
            connection_status = latest['connection_status']
            print(f"📡 حالة الاتصال: {'متصل' if connection_status['connected'] else 'غير متصل'}")
            print(f"⏱️ زمن الاستجابة: {connection_status['response_time_ms']:.2f} ms")
            
            system_stats = latest['system_stats']
            print(f"📈 إجمالي الأنظمة: {system_stats.get('total_systems', 0)}")
            print(f"📈 الأنظمة النشطة: {system_stats.get('active_systems', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مراقب الأداء: {e}")
        return False

def test_data_validator():
    """اختبار مدقق البيانات"""
    print("\n🔄 اختبار مدقق البيانات...")
    
    try:
        from advanced_db_features import DataValidator
        from oracle_db_manager import get_db_manager
        
        # التأكد من وجود اتصال
        db_manager = get_db_manager()
        success, message = db_manager.test_connection()
        
        if not success:
            print(f"❌ لا يوجد اتصال بقاعدة البيانات: {message}")
            return False
        
        # تهيئة مجموعة الاتصالات
        if not db_manager.initialize_connection_pool():
            print("❌ فشل في تهيئة مجموعة الاتصالات")
            return False
        
        validator = DataValidator()
        
        # تشغيل التحقق مباشرة (بدون إشارات Qt)
        print("🔍 بدء التحقق من البيانات...")
        
        # الحصول على البيانات للتحقق
        success, systems = db_manager.execute_query_safe(
            "SELECT * FROM S_ERP_SYSTEM ORDER BY SYS_NO"
        )
        
        if not success:
            print(f"❌ فشل في الحصول على البيانات: {systems}")
            db_manager.close_connections()
            return False
        
        # تشغيل التحقق يدوياً
        validation_results = {
            'total_systems': len(systems),
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # فحص البيانات المفقودة
        for system in systems:
            sys_no = system.get('SYS_NO')
            
            if not system.get('SYS_CODE'):
                validation_results['issues'].append(f"النظام {sys_no}: رمز النظام مفقود")
            
            if not system.get('SYS_NAME'):
                validation_results['warnings'].append(f"النظام {sys_no}: اسم النظام مفقود")
        
        # فحص العلاقات الهرمية
        system_ids = {system['SYS_NO'] for system in systems}
        
        for system in systems:
            sys_no = system.get('SYS_NO')
            parent_id = system.get('SYS_PARNT')
            
            if parent_id and parent_id != 0:
                if parent_id not in system_ids:
                    validation_results['issues'].append(f"النظام {sys_no}: النظام الأب {parent_id} غير موجود")
                elif parent_id == sys_no:
                    validation_results['issues'].append(f"النظام {sys_no}: يشير لنفسه كنظام أب")
        
        # عرض النتائج
        print(f"✅ تم فحص {validation_results['total_systems']} نظام")
        print(f"🔍 عدد المشاكل: {len(validation_results['issues'])}")
        print(f"⚠️ عدد التحذيرات: {len(validation_results['warnings'])}")
        
        if validation_results['issues']:
            print("\n❌ المشاكل المكتشفة:")
            for issue in validation_results['issues'][:3]:  # أول 3 مشاكل
                print(f"  • {issue}")
            if len(validation_results['issues']) > 3:
                print(f"  ... و {len(validation_results['issues']) - 3} مشكلة أخرى")
        
        if validation_results['warnings']:
            print("\n⚠️ التحذيرات:")
            for warning in validation_results['warnings'][:3]:  # أول 3 تحذيرات
                print(f"  • {warning}")
            if len(validation_results['warnings']) > 3:
                print(f"  ... و {len(validation_results['warnings']) - 3} تحذير آخر")
        
        db_manager.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدقق البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار نظام إدارة قاعدة البيانات المتقدم")
    print("=" * 70)
    
    tests = [
        ("الاتصال بقاعدة البيانات Oracle", test_oracle_connection),
        ("مدير النسخ الاحتياطي", test_backup_manager),
        ("مراقب الأداء", test_performance_monitor),
        ("مدقق البيانات", test_data_validator)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "="*70)
    print("📊 ملخص نتائج الاختبار:")
    print("="*70)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام إدارة قاعدة البيانات جاهز للاستخدام")
        print("\n🚀 طرق التشغيل:")
        print("1️⃣ الواجهة المحسنة: python enhanced_main_window.py")
        print("2️⃣ لوحة الإدارة: python database_admin_panel.py")
        print("3️⃣ الاختبار الشامل: python test_enhanced_system.py")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل - يرجى مراجعة الأخطاء أعلاه")
    
    print("="*70)

if __name__ == "__main__":
    main()
