#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ظهور نظام الإعدادات العامة في النافذة الرئيسية
"""

import sys
from PySide6.QtWidgets import QApplication

def test_general_settings_in_main():
    """اختبار ظهور نظام الإعدادات العامة"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إعداد التطبيق للنصوص العربية
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
        
        # استيراد النافذة الرئيسية
        from main_window import SHPERPMainWindow
        
        # إنشاء النافذة
        window = SHPERPMainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # محاولة تحميل البيانات
        print("🔄 محاولة تحميل بيانات الأنظمة...")
        
        # التحقق من وجود مدير الأنظمة
        if hasattr(window, 'systems_manager'):
            systems_data = window.systems_manager.load_systems_data()
            print(f"✅ تم تحميل {len(systems_data)} نظام")
            
            # البحث عن نظام الإعدادات العامة
            general_settings_found = False
            general_settings_subsystems = []
            
            for system in systems_data:
                system_code = system.get('SYSTEM_CODE', '')
                system_name = system.get('SYSTEM_NAME', '')
                parent_id = system.get('PARENT_SYSTEM_ID')
                
                if system_code == 'GENSETTINGS':
                    general_settings_found = True
                    print(f"✅ تم العثور على النظام الرئيسي: {system_name} [{system_code}]")
                
                # البحث عن الأنظمة الفرعية
                if parent_id == 10:  # الأنظمة الفرعية للإعدادات العامة
                    general_settings_subsystems.append({
                        'code': system_code,
                        'name': system_name
                    })
            
            if general_settings_found:
                print("🎉 نظام الإعدادات العامة موجود في البيانات!")
                
                if general_settings_subsystems:
                    print(f"📋 الأنظمة الفرعية ({len(general_settings_subsystems)}):")
                    for subsystem in general_settings_subsystems:
                        print(f"   • {subsystem['name']} [{subsystem['code']}]")
                else:
                    print("⚠️ لا توجد أنظمة فرعية")
            else:
                print("❌ لم يتم العثور على نظام الإعدادات العامة")
        else:
            print("❌ لا يوجد مدير أنظمة")
        
        # اختبار فتح النظام
        print("\n🔄 اختبار فتح نظام الإعدادات العامة...")
        
        try:
            # محاكاة بيانات النظام
            system_data = {
                'SYSTEM_CODE': 'GENSETTINGS',
                'SYSTEM_NAME': 'نظام الإعدادات العامة الشامل'
            }
            
            # اختبار المعالج
            window.handle_system_selection(system_data)
            print("✅ تم اختبار فتح النظام الرئيسي")
            
            # اختبار الأنظمة الفرعية
            subsystem_codes = ['APPEARANCE', 'COMPANY', 'CURRENCIES', 'FISCALYEAR']
            
            for code in subsystem_codes:
                test_data = {
                    'SYSTEM_CODE': code,
                    'SYSTEM_NAME': f'اختبار {code}'
                }
                window.handle_system_selection(test_data)
                print(f"✅ تم اختبار فتح النظام الفرعي: {code}")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار فتح النظام: {e}")
        
        print("\n🎯 النتيجة:")
        if general_settings_found:
            print("✅ نظام الإعدادات العامة متاح ويعمل!")
            print("📋 يمكنك الآن رؤيته في شجرة الأنظمة")
            print("🚀 انقر على ⚙️ نظام الإعدادات العامة الشامل")
        else:
            print("❌ نظام الإعدادات العامة غير متاح")
        
        return general_settings_found
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار نظام الإعدادات العامة في النافذة الرئيسية")
    print("=" * 80)
    
    success = test_general_settings_in_main()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 نجح الاختبار!")
        print("✅ نظام الإعدادات العامة متاح في النافذة الرئيسية")
        
        print("\n📋 للاستخدام:")
        print("1️⃣ شغل: python main_window.py")
        print("2️⃣ ابحث عن: ⚙️ نظام الإعدادات العامة الشامل")
        print("3️⃣ انقر على النظام الرئيسي أو أي نظام فرعي")
        print("4️⃣ ستفتح نافذة الإعدادات المناسبة! ✨")
    else:
        print("❌ فشل الاختبار!")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
