#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدراج بسيط لنظام الإعدادات العامة في قاعدة البيانات
"""

import sys
import cx_Oracle

def simple_insert():
    """إدراج بسيط"""
    try:
        print("🚢 SHP ERP - إدراج نظام الإعدادات العامة في قاعدة البيانات")
        print("=" * 80)
        
        # معلومات الاتصال
        username = 'ship2025'
        password = 'ys123'
        host = 'localhost'
        port = 1521
        service_name = 'orcl'
        
        # إنشاء DSN
        dsn = f"{host}:{port}/{service_name}"
        
        print(f"🔄 الاتصال بقاعدة البيانات: {username}@{dsn}")
        
        # الاتصال بقاعدة البيانات
        connection = cx_Oracle.connect(username, password, dsn)
        cursor = connection.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # فحص البيانات الحالية
        print("\n📊 فحص البيانات الحالية:")
        cursor.execute("SELECT COUNT(*) FROM S_ERP_SYSTEM")
        current_count = cursor.fetchone()[0]
        print(f"   📈 عدد السجلات الحالية: {current_count}")
        
        # فحص وجود نظام الإعدادات العامة
        print("\n🔍 فحص وجود نظام الإعدادات العامة:")
        cursor.execute("""
            SELECT SYS_NO, SYS_CODE, SYS_NAME
            FROM S_ERP_SYSTEM
            WHERE SYS_CODE = 'GENST' OR SYS_NAME LIKE '%إعدادات%'
        """)
        
        existing = cursor.fetchall()
        if existing:
            print(f"   ⚠️ تم العثور على {len(existing)} نظام إعدادات موجود:")
            for row in existing:
                print(f"      • {row[0]}: {row[2]} [{row[1]}]")
            
            # سؤال المستخدم
            response = input("\n❓ هل تريد المتابعة والإدراج؟ (y/n): ")
            if response.lower() != 'y':
                print("❌ تم إلغاء العملية")
                return False
        else:
            print("   ✅ لا يوجد نظام إعدادات عامة - يمكن الإدراج")
        
        # البحث عن أعلى SYS_NO
        print("\n📊 البحث عن أعلى SYS_NO:")
        cursor.execute("SELECT NVL(MAX(SYS_NO), 0) FROM S_ERP_SYSTEM")
        max_id = cursor.fetchone()[0]
        print(f"   📈 أعلى SYS_NO حالي: {max_id}")
        
        # تحديد IDs الجديدة
        main_id = max_id + 1
        print(f"   🆔 ID النظام الرئيسي الجديد: {main_id}")
        
        # إدراج النظام الرئيسي
        print(f"\n🔄 إدراج النظام الرئيسي:")
        
        main_insert = """
        INSERT INTO S_ERP_SYSTEM (SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE, FORM_NO)
        VALUES (:1, :2, :3, :4, :5, :6, :7)
        """
        
        main_data = (
            main_id,
            'GENST',  # مختصر لـ General Settings
            'نظام الإعدادات العامة الشامل',
            1,  # تحت النظام الرئيسي
            main_id,
            0,  # نشط
            'general_settings_system.py'
        )
        
        cursor.execute(main_insert, main_data)
        print(f"   ✅ تم إدراج النظام الرئيسي: {main_data[2]}")
        
        # إدراج الأنظمة الفرعية
        print(f"\n📂 إدراج الأنظمة الفرعية:")
        
        subsystems = [
            ('APPR', 'إعدادات المظهر'),           # Appearance
            ('COMP', 'بيانات الشركة'),             # Company
            ('CURR', 'إعدادات العملات'),           # Currencies
            ('FISC', 'السنة المالية'),             # Fiscal Year
            ('LANG', 'إعدادات اللغة'),             # Language
            ('SECR', 'إعدادات الأمان'),            # Security
            ('MAIL', 'إعدادات البريد الإلكتروني'),  # Email
            ('PRNT', 'إعدادات الطباعة'),           # Printing
            ('REPT', 'إعدادات التقارير'),          # Reports
            ('BKUP', 'إعدادات النسخ الاحتياطي'),    # Backup Settings
            ('SYST', 'إعدادات النظام'),            # System Settings
            ('NOTF', 'إعدادات الإشعارات')          # Notifications
        ]
        
        for i, (code, name) in enumerate(subsystems):
            sub_id = main_id + 1 + i
            sub_data = (
                sub_id,
                code,
                name,
                main_id,  # تحت النظام الرئيسي للإعدادات
                i + 1,
                0,
                'general_settings_system.py'
            )
            
            cursor.execute(main_insert, sub_data)
            print(f"   ✅ {i+1:2d}. {name} [{code}]")
        
        # حفظ التغييرات
        connection.commit()
        print(f"\n💾 تم حفظ جميع التغييرات")
        
        # التحقق من الإدراج
        print(f"\n🔍 التحقق من الإدراج:")
        cursor.execute(f"""
            SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT 
            FROM S_ERP_SYSTEM 
            WHERE SYS_NO >= {main_id}
            ORDER BY SYS_NO
        """)
        
        inserted = cursor.fetchall()
        print(f"   ✅ تم إدراج {len(inserted)} نظام بنجاح:")
        
        for row in inserted:
            sys_no, sys_code, sys_name, sys_parnt = row
            if sys_parnt == main_id:
                print(f"      📂 {sys_no}: {sys_name} [{sys_code}] (فرعي)")
            else:
                print(f"      🏗️ {sys_no}: {sys_name} [{sys_code}] (رئيسي)")
        
        # إحصائيات نهائية
        cursor.execute("SELECT COUNT(*) FROM S_ERP_SYSTEM")
        final_count = cursor.fetchone()[0]
        added_count = final_count - current_count
        
        print(f"\n📊 الإحصائيات النهائية:")
        print(f"   📈 السجلات قبل الإدراج: {current_count}")
        print(f"   📈 السجلات بعد الإدراج: {final_count}")
        print(f"   ➕ السجلات المضافة: {added_count}")
        
        # إغلاق الاتصال
        cursor.close()
        connection.close()
        print(f"\n✅ تم إغلاق الاتصال بقاعدة البيانات")
        
        return True
        
    except cx_Oracle.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    success = simple_insert()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 تم إدراج نظام الإعدادات العامة في قاعدة البيانات بنجاح!")
        print("✅ النظام متاح الآن في شجرة الأنظمة")
        print("🚀 يمكنك الآن استخدام النظام من النافذة الرئيسية")
        
        print("\n📋 للاستخدام:")
        print("1️⃣ شغل: python main_window.py")
        print("2️⃣ ابحث عن: نظام الإعدادات العامة الشامل")
        print("3️⃣ انقر على النظام أو أي نظام فرعي")
        print("4️⃣ ستفتح نافذة الإعدادات! ✨")
    else:
        print("❌ فشل في إدراج نظام الإعدادات العامة")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
