#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Enhanced System Test
اختبار النظام المحسن مع إدارة قاعدة البيانات المتقدمة
"""

import sys
import time
import threading
from datetime import datetime

def test_oracle_db_manager():
    """اختبار مدير قاعدة البيانات Oracle"""
    print("🔄 اختبار مدير قاعدة البيانات Oracle...")
    
    try:
        from oracle_db_manager import get_db_manager
        
        db_manager = get_db_manager()
        
        # اختبار الاتصال
        print("📡 اختبار الاتصال...")
        success, message = db_manager.test_connection()
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            return False
        
        # اختبار تهيئة مجموعة الاتصالات
        print("🔗 اختبار مجموعة الاتصالات...")
        if db_manager.initialize_connection_pool():
            print("✅ تم إنشاء مجموعة الاتصالات بنجاح")
        else:
            print("❌ فشل في إنشاء مجموعة الاتصالات")
            return False
        
        # اختبار الاستعلامات الآمنة
        print("🔍 اختبار الاستعلامات الآمنة...")
        success, result = db_manager.execute_query_safe(
            "SELECT COUNT(*) as COUNT FROM S_ERP_SYSTEM", 
            timeout=10
        )
        
        if success:
            count = result[0]['COUNT'] if result else 0
            print(f"✅ تم العثور على {count} نظام")
        else:
            print(f"❌ فشل في الاستعلام: {result}")
            return False
        
        # اختبار معلومات الجدول
        print("📋 اختبار معلومات الجدول...")
        success, table_info = db_manager.get_table_info("S_ERP_SYSTEM")
        
        if success:
            columns_count = len(table_info.get('columns', []))
            print(f"✅ الجدول يحتوي على {columns_count} عمود")
        else:
            print(f"❌ فشل في الحصول على معلومات الجدول: {table_info}")
        
        # اختبار تحميل بيانات الأنظمة
        print("🌳 اختبار تحميل بيانات الأنظمة...")
        success, systems_data = db_manager.get_systems_data()
        
        if success:
            print(f"✅ تم تحميل {len(systems_data)} نظام")
        else:
            print(f"❌ فشل في تحميل الأنظمة: {systems_data}")
        
        # إغلاق الاتصالات
        db_manager.close_connections()
        print("✅ تم إغلاق الاتصالات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير قاعدة البيانات: {e}")
        return False

def test_enhanced_systems_manager():
    """اختبار مدير الأنظمة المحسن"""
    print("\n🔄 اختبار مدير الأنظمة المحسن...")
    
    try:
        from enhanced_systems_manager import EnhancedSystemsManager
        from PySide6.QtCore import QCoreApplication
        
        # إنشاء تطبيق Qt للإشارات
        app = QCoreApplication.instance()
        if app is None:
            app = QCoreApplication(sys.argv)
        
        manager = EnhancedSystemsManager()
        
        # متغيرات لتتبع النتائج
        connection_result = {'success': False, 'message': ''}
        systems_result = {'data': []}
        
        def on_connection_status(success, message):
            connection_result['success'] = success
            connection_result['message'] = message
            print(f"📡 حالة الاتصال: {'نجح' if success else 'فشل'} - {message}")
        
        def on_systems_loaded(systems_data):
            systems_result['data'] = systems_data
            print(f"🌳 تم تحميل {len(systems_data)} نظام")
        
        def on_error(error_message):
            print(f"❌ خطأ: {error_message}")
        
        # ربط الإشارات
        manager.connection_status_changed.connect(on_connection_status)
        manager.systems_data_updated.connect(on_systems_loaded)
        manager.error_occurred.connect(on_error)
        
        # بدء الاتصال
        print("📡 بدء الاتصال غير المتزامن...")
        manager.connect_to_database_async()
        
        # انتظار النتائج (مع timeout)
        timeout = 30  # 30 ثانية
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            app.processEvents()
            if connection_result['success'] and systems_result['data']:
                break
            time.sleep(0.1)
        
        if connection_result['success']:
            print("✅ نجح الاتصال غير المتزامن")
            
            # اختبار الإحصائيات
            stats = manager.get_connection_stats()
            print(f"📊 الإحصائيات: {stats}")
            
            # اختبار بناء الشجرة
            root_systems = manager.get_root_systems()
            print(f"🌳 عدد الأنظمة الجذر: {len(root_systems)}")
            
            # قطع الاتصال
            manager.disconnect()
            print("✅ تم قطع الاتصال")
            
            return True
        else:
            print(f"❌ فشل الاتصال: {connection_result['message']}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الأنظمة المحسن: {e}")
        return False

def test_advanced_features():
    """اختبار الميزات المتقدمة"""
    print("\n🔄 اختبار الميزات المتقدمة...")
    
    try:
        from advanced_db_features import BackupManager, PerformanceMonitor, DataValidator
        
        # اختبار مدير النسخ الاحتياطي
        print("💾 اختبار مدير النسخ الاحتياطي...")
        backup_manager = BackupManager()
        
        # قائمة النسخ الاحتياطية
        backups = backup_manager.list_backups()
        print(f"📋 عدد النسخ الاحتياطية الموجودة: {len(backups)}")
        
        # اختبار مراقب الأداء
        print("📊 اختبار مراقب الأداء...")
        performance_monitor = PerformanceMonitor()
        
        # جمع بيانات الأداء مرة واحدة
        performance_monitor.collect_performance_data()
        
        # الحصول على ملخص الأداء
        summary = performance_monitor.get_performance_summary()
        print(f"📈 ملخص الأداء: {summary}")
        
        # اختبار مدقق البيانات
        print("🔍 اختبار مدقق البيانات...")
        data_validator = DataValidator()
        
        # متغير لتتبع نتائج التحقق
        validation_result = {'completed': False, 'results': {}}
        
        def on_validation_completed(success, results):
            validation_result['completed'] = True
            validation_result['results'] = results
            if success:
                print(f"✅ تم التحقق من {results.get('total_systems', 0)} نظام")
                print(f"🔍 عدد المشاكل: {len(results.get('issues', []))}")
                print(f"⚠️ عدد التحذيرات: {len(results.get('warnings', []))}")
            else:
                print(f"❌ فشل التحقق: {results}")
        
        data_validator.validation_completed.connect(on_validation_completed)
        
        # بدء التحقق في خيط منفصل
        validation_thread = threading.Thread(
            target=data_validator.validate_systems_data,
            daemon=True
        )
        validation_thread.start()
        
        # انتظار انتهاء التحقق
        validation_thread.join(timeout=30)
        
        if validation_result['completed']:
            print("✅ تم اختبار مدقق البيانات بنجاح")
        else:
            print("⚠️ انتهت مهلة اختبار مدقق البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات المتقدمة: {e}")
        return False

def test_enhanced_main_window():
    """اختبار الواجهة الرئيسية المحسنة"""
    print("\n🔄 اختبار الواجهة الرئيسية المحسنة...")
    
    try:
        from enhanced_main_window import EnhancedMainWindow
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة
        window = EnhancedMainWindow()
        
        print("✅ تم إنشاء الواجهة الرئيسية المحسنة")
        
        # اختبار قصير للواجهة
        window.show()
        
        # معالجة الأحداث لفترة قصيرة
        for _ in range(10):
            app.processEvents()
            time.sleep(0.1)
        
        window.close()
        print("✅ تم اختبار الواجهة الرئيسية بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرئيسية: {e}")
        return False

def test_database_admin_panel():
    """اختبار لوحة إدارة قاعدة البيانات"""
    print("\n🔄 اختبار لوحة إدارة قاعدة البيانات...")
    
    try:
        from database_admin_panel import DatabaseAdminPanel
        from PySide6.QtWidgets import QApplication
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء لوحة الإدارة
        panel = DatabaseAdminPanel()
        
        print("✅ تم إنشاء لوحة إدارة قاعدة البيانات")
        
        # اختبار قصير للوحة
        panel.show()
        
        # معالجة الأحداث لفترة قصيرة
        for _ in range(10):
            app.processEvents()
            time.sleep(0.1)
        
        panel.close()
        print("✅ تم اختبار لوحة الإدارة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة الإدارة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("🚢 SHP ERP - اختبار النظام المحسن مع إدارة قاعدة البيانات المتقدمة")
    print("=" * 80)
    
    tests = [
        ("مدير قاعدة البيانات Oracle", test_oracle_db_manager),
        ("مدير الأنظمة المحسن", test_enhanced_systems_manager),
        ("الميزات المتقدمة", test_advanced_features),
        ("الواجهة الرئيسية المحسنة", test_enhanced_main_window),
        ("لوحة إدارة قاعدة البيانات", test_database_admin_panel)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "="*80)
    print("📊 ملخص نتائج الاختبار:")
    print("="*80)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n🚀 طرق التشغيل:")
        print("1️⃣ الواجهة الرئيسية المحسنة: python enhanced_main_window.py")
        print("2️⃣ لوحة إدارة قاعدة البيانات: python database_admin_panel.py")
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")
    
    print("="*80)

if __name__ == "__main__":
    main()
