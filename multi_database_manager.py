#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Multi Database Manager
مدير قواعد البيانات المتعددة
"""

import json
import os
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False
    print("⚠️ مكتبة Oracle غير متوفرة")

try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False

try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

from arabic_text_helper import format_arabic_text

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseType(Enum):
    """أنواع قواعد البيانات المدعومة"""
    ORACLE = "Oracle"
    MYSQL = "MySQL"
    POSTGRESQL = "PostgreSQL"
    SQLITE = "SQLite"

@dataclass
class DatabaseConnection:
    """معلومات اتصال قاعدة البيانات"""
    name: str
    db_type: DatabaseType
    host: str = "localhost"
    port: int = 1521
    database: str = ""
    service_name: str = ""
    username: str = ""
    password: str = ""
    is_active: bool = True
    created_at: str = ""
    last_connected: str = ""
    connection_string: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

class MultiDatabaseManager:
    """مدير قواعد البيانات المتعددة"""
    
    def __init__(self, config_file: str = "database_connections.json"):
        self.config_file = config_file
        self.connections: Dict[str, DatabaseConnection] = {}
        self.active_connections: Dict[str, Any] = {}
        self.load_connections()
        
    def load_connections(self):
        """تحميل الاتصالات المحفوظة"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for name, conn_data in data.items():
                    # تحويل نوع قاعدة البيانات من نص إلى enum
                    if isinstance(conn_data['db_type'], str):
                        conn_data['db_type'] = DatabaseType(conn_data['db_type'])
                    
                    self.connections[name] = DatabaseConnection(**conn_data)
                    
                logger.info(f"تم تحميل {len(self.connections)} اتصال من {self.config_file}")
            else:
                # إنشاء اتصال افتراضي
                self.create_default_connection()
                
        except Exception as e:
            logger.error(f"خطأ في تحميل الاتصالات: {e}")
            self.create_default_connection()
    
    def create_default_connection(self):
        """إنشاء اتصال افتراضي"""
        default_conn = DatabaseConnection(
            name="SHIP2025_Oracle",
            db_type=DatabaseType.ORACLE,
            host="localhost",
            port=1521,
            service_name="orcl",
            username="ship2025",
            password="ys123"
        )
        self.connections[default_conn.name] = default_conn
        self.save_connections()
        logger.info("تم إنشاء اتصال افتراضي")
    
    def save_connections(self):
        """حفظ الاتصالات"""
        try:
            data = {}
            for name, conn in self.connections.items():
                conn_dict = asdict(conn)
                # تحويل enum إلى نص للحفظ
                conn_dict['db_type'] = conn.db_type.value
                data[name] = conn_dict
                
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"تم حفظ {len(self.connections)} اتصال في {self.config_file}")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الاتصالات: {e}")
    
    def add_connection(self, connection: DatabaseConnection) -> bool:
        """إضافة اتصال جديد"""
        try:
            if connection.name in self.connections:
                logger.warning(f"الاتصال {connection.name} موجود مسبقاً")
                return False
                
            self.connections[connection.name] = connection
            self.save_connections()
            logger.info(f"تم إضافة اتصال جديد: {connection.name}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة الاتصال: {e}")
            return False
    
    def remove_connection(self, name: str) -> bool:
        """حذف اتصال"""
        try:
            if name not in self.connections:
                logger.warning(f"الاتصال {name} غير موجود")
                return False
                
            # قطع الاتصال إذا كان نشطاً
            if name in self.active_connections:
                self.disconnect(name)
                
            del self.connections[name]
            self.save_connections()
            logger.info(f"تم حذف الاتصال: {name}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حذف الاتصال: {e}")
            return False
    
    def test_connection(self, name: str) -> Tuple[bool, str]:
        """اختبار اتصال"""
        try:
            if name not in self.connections:
                return False, f"الاتصال {name} غير موجود"
                
            conn_info = self.connections[name]
            
            if conn_info.db_type == DatabaseType.ORACLE:
                return self._test_oracle_connection(conn_info)
            elif conn_info.db_type == DatabaseType.MYSQL:
                return self._test_mysql_connection(conn_info)
            elif conn_info.db_type == DatabaseType.POSTGRESQL:
                return self._test_postgresql_connection(conn_info)
            elif conn_info.db_type == DatabaseType.SQLITE:
                return self._test_sqlite_connection(conn_info)
            else:
                return False, f"نوع قاعدة البيانات {conn_info.db_type.value} غير مدعوم"
                
        except Exception as e:
            logger.error(f"خطأ في اختبار الاتصال {name}: {e}")
            return False, str(e)
    
    def _test_oracle_connection(self, conn_info: DatabaseConnection) -> Tuple[bool, str]:
        """اختبار اتصال Oracle"""
        if not ORACLE_AVAILABLE:
            return False, "مكتبة Oracle غير متوفرة"
            
        try:
            dsn = f"""(DESCRIPTION=
                (ADDRESS=(PROTOCOL=TCP)(HOST={conn_info.host})(PORT={conn_info.port}))
                (CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME={conn_info.service_name}))
            )"""
            
            connection = cx_Oracle.connect(
                user=conn_info.username,
                password=conn_info.password,
                dsn=dsn,
                encoding="UTF-8"
            )
            
            cursor = connection.cursor()
            cursor.execute("SELECT 1 FROM DUAL")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            if result:
                # تحديث وقت آخر اتصال
                conn_info.last_connected = datetime.now().isoformat()
                self.save_connections()
                return True, "نجح الاتصال بقاعدة البيانات Oracle"
            else:
                return False, "فشل في تنفيذ الاستعلام التجريبي"
                
        except Exception as e:
            return False, f"خطأ في الاتصال: {str(e)}"
    
    def _test_mysql_connection(self, conn_info: DatabaseConnection) -> Tuple[bool, str]:
        """اختبار اتصال MySQL"""
        if not MYSQL_AVAILABLE:
            return False, "مكتبة MySQL غير متوفرة"
            
        try:
            connection = pymysql.connect(
                host=conn_info.host,
                port=conn_info.port,
                user=conn_info.username,
                password=conn_info.password,
                database=conn_info.database,
                charset='utf8mb4'
            )
            
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            if result:
                conn_info.last_connected = datetime.now().isoformat()
                self.save_connections()
                return True, "نجح الاتصال بقاعدة البيانات MySQL"
            else:
                return False, "فشل في تنفيذ الاستعلام التجريبي"
                
        except Exception as e:
            return False, f"خطأ في الاتصال: {str(e)}"
    
    def _test_postgresql_connection(self, conn_info: DatabaseConnection) -> Tuple[bool, str]:
        """اختبار اتصال PostgreSQL"""
        if not POSTGRESQL_AVAILABLE:
            return False, "مكتبة PostgreSQL غير متوفرة"
            
        try:
            connection = psycopg2.connect(
                host=conn_info.host,
                port=conn_info.port,
                user=conn_info.username,
                password=conn_info.password,
                database=conn_info.database
            )
            
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            if result:
                conn_info.last_connected = datetime.now().isoformat()
                self.save_connections()
                return True, "نجح الاتصال بقاعدة البيانات PostgreSQL"
            else:
                return False, "فشل في تنفيذ الاستعلام التجريبي"
                
        except Exception as e:
            return False, f"خطأ في الاتصال: {str(e)}"
    
    def _test_sqlite_connection(self, conn_info: DatabaseConnection) -> Tuple[bool, str]:
        """اختبار اتصال SQLite"""
        if not SQLITE_AVAILABLE:
            return False, "مكتبة SQLite غير متوفرة"
            
        try:
            connection = sqlite3.connect(conn_info.database)
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            if result:
                conn_info.last_connected = datetime.now().isoformat()
                self.save_connections()
                return True, "نجح الاتصال بقاعدة البيانات SQLite"
            else:
                return False, "فشل في تنفيذ الاستعلام التجريبي"
                
        except Exception as e:
            return False, f"خطأ في الاتصال: {str(e)}"
    
    def get_connections_list(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة الاتصالات"""
        connections_list = []
        for name, conn in self.connections.items():
            conn_dict = {
                'name': name,
                'type': conn.db_type.value,
                'host': conn.host,
                'port': conn.port,
                'database': conn.database or conn.service_name,
                'username': conn.username,
                'is_active': conn.is_active,
                'created_at': conn.created_at,
                'last_connected': conn.last_connected,
                'is_connected': name in self.active_connections
            }
            connections_list.append(conn_dict)
        
        return connections_list
    
    def get_connection_info(self, name: str) -> Optional[DatabaseConnection]:
        """الحصول على معلومات اتصال"""
        return self.connections.get(name)

# اختبار المدير
if __name__ == "__main__":
    print("🔄 اختبار مدير قواعد البيانات المتعددة...")
    
    manager = MultiDatabaseManager()
    
    # عرض الاتصالات المتاحة
    connections = manager.get_connections_list()
    print(f"📋 عدد الاتصالات المتاحة: {len(connections)}")
    
    for conn in connections:
        print(f"  • {conn['name']} ({conn['type']}) - {conn['host']}:{conn['port']}")
    
    # اختبار الاتصال الافتراضي
    if connections:
        default_name = connections[0]['name']
        success, message = manager.test_connection(default_name)
        status = "✅ نجح" if success else "❌ فشل"
        print(f"{status} اختبار الاتصال {default_name}: {message}")
    
    print("✅ اختبار مدير قواعد البيانات المتعددة مكتمل")
