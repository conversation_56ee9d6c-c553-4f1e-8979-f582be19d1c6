#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Advanced SQL Editor
محرر SQL المتقدم
"""

import sys
import re
from typing import List, Dict, Any, Optional

from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
    QPushButton, QLabel, QComboBox, QSplitter, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QProgressBar,
    QCompleter, QFrame, QToolBar, QFileDialog, QInputDialog
)
from PySide6.QtCore import Qt, QStringListModel, QThread, Signal, QTimer
from PySide6.QtGui import (
    QFont, QSyntaxHighlighter, QTextCharFormat, QColor,
    QTextDocument, QKeySequence, QShortcut
)

from arabic_text_helper import format_arabic_text, setup_arabic_widget

class SQLSyntaxHighlighter(QSyntaxHighlighter):
    """مُلون صيغة SQL"""
    
    def __init__(self, document):
        super().__init__(document)
        self.setup_highlighting_rules()
    
    def setup_highlighting_rules(self):
        """إعداد قواعد التلوين"""
        self.highlighting_rules = []
        
        # الكلمات المحجوزة في SQL
        sql_keywords = [
            'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE',
            'CREATE', 'DROP', 'ALTER', 'TABLE', 'INDEX', 'VIEW',
            'DATABASE', 'SCHEMA', 'PROCEDURE', 'FUNCTION', 'TRIGGER',
            'JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL', 'OUTER',
            'ON', 'AS', 'AND', 'OR', 'NOT', 'IN', 'EXISTS',
            'BETWEEN', 'LIKE', 'IS', 'NULL', 'DISTINCT', 'ORDER',
            'BY', 'GROUP', 'HAVING', 'LIMIT', 'OFFSET', 'UNION',
            'INTERSECT', 'EXCEPT', 'CASE', 'WHEN', 'THEN', 'ELSE',
            'END', 'IF', 'WHILE', 'FOR', 'DECLARE', 'SET',
            'BEGIN', 'COMMIT', 'ROLLBACK', 'TRANSACTION'
        ]
        
        # تنسيق الكلمات المحجوزة
        keyword_format = QTextCharFormat()
        keyword_format.setColor(QColor(0, 0, 255))  # أزرق
        keyword_format.setFontWeight(QFont.Bold)
        
        for keyword in sql_keywords:
            pattern = f'\\b{keyword}\\b'
            self.highlighting_rules.append((re.compile(pattern, re.IGNORECASE), keyword_format))
        
        # النصوص (بين علامات اقتباس)
        string_format = QTextCharFormat()
        string_format.setColor(QColor(255, 0, 0))  # أحمر
        self.highlighting_rules.append((re.compile(r"'[^']*'"), string_format))
        self.highlighting_rules.append((re.compile(r'"[^"]*"'), string_format))
        
        # الأرقام
        number_format = QTextCharFormat()
        number_format.setColor(QColor(255, 165, 0))  # برتقالي
        self.highlighting_rules.append((re.compile(r'\b\d+\.?\d*\b'), number_format))
        
        # التعليقات
        comment_format = QTextCharFormat()
        comment_format.setColor(QColor(0, 128, 0))  # أخضر
        comment_format.setFontItalic(True)
        self.highlighting_rules.append((re.compile(r'--[^\n]*'), comment_format))
        self.highlighting_rules.append((re.compile(r'/\*.*?\*/', re.DOTALL), comment_format))
        
        # أسماء الجداول والأعمدة (تخمين بسيط)
        table_format = QTextCharFormat()
        table_format.setColor(QColor(128, 0, 128))  # بنفسجي
        self.highlighting_rules.append((re.compile(r'\b[A-Z_][A-Z0-9_]*\b'), table_format))
    
    def highlightBlock(self, text):
        """تطبيق التلوين على النص"""
        for pattern, format_obj in self.highlighting_rules:
            for match in pattern.finditer(text):
                start, end = match.span()
                self.setFormat(start, end - start, format_obj)

class SQLQueryThread(QThread):
    """خيط تنفيذ استعلامات SQL"""
    query_finished = Signal(bool, object)
    progress_updated = Signal(int)
    
    def __init__(self, query, connection_manager, connection_name):
        super().__init__()
        self.query = query
        self.connection_manager = connection_manager
        self.connection_name = connection_name
    
    def run(self):
        """تنفيذ الاستعلام"""
        try:
            # محاكاة تقدم التنفيذ
            for i in range(0, 101, 10):
                self.progress_updated.emit(i)
                self.msleep(100)
            
            # هنا سيتم تنفيذ الاستعلام الفعلي
            # محاكاة نتائج
            if self.query.strip().upper().startswith('SELECT'):
                # محاكاة نتائج SELECT
                result = {
                    'type': 'select',
                    'columns': ['ID', 'NAME', 'TYPE', 'STATUS'],
                    'rows': [
                        [1, 'نظام ERP', 'رئيسي', 'نشط'],
                        [2, 'نظام المحاسبة', 'فرعي', 'نشط'],
                        [3, 'نظام المخازن', 'فرعي', 'نشط']
                    ],
                    'row_count': 3,
                    'execution_time': 0.15
                }
            else:
                # محاكاة نتائج العمليات الأخرى
                result = {
                    'type': 'operation',
                    'message': 'تم تنفيذ الاستعلام بنجاح',
                    'affected_rows': 1,
                    'execution_time': 0.08
                }
            
            self.query_finished.emit(True, result)
            
        except Exception as e:
            self.query_finished.emit(False, str(e))

class AdvancedSQLEditor(QWidget):
    """محرر SQL المتقدم"""
    
    def __init__(self, connection_manager=None):
        super().__init__()
        self.connection_manager = connection_manager
        self.current_connection = None
        self.query_thread = None
        self.query_history = []
        self.saved_queries = {}
        
        self.init_ui()
        self.setup_shortcuts()
        self.setup_auto_completion()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        layout = QVBoxLayout(self)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # المنطقة الرئيسية
        main_splitter = QSplitter(Qt.Vertical)
        
        # منطقة المحرر
        editor_widget = self.create_editor_area()
        main_splitter.addWidget(editor_widget)
        
        # منطقة النتائج
        results_widget = self.create_results_area()
        main_splitter.addWidget(results_widget)
        
        # تحديد نسب التقسيم
        main_splitter.setSizes([400, 300])
        
        layout.addWidget(main_splitter)
        
        # شريط الحالة
        self.create_status_bar()
        layout.addWidget(self.status_bar)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = QFrame()
        self.toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(self.toolbar)
        
        # اختيار الاتصال
        toolbar_layout.addWidget(QLabel(format_arabic_text("الاتصال:")))
        
        self.connection_combo = QComboBox()
        self.connection_combo.setMinimumWidth(200)
        self.connection_combo.currentTextChanged.connect(self.on_connection_changed)
        toolbar_layout.addWidget(self.connection_combo)
        
        # فاصل
        toolbar_layout.addWidget(QFrame())
        
        # أزرار التحكم
        buttons = [
            ("▶️", "تنفيذ (F5)", self.execute_query),
            ("⏹️", "إيقاف", self.stop_query),
            ("🔄", "تحديث", self.refresh_connection),
            ("💾", "حفظ", self.save_query),
            ("📂", "فتح", self.open_query),
            ("🗑️", "مسح", self.clear_editor),
            ("📋", "تاريخ", self.show_history)
        ]
        
        for icon, tooltip, action in buttons:
            btn = QPushButton(icon)
            btn.setToolTip(format_arabic_text(tooltip))
            btn.setFixedSize(35, 35)
            btn.clicked.connect(action)
            toolbar_layout.addWidget(btn)
        
        toolbar_layout.addStretch()
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        toolbar_layout.addWidget(self.progress_bar)
        
    def create_editor_area(self):
        """إنشاء منطقة المحرر"""
        editor_widget = QWidget()
        layout = QVBoxLayout(editor_widget)
        
        # شريط أدوات المحرر
        editor_toolbar = QHBoxLayout()
        
        # أزرار الاستعلامات السريعة
        quick_queries = [
            ("SELECT", "SELECT * FROM table_name;"),
            ("INSERT", "INSERT INTO table_name (column1, column2) VALUES (value1, value2);"),
            ("UPDATE", "UPDATE table_name SET column1 = value1 WHERE condition;"),
            ("DELETE", "DELETE FROM table_name WHERE condition;"),
            ("CREATE", "CREATE TABLE table_name (\n  id NUMBER PRIMARY KEY,\n  name VARCHAR2(100)\n);"),
            ("DESC", "DESC table_name;")
        ]
        
        for text, template in quick_queries:
            btn = QPushButton(text)
            btn.setFixedSize(60, 25)
            btn.clicked.connect(lambda checked, t=template: self.insert_template(t))
            editor_toolbar.addWidget(btn)
        
        editor_toolbar.addStretch()
        layout.addLayout(editor_toolbar)
        
        # محرر SQL
        self.sql_editor = QTextEdit()
        self.sql_editor.setFont(QFont("Consolas", 12))
        self.sql_editor.setPlaceholderText(format_arabic_text("اكتب استعلام SQL هنا..."))
        
        # إضافة تلوين الصيغة
        self.highlighter = SQLSyntaxHighlighter(self.sql_editor.document())
        
        layout.addWidget(self.sql_editor)
        
        return editor_widget
        
    def create_results_area(self):
        """إنشاء منطقة النتائج"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)
        
        # عنوان النتائج
        results_header = QHBoxLayout()
        
        self.results_label = QLabel(format_arabic_text("النتائج"))
        self.results_label.setFont(QFont("Tahoma", 10, QFont.Bold))
        results_header.addWidget(self.results_label)
        
        results_header.addStretch()
        
        # أزرار النتائج
        self.export_btn = QPushButton("📤")
        self.export_btn.setToolTip(format_arabic_text("تصدير النتائج"))
        self.export_btn.setFixedSize(30, 30)
        self.export_btn.clicked.connect(self.export_results)
        results_header.addWidget(self.export_btn)
        
        layout.addLayout(results_header)
        
        # تبويبات النتائج
        results_splitter = QSplitter(Qt.Horizontal)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setAlternatingRowColors(True)
        results_splitter.addWidget(self.results_table)
        
        # منطقة الرسائل
        messages_widget = QWidget()
        messages_layout = QVBoxLayout(messages_widget)
        
        messages_layout.addWidget(QLabel(format_arabic_text("الرسائل")))
        
        self.messages_area = QTextEdit()
        self.messages_area.setMaximumHeight(150)
        self.messages_area.setReadOnly(True)
        self.messages_area.setFont(QFont("Consolas", 10))
        messages_layout.addWidget(self.messages_area)
        
        results_splitter.addWidget(messages_widget)
        results_splitter.setSizes([500, 200])
        
        layout.addWidget(results_splitter)
        
        return results_widget
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QFrame()
        self.status_bar.setFrameStyle(QFrame.StyledPanel)
        status_layout = QHBoxLayout(self.status_bar)
        
        self.status_label = QLabel(format_arabic_text("جاهز"))
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.connection_status = QLabel("🔴")
        self.connection_status.setToolTip(format_arabic_text("حالة الاتصال"))
        status_layout.addWidget(self.connection_status)
        
        self.row_count_label = QLabel("")
        status_layout.addWidget(self.row_count_label)
        
    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # F5 لتنفيذ الاستعلام
        execute_shortcut = QShortcut(QKeySequence("F5"), self)
        execute_shortcut.activated.connect(self.execute_query)
        
        # Ctrl+S للحفظ
        save_shortcut = QShortcut(QKeySequence.Save, self)
        save_shortcut.activated.connect(self.save_query)
        
        # Ctrl+O للفتح
        open_shortcut = QShortcut(QKeySequence.Open, self)
        open_shortcut.activated.connect(self.open_query)
        
        # Ctrl+N لمحرر جديد
        new_shortcut = QShortcut(QKeySequence.New, self)
        new_shortcut.activated.connect(self.clear_editor)
        
    def setup_auto_completion(self):
        """إعداد الإكمال التلقائي"""
        # قائمة الكلمات للإكمال التلقائي
        sql_keywords = [
            'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE',
            'CREATE', 'DROP', 'ALTER', 'TABLE', 'INDEX', 'VIEW',
            'JOIN', 'INNER', 'LEFT', 'RIGHT', 'ON', 'AS',
            'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN',
            'LIKE', 'IS', 'NULL', 'DISTINCT', 'ORDER', 'BY',
            'GROUP', 'HAVING', 'LIMIT', 'UNION'
        ]
        
        # إضافة أسماء الجداول الشائعة
        common_tables = [
            'S_ERP_SYSTEM', 'USERS', 'ROLES', 'PERMISSIONS',
            'CUSTOMERS', 'SUPPLIERS', 'PRODUCTS', 'ORDERS'
        ]
        
        all_completions = sql_keywords + common_tables
        
        completer = QCompleter(all_completions)
        completer.setCaseSensitivity(Qt.CaseInsensitive)
        completer.setCompletionMode(QCompleter.PopupCompletion)
        
        # ملاحظة: QTextEdit لا يدعم QCompleter مباشرة
        # يمكن تطوير هذا لاحقاً باستخدام QPlainTextEdit
        
    def load_connections(self):
        """تحميل قائمة الاتصالات"""
        self.connection_combo.clear()
        
        if self.connection_manager:
            connections = self.connection_manager.get_connections_list()
            for conn in connections:
                self.connection_combo.addItem(f"{conn['name']} ({conn['type']})", conn['name'])
    
    def on_connection_changed(self):
        """عند تغيير الاتصال"""
        current_data = self.connection_combo.currentData()
        if current_data:
            self.current_connection = current_data
            self.update_connection_status()
    
    def update_connection_status(self):
        """تحديث حالة الاتصال"""
        if self.current_connection and self.connection_manager:
            success, message = self.connection_manager.test_connection(self.current_connection)
            if success:
                self.connection_status.setText("🟢")
                self.connection_status.setToolTip(format_arabic_text("متصل"))
            else:
                self.connection_status.setText("🔴")
                self.connection_status.setToolTip(format_arabic_text(f"غير متصل: {message}"))
        else:
            self.connection_status.setText("⚪")
            self.connection_status.setToolTip(format_arabic_text("لا يوجد اتصال"))
    
    def insert_template(self, template):
        """إدراج قالب استعلام"""
        cursor = self.sql_editor.textCursor()
        cursor.insertText(template)
        self.sql_editor.setFocus()
    
    def execute_query(self):
        """تنفيذ الاستعلام"""
        if not self.current_connection:
            QMessageBox.warning(self, "تحذير", format_arabic_text("يرجى اختيار اتصال أولاً"))
            return
        
        query = self.sql_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", format_arabic_text("يرجى كتابة استعلام أولاً"))
            return
        
        # إضافة الاستعلام للتاريخ
        self.query_history.append({
            'query': query,
            'timestamp': QTimer().remainingTime(),
            'connection': self.current_connection
        })
        
        # بدء التنفيذ
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText(format_arabic_text("جاري تنفيذ الاستعلام..."))
        
        self.messages_area.clear()
        self.messages_area.append(format_arabic_text(f"🔄 تنفيذ الاستعلام على {self.current_connection}..."))
        
        # إنشاء خيط التنفيذ
        self.query_thread = SQLQueryThread(query, self.connection_manager, self.current_connection)
        self.query_thread.query_finished.connect(self.on_query_finished)
        self.query_thread.progress_updated.connect(self.progress_bar.setValue)
        self.query_thread.start()
    
    def on_query_finished(self, success, result):
        """عند انتهاء تنفيذ الاستعلام"""
        self.progress_bar.setVisible(False)
        
        if success:
            self.display_results(result)
            self.status_label.setText(format_arabic_text("تم تنفيذ الاستعلام بنجاح"))
        else:
            self.messages_area.append(format_arabic_text(f"❌ خطأ: {result}"))
            self.status_label.setText(format_arabic_text("فشل في تنفيذ الاستعلام"))
    
    def display_results(self, result):
        """عرض النتائج"""
        if result['type'] == 'select':
            # عرض نتائج SELECT
            self.results_table.setRowCount(len(result['rows']))
            self.results_table.setColumnCount(len(result['columns']))
            self.results_table.setHorizontalHeaderLabels(result['columns'])
            
            for row_idx, row_data in enumerate(result['rows']):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    self.results_table.setItem(row_idx, col_idx, item)
            
            # تحسين عرض الجدول
            self.results_table.resizeColumnsToContents()
            header = self.results_table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.Interactive)
            
            # تحديث عداد الصفوف
            self.row_count_label.setText(format_arabic_text(f"الصفوف: {result['row_count']}"))
            
            # رسالة النجاح
            self.messages_area.append(format_arabic_text(
                f"✅ تم تنفيذ الاستعلام بنجاح\n"
                f"📊 عدد الصفوف: {result['row_count']}\n"
                f"⏱️ وقت التنفيذ: {result['execution_time']:.3f} ثانية"
            ))
            
        else:
            # عرض نتائج العمليات الأخرى
            self.results_table.setRowCount(0)
            self.results_table.setColumnCount(0)
            
            self.messages_area.append(format_arabic_text(
                f"✅ {result['message']}\n"
                f"📝 الصفوف المتأثرة: {result.get('affected_rows', 0)}\n"
                f"⏱️ وقت التنفيذ: {result['execution_time']:.3f} ثانية"
            ))
    
    def stop_query(self):
        """إيقاف الاستعلام"""
        if self.query_thread and self.query_thread.isRunning():
            self.query_thread.terminate()
            self.query_thread.wait()
            self.progress_bar.setVisible(False)
            self.status_label.setText(format_arabic_text("تم إيقاف الاستعلام"))
            self.messages_area.append(format_arabic_text("⏹️ تم إيقاف الاستعلام"))
    
    def refresh_connection(self):
        """تحديث الاتصال"""
        self.load_connections()
        self.update_connection_status()
    
    def save_query(self):
        """حفظ الاستعلام"""
        query = self.sql_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", format_arabic_text("لا يوجد استعلام للحفظ"))
            return
        
        name, ok = QInputDialog.getText(self, "حفظ الاستعلام", format_arabic_text("اسم الاستعلام:"))
        if ok and name:
            self.saved_queries[name] = query
            QMessageBox.information(self, "نجح", format_arabic_text("تم حفظ الاستعلام"))
    
    def open_query(self):
        """فتح استعلام محفوظ"""
        if not self.saved_queries:
            QMessageBox.information(self, "معلومات", format_arabic_text("لا توجد استعلامات محفوظة"))
            return
        
        names = list(self.saved_queries.keys())
        name, ok = QInputDialog.getItem(self, "فتح استعلام", format_arabic_text("اختر الاستعلام:"), names, 0, False)
        if ok and name:
            self.sql_editor.setPlainText(self.saved_queries[name])
    
    def clear_editor(self):
        """مسح المحرر"""
        self.sql_editor.clear()
        self.results_table.setRowCount(0)
        self.results_table.setColumnCount(0)
        self.messages_area.clear()
        self.row_count_label.setText("")
    
    def show_history(self):
        """عرض تاريخ الاستعلامات"""
        if not self.query_history:
            QMessageBox.information(self, "معلومات", format_arabic_text("لا يوجد تاريخ استعلامات"))
            return
        
        # يمكن تطوير نافذة منفصلة لعرض التاريخ
        QMessageBox.information(self, "تاريخ الاستعلامات", 
                              format_arabic_text(f"عدد الاستعلامات: {len(self.query_history)}"))
    
    def export_results(self):
        """تصدير النتائج"""
        if self.results_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", format_arabic_text("لا توجد نتائج للتصدير"))
            return
        
        filename, _ = QFileDialog.getSaveFileName(self, "تصدير النتائج", "", "CSV Files (*.csv)")
        if filename:
            # تصدير إلى CSV
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    # كتابة العناوين
                    headers = []
                    for col in range(self.results_table.columnCount()):
                        headers.append(self.results_table.horizontalHeaderItem(col).text())
                    f.write(','.join(headers) + '\n')
                    
                    # كتابة البيانات
                    for row in range(self.results_table.rowCount()):
                        row_data = []
                        for col in range(self.results_table.columnCount()):
                            item = self.results_table.item(row, col)
                            row_data.append(item.text() if item else '')
                        f.write(','.join(row_data) + '\n')
                
                QMessageBox.information(self, "نجح", format_arabic_text("تم تصدير النتائج بنجاح"))
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", format_arabic_text(f"فشل في التصدير: {str(e)}"))

def main():
    """اختبار المحرر"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق للنصوص العربية
    from arabic_text_helper import setup_arabic_application
    setup_arabic_application(app)
    
    # إنشاء المحرر
    editor = AdvancedSQLEditor()
    editor.resize(1000, 700)
    editor.show()
    
    print("🚀 تم تشغيل محرر SQL المتقدم")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
