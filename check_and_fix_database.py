#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص وإصلاح جدول S_ERP_SYSTEM في قاعدة البيانات
"""

import sys
from oracle_db_manager import OracleDBManager

def check_and_fix_database():
    """فحص وإصلاح قاعدة البيانات"""
    try:
        # إنشاء مدير قاعدة البيانات
        db = OracleDBManager()
        
        # الاتصال بقاعدة البيانات
        print("🔄 اختبار الاتصال بقاعدة البيانات...")
        success, message = db.test_connection()
        if not success:
            print(f"❌ فشل الاتصال بقاعدة البيانات: {message}")
            return False

        print("✅ تم الاتصال بقاعدة البيانات بنجاح")

        # تهيئة مجموعة الاتصالات
        print("🔄 تهيئة مجموعة الاتصالات...")
        if not db.initialize_connection_pool():
            print("❌ فشل في تهيئة مجموعة الاتصالات")
            return False

        print("✅ تم تهيئة مجموعة الاتصالات بنجاح")
        
        # 1. فحص بنية الجدول
        print("\n📋 فحص بنية جدول S_ERP_SYSTEM:")
        print("-" * 60)
        
        structure_query = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            DATA_LENGTH,
            NULLABLE,
            DATA_DEFAULT
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'S_ERP_SYSTEM'
        ORDER BY COLUMN_ID
        """
        
        success, columns = db.execute_query_safe(structure_query)

        if success and columns:
            print(f"📊 الجدول يحتوي على {len(columns)} عمود:")
            for col in columns:
                column_name = col.get('COLUMN_NAME', '')
                data_type = col.get('DATA_TYPE', '')
                data_length = col.get('DATA_LENGTH', '')
                nullable = col.get('NULLABLE', '')
                print(f"   • {column_name:<20} {data_type}({data_length}) {nullable}")
        else:
            print("❌ لم يتم العثور على الجدول")
            return False
        
        # 2. فحص البيانات الحالية
        print(f"\n📊 فحص البيانات الحالية:")
        print("-" * 60)
        
        count_query = "SELECT COUNT(*) as TOTAL_COUNT FROM S_ERP_SYSTEM"
        success, result = db.execute_query_safe(count_query)

        if success and result:
            total_count = result[0].get('TOTAL_COUNT', 0)
            print(f"📈 إجمالي السجلات الحالية: {total_count}")
        
        # 3. فحص وجود نظام الإعدادات العامة
        print(f"\n🔍 فحص وجود نظام الإعدادات العامة:")
        print("-" * 60)
        
        check_query = """
        SELECT * FROM S_ERP_SYSTEM 
        WHERE SYS_CODE = 'GENSETTINGS' OR SYS_NAME LIKE '%إعدادات%' OR SYS_NAME LIKE '%اعدادات%'
        """
        
        success, existing_settings = db.execute_query_safe(check_query)

        if success and existing_settings:
            print(f"✅ تم العثور على {len(existing_settings)} نظام إعدادات موجود:")
            for system in existing_settings:
                sys_no = system.get('SYS_NO', '')
                sys_code = system.get('SYS_CODE', '')
                sys_name = system.get('SYS_NAME', '')
                print(f"   • {sys_no}: {sys_name} [{sys_code}]")
        else:
            print("❌ لا يوجد نظام إعدادات عامة في قاعدة البيانات")
        
        # 4. إدراج نظام الإعدادات العامة
        print(f"\n🔄 إدراج نظام الإعدادات العامة:")
        print("-" * 60)
        
        # البحث عن أعلى SYS_NO
        max_query = "SELECT NVL(MAX(SYS_NO), 0) as MAX_ID FROM S_ERP_SYSTEM"
        success, max_result = db.execute_query_safe(max_query)
        max_id = max_result[0].get('MAX_ID', 0) if success and max_result else 0
        
        print(f"📊 أعلى SYS_NO حالي: {max_id}")
        
        # تحديد IDs للأنظمة الجديدة
        main_system_id = max_id + 1
        subsystem_start_id = max_id + 2
        
        print(f"🆔 ID النظام الرئيسي الجديد: {main_system_id}")
        print(f"🆔 IDs الأنظمة الفرعية: {subsystem_start_id} - {subsystem_start_id + 11}")
        
        # إدراج النظام الرئيسي
        main_insert_query = """
        INSERT INTO S_ERP_SYSTEM (
            SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE, FORM_NO
        ) VALUES (
            :sys_no, :sys_code, :sys_name, :sys_parnt, :ordr_no, :inactive, :form_no
        )
        """
        
        main_system_data = {
            'sys_no': main_system_id,
            'sys_code': 'GENSETTINGS',
            'sys_name': 'نظام الإعدادات العامة الشامل',
            'sys_parnt': 1,  # تحت النظام الرئيسي
            'ordr_no': main_system_id,
            'inactive': 0,
            'form_no': 'general_settings_system.py'
        }
        
        try:
            success, result = db.execute_query_safe(main_insert_query, main_system_data, fetch_all=False)
            if success:
                print(f"✅ تم إدراج النظام الرئيسي: {main_system_data['sys_name']}")
            else:
                print(f"❌ فشل في إدراج النظام الرئيسي: {result}")
                return False
        except Exception as e:
            print(f"❌ فشل في إدراج النظام الرئيسي: {e}")
            return False
        
        # إدراج الأنظمة الفرعية
        subsystems = [
            ('APPEARANCE', 'إعدادات المظهر', '🎨'),
            ('COMPANY', 'بيانات الشركة', '🏢'),
            ('CURRENCIES', 'إعدادات العملات', '💰'),
            ('FISCALYEAR', 'السنة المالية', '📅'),
            ('LANGUAGE', 'إعدادات اللغة', '🌐'),
            ('SECURITY', 'إعدادات الأمان', '🔐'),
            ('EMAIL', 'إعدادات البريد الإلكتروني', '📧'),
            ('PRINTING', 'إعدادات الطباعة', '🖨️'),
            ('REPORTS', 'إعدادات التقارير', '📊'),
            ('BACKUPSETTINGS', 'إعدادات النسخ الاحتياطي', '🔄'),
            ('SYSTEMSETTINGS', 'إعدادات النظام', '⚙️'),
            ('NOTIFICATIONS', 'إعدادات الإشعارات', '📱')
        ]
        
        print(f"\n📂 إدراج الأنظمة الفرعية ({len(subsystems)} نظام):")
        
        for i, (code, name, icon) in enumerate(subsystems):
            subsystem_id = subsystem_start_id + i
            subsystem_data = {
                'sys_no': subsystem_id,
                'sys_code': code,
                'sys_name': name,
                'sys_parnt': main_system_id,  # تحت النظام الرئيسي للإعدادات
                'ordr_no': i + 1,
                'inactive': 0,
                'form_no': 'general_settings_system.py'
            }
            
            try:
                success, result = db.execute_query_safe(main_insert_query, subsystem_data, fetch_all=False)
                if success:
                    print(f"   ✅ {i+1:2d}. {icon} {name} [{code}]")
                else:
                    print(f"   ❌ {i+1:2d}. فشل في إدراج {name}: {result}")
            except Exception as e:
                print(f"   ❌ {i+1:2d}. فشل في إدراج {name}: {e}")
        
        # 5. التحقق من الإدراج
        print(f"\n🔍 التحقق من الإدراج:")
        print("-" * 60)
        
        verify_query = """
        SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO
        FROM S_ERP_SYSTEM 
        WHERE SYS_NO >= :start_id
        ORDER BY SYS_NO
        """
        
        success, verify_result = db.execute_query_safe(verify_query, {'start_id': main_system_id})
        
        if success and verify_result:
            print(f"✅ تم إدراج {len(verify_result)} نظام بنجاح:")
            for system in verify_result:
                sys_no = system.get('SYS_NO', '')
                sys_code = system.get('SYS_CODE', '')
                sys_name = system.get('SYS_NAME', '')
                sys_parnt = system.get('SYS_PARNT', '')
                ordr_no = system.get('ORDR_NO', '')
                
                if sys_parnt == main_system_id:
                    print(f"   📂 {sys_no}: {sys_name} [{sys_code}] (فرعي)")
                else:
                    print(f"   🏗️ {sys_no}: {sys_name} [{sys_code}] (رئيسي)")
        
        # 6. إحصائيات نهائية
        success, final_count_result = db.execute_query_safe(count_query)
        if success and final_count_result:
            final_count = final_count_result[0].get('TOTAL_COUNT', 0)
            added_count = final_count - total_count
            print(f"\n📊 الإحصائيات النهائية:")
            print(f"   📈 السجلات قبل الإدراج: {total_count}")
            print(f"   📈 السجلات بعد الإدراج: {final_count}")
            print(f"   ➕ السجلات المضافة: {added_count}")
        
        # قطع الاتصال
        db.close_connections()
        print("\n✅ تم قطع الاتصال بقاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العملية: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - فحص وإصلاح جدول S_ERP_SYSTEM")
    print("=" * 80)
    
    success = check_and_fix_database()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
        print("✅ نظام الإعدادات العامة مدرج الآن في الجدول")
        print("🚀 يمكنك الآن استخدام النظام من النافذة الرئيسية")
    else:
        print("❌ فشل في إصلاح قاعدة البيانات")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
