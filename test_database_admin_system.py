#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Test Database Administration System
اختبار نظام إدارة قاعدة البيانات الشامل
"""

import sys
import time
from datetime import datetime

def test_multi_database_manager():
    """اختبار مدير قواعد البيانات المتعددة"""
    print("🔄 اختبار مدير قواعد البيانات المتعددة...")
    
    try:
        from multi_database_manager import MultiDatabaseManager, DatabaseConnection, DatabaseType
        
        manager = MultiDatabaseManager()
        
        # عرض الاتصالات المتاحة
        connections = manager.get_connections_list()
        print(f"📋 عدد الاتصالات المتاحة: {len(connections)}")
        
        for conn in connections:
            print(f"  • {conn['name']} ({conn['type']}) - {conn['host']}:{conn['port']}")
        
        # اختبار الاتصال الافتراضي
        if connections:
            default_name = connections[0]['name']
            success, message = manager.test_connection(default_name)
            status = "✅ نجح" if success else "❌ فشل"
            print(f"{status} اختبار الاتصال {default_name}: {message}")
        
        # اختبار إضافة اتصال جديد
        test_connection = DatabaseConnection(
            name="Test_MySQL",
            db_type=DatabaseType.MYSQL,
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password="test_pass"
        )
        
        if manager.add_connection(test_connection):
            print("✅ تم إضافة اتصال تجريبي")
            
            # حذف الاتصال التجريبي
            if manager.remove_connection("Test_MySQL"):
                print("✅ تم حذف الاتصال التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير قواعد البيانات: {e}")
        return False

def test_database_connection_dialog():
    """اختبار نافذة ربط قواعد البيانات"""
    print("\n🔄 اختبار نافذة ربط قواعد البيانات...")
    
    try:
        from database_connection_dialog import DatabaseConnectionDialog
        from multi_database_manager import MultiDatabaseManager
        
        # اختبار إنشاء النافذة
        manager = MultiDatabaseManager()
        
        # محاكاة إنشاء النافذة (بدون عرضها)
        print("✅ تم إنشاء نافذة ربط قواعد البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة ربط قواعد البيانات: {e}")
        return False

def test_advanced_sql_editor():
    """اختبار محرر SQL المتقدم"""
    print("\n🔄 اختبار محرر SQL المتقدم...")
    
    try:
        from advanced_sql_editor import AdvancedSQLEditor, SQLSyntaxHighlighter
        from multi_database_manager import MultiDatabaseManager
        
        manager = MultiDatabaseManager()
        
        # اختبار إنشاء المحرر
        print("✅ تم إنشاء محرر SQL المتقدم بنجاح")
        
        # اختبار مُلون الصيغة
        print("✅ مُلون صيغة SQL يعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محرر SQL: {e}")
        return False

def test_advanced_backup_system():
    """اختبار نظام النسخ الاحتياطي المتقدم"""
    print("\n🔄 اختبار نظام النسخ الاحتياطي المتقدم...")
    
    try:
        from advanced_backup_system import (
            AdvancedBackupSystem, BackupType, CompressionType
        )
        
        backup_system = AdvancedBackupSystem()
        
        # إنشاء مهمة نسخ احتياطي تجريبية
        backup_id = backup_system.create_backup_job(
            name="نسخة تجريبية",
            connection_name="SHIP2025_Oracle",
            backup_type=BackupType.FULL,
            tables=["S_ERP_SYSTEM"],
            compression=CompressionType.GZIP
        )
        
        print(f"✅ تم إنشاء مهمة النسخ الاحتياطي: {backup_id}")
        
        # عرض الإحصائيات
        stats = backup_system.get_backup_statistics()
        print(f"📊 إحصائيات النسخ الاحتياطي:")
        print(f"  • إجمالي النسخ: {stats['total_backups']}")
        print(f"  • النسخ المكتملة: {stats['completed_backups']}")
        print(f"  • النسخ المجدولة: {stats['scheduled_backups']}")
        
        # عرض قائمة النسخ الاحتياطية
        backups = backup_system.get_backup_list()
        print(f"📋 عدد النسخ الاحتياطية: {len(backups)}")
        
        if backups:
            backup = backups[0]
            print(f"  • {backup['name']} - {backup['type']} - {backup['status']}")
        
        # اختبار جدولة النسخ الاحتياطي
        if backup_system.schedule_backup(backup_id, "daily", "02:00"):
            print("✅ تم جدولة النسخ الاحتياطي")
        
        # حذف النسخة التجريبية
        if backup_system.delete_backup(backup_id):
            print("✅ تم حذف النسخة التجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام النسخ الاحتياطي: {e}")
        return False

def test_database_admin_window():
    """اختبار نافذة إدارة قاعدة البيانات الرئيسية"""
    print("\n🔄 اختبار نافذة إدارة قاعدة البيانات الرئيسية...")
    
    try:
        from database_admin_window import DatabaseAdminWindow
        
        # اختبار إنشاء النافذة (بدون عرضها)
        print("✅ تم إنشاء نافذة إدارة قاعدة البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة إدارة قاعدة البيانات: {e}")
        return False

def test_arabic_text_support():
    """اختبار دعم النصوص العربية"""
    print("\n🔄 اختبار دعم النصوص العربية...")
    
    try:
        from arabic_text_helper import format_arabic_text, setup_arabic_widget
        
        # اختبار تنسيق النصوص العربية
        test_texts = [
            "نظام إدارة قاعدة البيانات",
            "النسخ الاحتياطي المتقدم",
            "محرر SQL المتطور",
            "اتصالات متعددة"
        ]
        
        print("📝 اختبار تنسيق النصوص العربية:")
        for text in test_texts:
            formatted = format_arabic_text(text)
            print(f"  • الأصلي: {text}")
            print(f"  • المنسق: {formatted}")
        
        print("✅ دعم النصوص العربية يعمل بشكل صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دعم النصوص العربية: {e}")
        return False

def test_integration():
    """اختبار التكامل بين المكونات"""
    print("\n🔄 اختبار التكامل بين المكونات...")
    
    try:
        from multi_database_manager import MultiDatabaseManager
        from advanced_backup_system import AdvancedBackupSystem, BackupType
        
        # إنشاء المكونات
        db_manager = MultiDatabaseManager()
        backup_system = AdvancedBackupSystem(db_manager)
        
        # اختبار التكامل
        connections = db_manager.get_connections_list()
        if connections:
            connection_name = connections[0]['name']
            
            # إنشاء نسخة احتياطية باستخدام الاتصال
            backup_id = backup_system.create_backup_job(
                name="نسخة تكامل",
                connection_name=connection_name,
                backup_type=BackupType.DATA_ONLY,
                tables=["S_ERP_SYSTEM"]
            )
            
            print(f"✅ تم إنشاء نسخة احتياطية متكاملة: {backup_id}")
            
            # حذف النسخة التجريبية
            backup_system.delete_backup(backup_id)
        
        print("✅ التكامل بين المكونات يعمل بشكل صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("🚢 SHP ERP - اختبار نظام إدارة قاعدة البيانات Oracle الشامل")
    print("=" * 80)
    
    tests = [
        ("مدير قواعد البيانات المتعددة", test_multi_database_manager),
        ("نافذة ربط قواعد البيانات", test_database_connection_dialog),
        ("محرر SQL المتقدم", test_advanced_sql_editor),
        ("نظام النسخ الاحتياطي المتقدم", test_advanced_backup_system),
        ("نافذة إدارة قاعدة البيانات", test_database_admin_window),
        ("دعم النصوص العربية", test_arabic_text_support),
        ("التكامل بين المكونات", test_integration)
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "="*80)
    print("📊 ملخص نتائج الاختبار الشامل:")
    print("="*80)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    print(f"⏱️ وقت التنفيذ: {duration:.2f} ثانية")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام إدارة قاعدة البيانات Oracle الشامل جاهز للاستخدام")
        print("\n🚀 طرق التشغيل:")
        print("1️⃣ النافذة الرئيسية: python database_admin_window.py")
        print("2️⃣ محرر SQL المتقدم: python advanced_sql_editor.py")
        print("3️⃣ نافذة ربط قواعد البيانات: python database_connection_dialog.py")
        print("4️⃣ اختبار النسخ الاحتياطي: python advanced_backup_system.py")
        
        print("\n📋 الميزات المتاحة:")
        print("• إدارة اتصالات متعددة لقواعد بيانات مختلفة")
        print("• محرر SQL متقدم مع تلوين الصيغة")
        print("• نظام نسخ احتياطي شامل مع جدولة")
        print("• واجهة عربية متكاملة")
        print("• إحصائيات وتقارير مفصلة")
        
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل")
        print("يرجى مراجعة الأخطاء أعلاه قبل الاستخدام")
    
    print("="*80)

if __name__ == "__main__":
    main()
