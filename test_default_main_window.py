#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النافذة الرئيسية الافتراضية (بدون قاعدة البيانات)
"""

import sys

def test_default_main_window():
    """اختبار النافذة الرئيسية الافتراضية"""
    try:
        print("🚢 SHP ERP - اختبار النافذة الرئيسية الافتراضية")
        print("=" * 70)
        
        # اختبار الاستيراد
        print("🔄 اختبار استيراد المكونات...")
        
        from PySide6.QtWidgets import QApplication
        print("✅ تم استيراد PySide6")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ تم إنشاء التطبيق")
        
        # اختبار استيراد النافذة الرئيسية الافتراضية
        from main_window import SHPERPMainWindow
        print("✅ تم استيراد النافذة الرئيسية الافتراضية")
        
        # إنشاء النافذة الرئيسية
        main_window = SHPERPMainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من البيانات الافتراضية
        if hasattr(main_window, 'systems_data') and main_window.systems_data:
            print(f"✅ تم تحميل {len(main_window.systems_data)} نظام افتراضي")
            
            # التحقق من وجود النظام STP
            stp_system = None
            for system in main_window.systems_data:
                if system.get('SYSTEM_CODE') == 'STP':
                    stp_system = system
                    break
            
            if stp_system:
                print(f"✅ تم العثور على النظام STP: {stp_system.get('SYSTEM_NAME')}")
            else:
                print("⚠️ لم يتم العثور على النظام STP")
        else:
            print("❌ لم يتم تحميل البيانات الافتراضية")
            return False
        
        # اختبار دالة تشغيل النظام المتقدم
        if hasattr(main_window, 'launch_advanced_settings_system'):
            print("✅ دالة تشغيل النظام المتقدم موجودة")
        else:
            print("❌ دالة تشغيل النظام المتقدم غير موجودة")
            return False
        
        # عرض النافذة
        main_window.show()
        print("✅ تم عرض النافذة")
        
        print("\n🎉 نجح الاختبار!")
        print("✅ النافذة الرئيسية الافتراضية تعمل بنجاح")
        print("🖥️ النافذة مفتوحة الآن مع البيانات الافتراضية")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    success = test_default_main_window()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 تم إلغاء ربط قاعدة البيانات بنجاح!")
        print("✅ النافذة الرئيسية تعمل الآن في الوضع الافتراضي")
        
        print("\n📋 الميزات المتاحة:")
        print("• 🏗️ بيانات افتراضية للأنظمة")
        print("• 📊 إحصائيات النظام")
        print("• 🔧 نظام الإعدادات المتقدم (STP)")
        print("• 🎨 واجهة حديثة ومتجاوبة")
        print("• ⚡ أداء سريع بدون قاعدة بيانات")
        
        print("\n📋 للاستخدام:")
        print("1️⃣ شغل النافذة الرئيسية: python main_window.py")
        print("2️⃣ تصفح الأنظمة من الشريط الجانبي")
        print("3️⃣ انقر مزدوج على 'نظام الإعدادات العامة (STP)'")
        print("4️⃣ ستفتح نافذة الإعدادات المتقدمة! ✨")
        
        print("\n🔄 للعودة لقاعدة البيانات:")
        print("استخدم: main_window_with_database.py")
        
    else:
        print("❌ لا تزال هناك مشاكل تحتاج إلى حل")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
