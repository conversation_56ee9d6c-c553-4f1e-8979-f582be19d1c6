#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Add Database Administration System to Main Systems Tree (Fixed)
إضافة نظام إدارة قاعدة البيانات إلى شجرة الأنظمة الرئيسية (محدث)
"""

import sys

def add_database_admin_system():
    """إضافة نظام إدارة قاعدة البيانات إلى جدول S_ERP_SYSTEM"""
    print("🔄 إضافة نظام إدارة قاعدة البيانات إلى الأنظمة الرئيسية...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        
        # الاتصال بقاعدة البيانات
        success = db.connect(
            username="ship2025",
            password="ys123",
            host="localhost",
            port=1521,
            service_name="orcl"
        )
        
        if not success:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # التحقق من وجود النظام مسبقاً
        check_query = """
        SELECT COUNT(*) as COUNT 
        FROM S_ERP_SYSTEM 
        WHERE SYS_CODE = 'DBADM' OR SYS_NAME LIKE '%إدارة قاعدة البيانات%'
        """
        
        result = db.execute_query(check_query)
        if result and result[0]['COUNT'] > 0:
            print("⚠️ نظام إدارة قاعدة البيانات موجود مسبقاً")
            
            # عرض النظام الموجود
            existing_query = """
            SELECT SYS_NO, SYS_CODE, SYS_NAME 
            FROM S_ERP_SYSTEM 
            WHERE SYS_CODE = 'DBADM' OR SYS_NAME LIKE '%إدارة قاعدة البيانات%'
            """
            existing_systems = db.execute_query(existing_query)
            if existing_systems:
                for system in existing_systems:
                    print(f"  📋 {system['SYS_NO']}: {system['SYS_NAME']} [{system['SYS_CODE']}]")
            
            db.disconnect()
            return True
        
        # الحصول على أعلى رقم نظام
        max_query = "SELECT NVL(MAX(SYS_NO), 0) as MAX_NO FROM S_ERP_SYSTEM"
        max_result = db.execute_query(max_query)
        next_sys_no = (max_result[0]['MAX_NO'] if max_result else 0) + 1
        
        print(f"📊 رقم النظام الجديد: {next_sys_no}")
        
        # إدراج نظام إدارة قاعدة البيانات الرئيسي
        insert_query = """
        INSERT INTO S_ERP_SYSTEM (
            SYS_NO,
            SYS_CODE,
            SYS_NAME,
            SYS_PARNT,
            ORDR_NO,
            INACTIVE,
            FORM_NO
        ) VALUES (
            :sys_no,
            :sys_code,
            :sys_name,
            :sys_parent,
            :order_no,
            :inactive,
            :form_no
        )
        """
        
        # بيانات النظام الرئيسي
        main_system_data = {
            'sys_no': next_sys_no,
            'sys_code': 'DBADM',
            'sys_name': '🗃️ نظام إدارة قاعدة البيانات Oracle',
            'sys_parent': 1,  # تحت النظام الجذر (ERP)
            'order_no': 999,  # في النهاية
            'inactive': 0,    # نشط
            'form_no': 999    # رقم النموذج
        }
        
        # تنفيذ الإدراج
        cursor = db.connection.cursor()
        cursor.execute(insert_query, main_system_data)
        db.connection.commit()
        cursor.close()
        
        print("✅ تم إدراج النظام الرئيسي بنجاح")
        print(f"📋 رقم النظام: {next_sys_no}")
        print(f"🏷️ رمز النظام: {main_system_data['sys_code']}")
        print(f"📝 اسم النظام: {main_system_data['sys_name']}")
        
        # إضافة الأنظمة الفرعية
        subsystems = [
            {
                'code': 'DBCON',
                'name': '🔗 إدارة الاتصالات',
                'order': 10,
                'form': 1001
            },
            {
                'code': 'SQLED',
                'name': '📝 محرر SQL المتقدم',
                'order': 20,
                'form': 1002
            },
            {
                'code': 'DBBAK',
                'name': '💾 النسخ الاحتياطي',
                'order': 30,
                'form': 1003
            },
            {
                'code': 'DBSTA',
                'name': '📊 إحصائيات قاعدة البيانات',
                'order': 40,
                'form': 1004
            },
            {
                'code': 'DBTBL',
                'name': '📋 إدارة الجداول',
                'order': 50,
                'form': 1005
            }
        ]
        
        print("\n🔄 إضافة الأنظمة الفرعية...")
        
        for i, subsystem in enumerate(subsystems):
            sub_sys_no = next_sys_no + i + 1
            
            sub_system_data = {
                'sys_no': sub_sys_no,
                'sys_code': subsystem['code'],
                'sys_name': subsystem['name'],
                'sys_parent': next_sys_no,  # تحت نظام إدارة قاعدة البيانات
                'order_no': subsystem['order'],
                'inactive': 0,  # نشط
                'form_no': subsystem['form']
            }
            
            cursor = db.connection.cursor()
            cursor.execute(insert_query, sub_system_data)
            db.connection.commit()
            cursor.close()
            
            print(f"  ✅ {subsystem['name']} - رقم {sub_sys_no}")
        
        print(f"\n🎉 تم إضافة نظام إدارة قاعدة البيانات مع {len(subsystems)} نظام فرعي")
        
        # التحقق من الإدراج
        verify_query = """
        SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE
        FROM S_ERP_SYSTEM 
        WHERE SYS_NO >= :start_no
        ORDER BY SYS_NO
        """
        
        verification = db.execute_query(verify_query, {'start_no': next_sys_no})
        if verification:
            print("\n📋 الأنظمة المضافة:")
            for system in verification:
                status = "نشط" if system['INACTIVE'] == 0 else "غير نشط"
                parent_info = f" (تحت {system['SYS_PARNT']})" if system['SYS_PARNT'] != 1 else ""
                print(f"  📌 {system['SYS_NO']}: {system['SYS_NAME']} [{system['SYS_CODE']}] - {status}{parent_info}")
        
        db.disconnect()
        print("\n✅ تم إنجاز العملية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة النظام: {e}")
        return False

def update_main_window_for_database_admin():
    """تحديث النافذة الرئيسية لدعم تشغيل نظام إدارة قاعدة البيانات"""
    print("\n🔄 تحديث النافذة الرئيسية لدعم نظام إدارة قاعدة البيانات...")
    
    try:
        # قراءة ملف النافذة الرئيسية
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود معالج النظام
        if 'def launch_database_admin' in content:
            print("✅ معالج نظام إدارة قاعدة البيانات موجود مسبقاً")
        else:
            # إضافة معالج تشغيل نظام إدارة قاعدة البيانات
            handler_code = '''
    def launch_database_admin(self, system_data=None):
        """تشغيل نظام إدارة قاعدة البيانات"""
        try:
            import subprocess
            import os
            
            # تحديد النظام المراد تشغيله بناءً على رمز النظام
            if system_data:
                sys_code = system_data.get('SYS_CODE', '')
                
                if sys_code == 'DBADM':
                    # النظام الرئيسي
                    script_path = 'run_database_admin.py'
                elif sys_code == 'DBCON':
                    # إدارة الاتصالات
                    script_path = 'database_connection_dialog.py'
                elif sys_code == 'SQLED':
                    # محرر SQL
                    script_path = 'advanced_sql_editor.py'
                elif sys_code == 'DBBAK':
                    # النسخ الاحتياطي
                    script_path = 'advanced_backup_system.py'
                elif sys_code == 'DBSTA':
                    # الإحصائيات
                    script_path = 'database_admin_window.py'
                elif sys_code == 'DBTBL':
                    # إدارة الجداول
                    script_path = 'database_admin_window.py'
                else:
                    script_path = 'run_database_admin.py'
            else:
                script_path = 'run_database_admin.py'
            
            # التحقق من وجود الملف
            if not os.path.exists(script_path):
                self.statusBar().showMessage(
                    self.format_arabic_text(f"⚠️ الملف غير موجود: {script_path}")
                )
                return
            
            # تشغيل النظام
            subprocess.Popen([sys.executable, script_path], 
                           cwd=os.getcwd(),
                           creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
            
            system_name = system_data.get('SYS_NAME', 'نظام إدارة قاعدة البيانات') if system_data else 'نظام إدارة قاعدة البيانات'
            self.statusBar().showMessage(
                self.format_arabic_text(f"🚀 تم تشغيل {system_name}")
            )
            
        except Exception as e:
            self.statusBar().showMessage(
                self.format_arabic_text(f"❌ خطأ في تشغيل النظام: {str(e)}")
            )
'''
            
            # البحث عن مكان مناسب لإضافة المعالج
            if 'def on_system_selected' in content:
                # إضافة المعالج قبل on_system_selected
                insertion_point = content.find('    def on_system_selected')
                if insertion_point != -1:
                    new_content = content[:insertion_point] + handler_code + '\n' + content[insertion_point:]
                    
                    # كتابة الملف المحدث
                    with open('main_window.py', 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    print("✅ تم إضافة معالج نظام إدارة قاعدة البيانات")
        
        # تحديث معالج اختيار النظام
        if 'def on_system_selected' in content:
            # قراءة الملف المحدث
            with open('main_window.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن معالج اختيار النظام وتحديثه
            if 'system_code = system_data.get' not in content:
                # إضافة معالجة خاصة لأنظمة قاعدة البيانات
                update_code = '''
        # معالجة خاصة لأنظمة قاعدة البيانات
        system_code = system_data.get('SYS_CODE', '')
        if system_code in ['DBADM', 'DBCON', 'SQLED', 'DBBAK', 'DBSTA', 'DBTBL']:
            self.launch_database_admin(system_data)
            return
'''
                
                # البحث عن بداية معالج on_system_selected
                pattern = 'def on_system_selected(self, item, column):'
                if pattern in content:
                    # البحث عن نهاية التعريف وبداية الكود
                    start_pos = content.find(pattern)
                    if start_pos != -1:
                        # البحث عن السطر التالي بعد التعريف
                        next_line_pos = content.find('\n', start_pos)
                        if next_line_pos != -1:
                            # البحث عن بداية الكود الفعلي
                            code_start = content.find('if not item:', next_line_pos)
                            if code_start != -1:
                                # إدراج الكود الجديد
                                new_content = content[:code_start] + update_code + '\n        ' + content[code_start:]
                                
                                with open('main_window.py', 'w', encoding='utf-8') as f:
                                    f.write(new_content)
                                
                                print("✅ تم تحديث معالج اختيار النظام")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث النافذة الرئيسية: {e}")
        return False

def test_system_integration():
    """اختبار تكامل النظام مع الشجرة الرئيسية"""
    print("\n🔄 اختبار تكامل النظام مع الشجرة الرئيسية...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        success = db.connect(
            username="ship2025",
            password="ys123",
            host="localhost",
            port=1521,
            service_name="orcl"
        )
        
        if not success:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # البحث عن نظام إدارة قاعدة البيانات
        search_query = """
        SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE
        FROM S_ERP_SYSTEM 
        WHERE SYS_CODE LIKE 'DB%' OR SYS_NAME LIKE '%قاعدة البيانات%'
        ORDER BY SYS_NO
        """
        
        systems = db.execute_query(search_query)
        if systems:
            print(f"✅ تم العثور على {len(systems)} نظام متعلق بقاعدة البيانات:")
            for system in systems:
                status = "نشط" if system['INACTIVE'] == 0 else "غير نشط"
                parent_info = f" (تحت {system['SYS_PARNT']})" if system['SYS_PARNT'] != 1 else ""
                print(f"  📌 {system['SYS_NO']}: {system['SYS_NAME']} [{system['SYS_CODE']}] - {status}{parent_info}")
        else:
            print("⚠️ لم يتم العثور على أنظمة قاعدة البيانات")
        
        # اختبار تحميل الأنظمة في التطبيق
        all_systems_query = "SELECT COUNT(*) as TOTAL FROM S_ERP_SYSTEM WHERE INACTIVE = 0"
        total_result = db.execute_query(all_systems_query)
        if total_result:
            total_systems = total_result[0]['TOTAL']
            print(f"\n📊 إجمالي الأنظمة النشطة: {total_systems}")
        
        db.disconnect()
        return len(systems) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - إضافة نظام إدارة قاعدة البيانات إلى الأنظمة الرئيسية (محدث)")
    print("=" * 90)
    
    tasks = [
        ("إضافة نظام إدارة قاعدة البيانات", add_database_admin_system),
        ("تحديث النافذة الرئيسية", update_main_window_for_database_admin),
        ("اختبار التكامل", test_system_integration)
    ]
    
    results = {}
    
    for task_name, task_func in tasks:
        print(f"\n{'='*15} {task_name} {'='*15}")
        try:
            results[task_name] = task_func()
        except Exception as e:
            print(f"❌ خطأ في {task_name}: {e}")
            results[task_name] = False
    
    # ملخص النتائج
    print("\n" + "="*90)
    print("📊 ملخص النتائج:")
    print("="*90)
    
    passed = 0
    total = len(tasks)
    
    for task_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {task_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} مهمة نجحت")
    
    if passed == total:
        print("\n🎉 تم دمج نظام إدارة قاعدة البيانات بنجاح!")
        print("✅ النظام متاح الآن في شجرة الأنظمة الرئيسية")
        print("\n📋 للوصول للنظام:")
        print("1️⃣ شغل التطبيق الرئيسي: python main_window.py")
        print("2️⃣ ابحث عن 🗃️ نظام إدارة قاعدة البيانات Oracle")
        print("3️⃣ انقر على النظام أو أحد الأنظمة الفرعية")
        
        print("\n🔧 الأنظمة الفرعية المتاحة:")
        print("  🔗 إدارة الاتصالات")
        print("  📝 محرر SQL المتقدم")
        print("  💾 النسخ الاحتياطي")
        print("  📊 إحصائيات قاعدة البيانات")
        print("  📋 إدارة الجداول")
    else:
        print(f"\n⚠️ {total - passed} مهمة فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("="*90)

if __name__ == "__main__":
    main()
