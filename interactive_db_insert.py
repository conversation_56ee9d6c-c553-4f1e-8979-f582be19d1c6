#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدراج تفاعلي لنظام الإعدادات العامة
"""

import cx_Oracle

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - إدراج نظام الإعدادات العامة")
    print("=" * 60)
    
    try:
        # الاتصال
        print("🔄 الاتصال بقاعدة البيانات...")
        conn = cx_Oracle.connect('ship2025', 'ys123', 'localhost:1521/orcl')
        cursor = conn.cursor()
        print("✅ تم الاتصال بنجاح")
        
        # فحص البيانات الحالية
        cursor.execute("SELECT COUNT(*) FROM S_ERP_SYSTEM")
        count = cursor.fetchone()[0]
        print(f"📊 عدد السجلات الحالية: {count}")
        
        # فحص وجود نظام الإعدادات
        cursor.execute("SELECT * FROM S_ERP_SYSTEM WHERE SYS_CODE = 'GENST'")
        existing = cursor.fetchall()
        
        if existing:
            print("⚠️ نظام الإعدادات العامة موجود بالفعل!")
            for row in existing:
                print(f"   • {row[0]}: {row[2]} [{row[1]}]")
            
            response = input("\n❓ هل تريد المتابعة؟ (y/n): ")
            if response.lower() != 'y':
                print("❌ تم الإلغاء")
                return
        
        # البحث عن أعلى ID
        cursor.execute("SELECT NVL(MAX(SYS_NO), 0) FROM S_ERP_SYSTEM")
        max_id = cursor.fetchone()[0]
        new_id = max_id + 1
        print(f"🆔 ID الجديد: {new_id}")
        
        # إدراج النظام الرئيسي
        print("🔄 إدراج النظام الرئيسي...")
        cursor.execute("""
            INSERT INTO S_ERP_SYSTEM (SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE)
            VALUES (:1, :2, :3, :4, :5, :6)
        """, (new_id, 'GENST', 'نظام الإعدادات العامة الشامل', 1, new_id, 0))
        
        print("✅ تم إدراج النظام الرئيسي")
        
        # إدراج الأنظمة الفرعية
        subsystems = [
            ('APPR', 'إعدادات المظهر'),
            ('COMP', 'بيانات الشركة'),
            ('CURR', 'إعدادات العملات'),
            ('FISC', 'السنة المالية'),
            ('LANG', 'إعدادات اللغة'),
            ('SECR', 'إعدادات الأمان'),
            ('MAIL', 'إعدادات البريد الإلكتروني'),
            ('PRNT', 'إعدادات الطباعة'),
            ('REPT', 'إعدادات التقارير'),
            ('BKUP', 'إعدادات النسخ الاحتياطي'),
            ('SYST', 'إعدادات النظام'),
            ('NOTF', 'إعدادات الإشعارات')
        ]
        
        print(f"🔄 إدراج {len(subsystems)} نظام فرعي...")
        
        for i, (code, name) in enumerate(subsystems):
            sub_id = new_id + 1 + i
            cursor.execute("""
                INSERT INTO S_ERP_SYSTEM (SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE)
                VALUES (:1, :2, :3, :4, :5, :6)
            """, (sub_id, code, name, new_id, i + 1, 0))
            
            print(f"   ✅ {i+1:2d}. {name} [{code}]")
        
        # حفظ
        conn.commit()
        print("💾 تم حفظ جميع التغييرات")
        
        # التحقق
        cursor.execute(f"SELECT COUNT(*) FROM S_ERP_SYSTEM WHERE SYS_NO >= {new_id}")
        added_count = cursor.fetchone()[0]
        print(f"✅ تم إدراج {added_count} نظام بنجاح")
        
        # إغلاق
        cursor.close()
        conn.close()
        print("✅ تم إغلاق الاتصال")
        
        print("\n🎉 تم إدراج نظام الإعدادات العامة بنجاح!")
        print("🚀 يمكنك الآن استخدام النظام من النافذة الرئيسية")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
