#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from database_connection import get_db_connection

db = get_db_connection()
db.connect(username='ship2025', password='ys123', host='localhost', port=1521, service_name='orcl')

result = db.execute_query("SELECT * FROM S_ERP_SYSTEM WHERE SYS_CODE = 'DBCON' AND ROWNUM = 1")
if result:
    print('أعمدة قاعدة البيانات:')
    for key, value in result[0].items():
        print(f'{key}: {value}')

db.disconnect()
