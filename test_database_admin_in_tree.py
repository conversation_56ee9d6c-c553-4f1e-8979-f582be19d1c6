#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Test Database Admin System in Main Tree
اختبار ظهور نظام إدارة قاعدة البيانات في الشجرة الرئيسية
"""

def test_database_admin_in_systems_tree():
    """اختبار ظهور نظام إدارة قاعدة البيانات في شجرة الأنظمة"""
    print("🔄 اختبار ظهور نظام إدارة قاعدة البيانات في شجرة الأنظمة...")
    
    try:
        from erp_systems_manager import ERPSystemsManager
        
        # إنشاء مدير الأنظمة
        systems_manager = ERPSystemsManager()
        
        # الاتصال بقاعدة البيانات
        if not systems_manager.connect_to_database():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تحميل بيانات الأنظمة
        systems_data = systems_manager.load_systems_data()
        print(f"📋 تم تحميل {len(systems_data)} نظام")
        
        # البحث عن نظام إدارة قاعدة البيانات
        database_admin_systems = []
        for system in systems_data:
            if ('قاعدة البيانات' in system['SYSTEM_NAME'] or 
                system['SYSTEM_CODE'] in ['DBADM', 'DBCON', 'SQLED', 'DBBAK', 'DBSTA', 'DBTBL']):
                database_admin_systems.append(system)
        
        if database_admin_systems:
            print(f"✅ تم العثور على {len(database_admin_systems)} نظام متعلق بقاعدة البيانات:")
            
            # ترتيب الأنظمة حسب الرقم
            database_admin_systems.sort(key=lambda x: x['SYSTEM_NO'])
            
            for system in database_admin_systems:
                level_indent = "  " * (system.get('LEVEL', 1) - 1)
                parent_info = f" (تحت {system['PARENT_ID']})" if system.get('PARENT_ID') and system['PARENT_ID'] != 1 else ""
                print(f"{level_indent}📌 {system['SYSTEM_NO']}: {system['SYSTEM_NAME']} [{system['SYSTEM_CODE']}]{parent_info}")
        else:
            print("⚠️ لم يتم العثور على نظام إدارة قاعدة البيانات في الشجرة")
            
            # عرض آخر 10 أنظمة للتحقق
            print("\n📋 آخر 10 أنظمة في قاعدة البيانات:")
            last_systems = sorted(systems_data, key=lambda x: x['SYSTEM_NO'])[-10:]
            for system in last_systems:
                print(f"  📌 {system['SYSTEM_NO']}: {system['SYSTEM_NAME']} [{system['SYSTEM_CODE']}]")
        
        # بناء الشجرة الهرمية
        root_systems = systems_manager.get_root_systems()
        print(f"\n🌳 عدد الأنظمة الجذر: {len(root_systems)}")
        
        # البحث في الشجرة الهرمية
        def find_database_admin_in_tree(systems, level=0):
            found_systems = []
            for system in systems:
                if ('قاعدة البيانات' in system['name'] or 
                    'DBADM' in system.get('code', '')):
                    found_systems.append((system, level))
                
                # البحث في الأنظمة الفرعية
                if 'children' in system:
                    found_systems.extend(find_database_admin_in_tree(system['children'], level + 1))
            
            return found_systems
        
        found_in_tree = find_database_admin_in_tree(root_systems)
        if found_in_tree:
            print("✅ تم العثور على نظام إدارة قاعدة البيانات في الشجرة الهرمية:")
            for system, level in found_in_tree:
                level_indent = "  " * level
                print(f"{level_indent}🗃️ {system['name']} [{system.get('code', 'N/A')}]")
        
        systems_manager.disconnect()
        return len(database_admin_systems) > 0
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_main_window_with_database_admin():
    """اختبار النافذة الرئيسية مع نظام إدارة قاعدة البيانات"""
    print("\n🔄 اختبار النافذة الرئيسية مع نظام إدارة قاعدة البيانات...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from main_window import SHPERPMainWindow
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء النافذة الرئيسية
        window = SHPERPMainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من وجود معالج نظام إدارة قاعدة البيانات
        if hasattr(window, 'launch_database_admin'):
            print("✅ معالج نظام إدارة قاعدة البيانات متاح")
        else:
            print("⚠️ معالج نظام إدارة قاعدة البيانات غير متاح")
        
        # محاولة تحميل الأنظمة
        if hasattr(window, 'systems_manager'):
            if window.systems_manager.connect_to_database():
                systems_data = window.systems_manager.load_systems_data()
                print(f"✅ تم تحميل {len(systems_data)} نظام في النافذة الرئيسية")
                
                # البحث عن نظام إدارة قاعدة البيانات
                db_admin_count = sum(1 for system in systems_data 
                                   if 'قاعدة البيانات' in system['SYSTEM_NAME'] or 
                                      system['SYSTEM_CODE'] in ['DBADM', 'DBCON', 'SQLED', 'DBBAK', 'DBSTA', 'DBTBL'])
                
                if db_admin_count > 0:
                    print(f"✅ تم العثور على {db_admin_count} نظام متعلق بقاعدة البيانات في النافذة")
                else:
                    print("⚠️ لم يتم العثور على نظام إدارة قاعدة البيانات في النافذة")
                
                window.systems_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة الرئيسية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار ظهور نظام إدارة قاعدة البيانات في الشجرة الرئيسية")
    print("=" * 80)
    
    tests = [
        ("ظهور النظام في شجرة الأنظمة", test_database_admin_in_systems_tree),
        ("النافذة الرئيسية مع النظام", test_main_window_with_database_admin)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "="*80)
    print("📊 ملخص نتائج الاختبار:")
    print("="*80)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 نظام إدارة قاعدة البيانات متاح في الشجرة الرئيسية!")
        print("✅ يمكنك الآن الوصول إليه من التطبيق الرئيسي")
        print("\n📋 للوصول للنظام:")
        print("1️⃣ شغل: python main_window.py")
        print("2️⃣ ابحث عن: 🗃️ نظام إدارة قاعدة البيانات Oracle")
        print("3️⃣ انقر على النظام لتشغيله")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل")
        print("قد تحتاج لإعادة تشغيل التطبيق الرئيسي لرؤية التحديثات")
    
    print("="*80)

if __name__ == "__main__":
    main()
