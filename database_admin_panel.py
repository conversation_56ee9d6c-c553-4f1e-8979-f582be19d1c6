#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Database Administration Panel
لوحة إدارة قاعدة البيانات المتقدمة
"""

import sys
import json
import time
from datetime import datetime
from PySide6.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QTableWidget, QTableWidgetItem, QTextEdit, QPushButton,
    QLabel, QLineEdit, QComboBox, QSpinBox, QGroupBox, QSplitter,
    QMessageBox, QProgressBar, QHeaderView, QFrame, QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QTextCharFormat, QSyntaxHighlighter

from oracle_db_manager import get_db_manager
from advanced_db_features import BackupManager, PerformanceMonitor, DataValidator

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class SQLHighlighter(QSyntaxHighlighter):
    """مُلون SQL"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_highlighting_rules()
    
    def setup_highlighting_rules(self):
        """إعداد قواعد التلوين"""
        # كلمات SQL المحجوزة
        keyword_format = QTextCharFormat()
        keyword_format.setForeground(Qt.blue)
        keyword_format.setFontWeight(QFont.Bold)
        
        keywords = [
            'SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE',
            'CREATE', 'DROP', 'ALTER', 'TABLE', 'INDEX', 'VIEW',
            'AND', 'OR', 'NOT', 'NULL', 'IS', 'IN', 'LIKE',
            'ORDER', 'BY', 'GROUP', 'HAVING', 'DISTINCT', 'COUNT',
            'SUM', 'AVG', 'MAX', 'MIN', 'JOIN', 'INNER', 'LEFT',
            'RIGHT', 'OUTER', 'ON', 'AS', 'UNION', 'ALL'
        ]
        
        self.highlighting_rules = []
        for keyword in keywords:
            pattern = f'\\b{keyword}\\b'
            self.highlighting_rules.append((pattern, keyword_format))
    
    def highlightBlock(self, text):
        """تلوين النص"""
        for pattern, format in self.highlighting_rules:
            import re
            for match in re.finditer(pattern, text, re.IGNORECASE):
                start, end = match.span()
                self.setFormat(start, end - start, format)

class QueryExecutorThread(QThread):
    """خيط تنفيذ الاستعلامات"""
    
    query_finished = Signal(bool, object)
    progress_update = Signal(str)
    
    def __init__(self, query, params=None):
        super().__init__()
        self.query = query
        self.params = params
        self.db_manager = get_db_manager()
    
    def run(self):
        """تنفيذ الاستعلام"""
        try:
            self.progress_update.emit("جاري تنفيذ الاستعلام...")
            success, result = self.db_manager.execute_query_safe(self.query, self.params)
            self.query_finished.emit(success, result)
        except Exception as e:
            self.query_finished.emit(False, str(e))

class DatabaseStatsWidget(QWidget):
    """مكون إحصائيات قاعدة البيانات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = get_db_manager()
        self.init_ui()
        self.refresh_stats()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان
        title = QLabel("📊 إحصائيات قاعدة البيانات")
        title.setFont(QFont("Tahoma", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # جدول الإحصائيات
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(2)
        self.stats_table.setHorizontalHeaderLabels(["المؤشر", "القيمة"])
        self.stats_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.stats_table)
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث الإحصائيات")
        refresh_btn.clicked.connect(self.refresh_stats)
        layout.addWidget(refresh_btn)
    
    def refresh_stats(self):
        """تحديث الإحصائيات"""
        stats_queries = {
            "عدد الأنظمة الإجمالي": "SELECT COUNT(*) FROM S_ERP_SYSTEM",
            "عدد الأنظمة النشطة": "SELECT COUNT(*) FROM S_ERP_SYSTEM WHERE (INACTIVE = 0 OR INACTIVE IS NULL)",
            "عدد الأنظمة غير النشطة": "SELECT COUNT(*) FROM S_ERP_SYSTEM WHERE INACTIVE = 1",
            "عدد الأنظمة الجذر": "SELECT COUNT(*) FROM S_ERP_SYSTEM WHERE (SYS_PARNT IS NULL OR SYS_PARNT = 0)",
            "أعلى رقم نظام": "SELECT MAX(SYS_NO) FROM S_ERP_SYSTEM",
            "عدد الجداول": "SELECT COUNT(*) FROM USER_TABLES",
            "عدد المؤشرات": "SELECT COUNT(*) FROM USER_INDEXES",
            "تاريخ آخر تحديث": "SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') FROM DUAL"
        }
        
        self.stats_table.setRowCount(len(stats_queries))
        
        row = 0
        for stat_name, query in stats_queries.items():
            success, result = self.db_manager.execute_query_safe(query, fetch_all=False)
            
            self.stats_table.setItem(row, 0, QTableWidgetItem(stat_name))
            
            if success and result:
                value = str(list(result.values())[0]) if isinstance(result, dict) else str(result[0])
                self.stats_table.setItem(row, 1, QTableWidgetItem(value))
            else:
                self.stats_table.setItem(row, 1, QTableWidgetItem("خطأ"))
            
            row += 1

class QueryEditorWidget(QWidget):
    """مكون محرر الاستعلامات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = get_db_manager()
        self.query_thread = None
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات
        toolbar = QHBoxLayout()
        
        # أزرار سريعة
        quick_queries = [
            ("عرض جميع الأنظمة", "SELECT * FROM S_ERP_SYSTEM ORDER BY SYS_NO"),
            ("الأنظمة النشطة", "SELECT * FROM S_ERP_SYSTEM WHERE (INACTIVE = 0 OR INACTIVE IS NULL)"),
            ("الأنظمة الجذر", "SELECT * FROM S_ERP_SYSTEM WHERE (SYS_PARNT IS NULL OR SYS_PARNT = 0)"),
            ("إحصائيات", "SELECT COUNT(*) as TOTAL, COUNT(CASE WHEN INACTIVE=0 THEN 1 END) as ACTIVE FROM S_ERP_SYSTEM")
        ]
        
        self.quick_combo = QComboBox()
        self.quick_combo.addItem("اختر استعلام سريع...")
        for name, query in quick_queries:
            self.quick_combo.addItem(name, query)
        self.quick_combo.currentTextChanged.connect(self.load_quick_query)
        toolbar.addWidget(self.quick_combo)
        
        toolbar.addStretch()
        
        # زر التنفيذ
        self.execute_btn = QPushButton("▶ تنفيذ")
        self.execute_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.execute_btn.clicked.connect(self.execute_query)
        toolbar.addWidget(self.execute_btn)
        
        # زر المسح
        clear_btn = QPushButton("🗑 مسح")
        clear_btn.clicked.connect(self.clear_editor)
        toolbar.addWidget(clear_btn)
        
        layout.addLayout(toolbar)
        
        # محرر SQL
        self.query_editor = QTextEdit()
        self.query_editor.setFont(QFont("Consolas", 11))
        self.query_editor.setPlaceholderText("أدخل استعلام SQL هنا...")
        
        # إضافة مُلون SQL
        self.highlighter = SQLHighlighter(self.query_editor.document())
        
        layout.addWidget(self.query_editor)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # منطقة النتائج
        self.results_widget = QTabWidget()
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_widget.addTab(self.results_table, "النتائج")
        
        # نص النتائج
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setFont(QFont("Consolas", 10))
        self.results_widget.addTab(self.results_text, "تفاصيل")
        
        layout.addWidget(self.results_widget)
    
    def load_quick_query(self, text):
        """تحميل استعلام سريع"""
        if text == "اختر استعلام سريع...":
            return
        
        query = self.quick_combo.currentData()
        if query:
            self.query_editor.setPlainText(query)
    
    def execute_query(self):
        """تنفيذ الاستعلام"""
        query = self.query_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال استعلام SQL")
            return
        
        # تعطيل زر التنفيذ
        self.execute_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        # تنفيذ الاستعلام في خيط منفصل
        self.query_thread = QueryExecutorThread(query)
        self.query_thread.query_finished.connect(self.on_query_finished)
        self.query_thread.progress_update.connect(self.on_progress_update)
        self.query_thread.start()
    
    def on_progress_update(self, message):
        """تحديث التقدم"""
        self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def on_query_finished(self, success, result):
        """معالج انتهاء الاستعلام"""
        self.execute_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            self.display_results(result)
            self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] تم تنفيذ الاستعلام بنجاح")
        else:
            self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] خطأ: {result}")
            QMessageBox.critical(self, "خطأ", f"فشل في تنفيذ الاستعلام:\n{result}")
    
    def display_results(self, result):
        """عرض النتائج"""
        if isinstance(result, list) and result:
            # نتائج SELECT
            self.results_table.setRowCount(len(result))
            
            if result:
                columns = list(result[0].keys())
                self.results_table.setColumnCount(len(columns))
                self.results_table.setHorizontalHeaderLabels(columns)
                
                for row, record in enumerate(result):
                    for col, key in enumerate(columns):
                        value = str(record.get(key, ''))
                        self.results_table.setItem(row, col, QTableWidgetItem(value))
                
                # ضبط عرض الأعمدة
                self.results_table.resizeColumnsToContents()
                
                self.results_text.append(f"تم استرجاع {len(result)} سجل")
        else:
            # نتائج INSERT/UPDATE/DELETE
            self.results_table.setRowCount(0)
            self.results_table.setColumnCount(0)
            self.results_text.append(f"تأثر {result} سجل")
    
    def clear_editor(self):
        """مسح المحرر"""
        self.query_editor.clear()
        self.results_table.setRowCount(0)
        self.results_table.setColumnCount(0)
        self.results_text.clear()

class DatabaseAdminPanel(QMainWindow):
    """لوحة إدارة قاعدة البيانات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = get_db_manager()

        # إنشاء مدراء الميزات المتقدمة
        self.backup_manager = BackupManager()
        self.performance_monitor = PerformanceMonitor()
        self.data_validator = DataValidator()

        self.init_ui()
        self.setup_advanced_connections()
        self.check_connection()
    
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي"""
        if not ARABIC_SUPPORT or not text:
            return text
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text, base_dir='R')
        except:
            return text
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(self.format_arabic_text("SHP ERP - لوحة إدارة قاعدة البيانات"))
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # شريط العنوان
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.StyledPanel)
        title_frame.setMaximumHeight(60)
        
        title_layout = QHBoxLayout(title_frame)
        title_label = QLabel(self.format_arabic_text("🗃️ لوحة إدارة قاعدة بيانات Oracle"))
        title_label.setFont(QFont("Tahoma", 16, QFont.Bold))
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # مؤشر الاتصال
        self.connection_status = QLabel("●")
        self.connection_status.setStyleSheet("color: red; font-size: 20px;")
        title_layout.addWidget(self.connection_status)
        
        layout.addWidget(title_frame)
        
        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        
        # تبويب الإحصائيات
        self.stats_widget = DatabaseStatsWidget()
        self.tab_widget.addTab(self.stats_widget, "📊 الإحصائيات")
        
        # تبويب محرر الاستعلامات
        self.query_editor = QueryEditorWidget()
        self.tab_widget.addTab(self.query_editor, "📝 محرر SQL")
        
        # تبويب إدارة الجداول
        self.create_tables_tab()

        # تبويب النسخ الاحتياطي
        self.create_backup_tab()

        # تبويب مراقبة الأداء
        self.create_performance_tab()

        # تبويب التحقق من البيانات
        self.create_validation_tab()

        layout.addWidget(self.tab_widget)
        
        # شريط الحالة
        self.statusBar().showMessage(self.format_arabic_text("جاري فحص الاتصال..."))
    
    def create_tables_tab(self):
        """إنشاء تبويب إدارة الجداول"""
        tables_widget = QWidget()
        layout = QVBoxLayout(tables_widget)
        
        # قائمة الجداول
        self.tables_list = QTableWidget()
        self.tables_list.setColumnCount(3)
        self.tables_list.setHorizontalHeaderLabels(["اسم الجدول", "عدد الأسطر", "التعليق"])
        self.tables_list.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(QLabel("📋 جداول قاعدة البيانات:"))
        layout.addWidget(self.tables_list)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        refresh_tables_btn = QPushButton("🔄 تحديث القائمة")
        refresh_tables_btn.clicked.connect(self.refresh_tables_list)
        buttons_layout.addWidget(refresh_tables_btn)
        
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        self.tab_widget.addTab(tables_widget, "📋 الجداول")
        
        # تحميل قائمة الجداول
        self.refresh_tables_list()
    
    def check_connection(self):
        """فحص الاتصال"""
        success, message = self.db_manager.test_connection()
        
        if success:
            self.connection_status.setStyleSheet("color: green; font-size: 20px;")
            self.statusBar().showMessage(self.format_arabic_text("✅ متصل بقاعدة البيانات"))
            
            # تهيئة مجموعة الاتصالات
            if not self.db_manager.connection_pool:
                self.db_manager.initialize_connection_pool()
        else:
            self.connection_status.setStyleSheet("color: red; font-size: 20px;")
            self.statusBar().showMessage(self.format_arabic_text(f"❌ {message}"))
    
    def refresh_tables_list(self):
        """تحديث قائمة الجداول"""
        query = """
        SELECT 
            t.TABLE_NAME,
            NVL(s.NUM_ROWS, 0) as NUM_ROWS,
            c.COMMENTS
        FROM USER_TABLES t
        LEFT JOIN USER_TAB_STATISTICS s ON t.TABLE_NAME = s.TABLE_NAME
        LEFT JOIN USER_TAB_COMMENTS c ON t.TABLE_NAME = c.TABLE_NAME
        ORDER BY t.TABLE_NAME
        """
        
        success, result = self.db_manager.execute_query_safe(query)
        
        if success and result:
            self.tables_list.setRowCount(len(result))
            
            for row, table in enumerate(result):
                self.tables_list.setItem(row, 0, QTableWidgetItem(table['TABLE_NAME']))
                self.tables_list.setItem(row, 1, QTableWidgetItem(str(table['NUM_ROWS'] or 0)))
                self.tables_list.setItem(row, 2, QTableWidgetItem(table['COMMENTS'] or ''))
            
            self.tables_list.resizeColumnsToContents()

    def setup_advanced_connections(self):
        """إعداد اتصالات الميزات المتقدمة"""
        # اتصالات النسخ الاحتياطي
        self.backup_manager.backup_progress.connect(self.on_backup_progress)
        self.backup_manager.backup_completed.connect(self.on_backup_completed)

        # اتصالات مراقبة الأداء
        self.performance_monitor.performance_update.connect(self.on_performance_update)

        # اتصالات التحقق من البيانات
        self.data_validator.validation_progress.connect(self.on_validation_progress)
        self.data_validator.validation_completed.connect(self.on_validation_completed)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        backup_type_map = {
            "كاملة": "full",
            "البنية فقط": "structure",
            "البيانات فقط": "data"
        }

        backup_type = backup_type_map.get(self.backup_type_combo.currentText(), "full")

        self.backup_progress.setVisible(True)
        self.backup_progress.setRange(0, 100)

        # تشغيل النسخ الاحتياطي في خيط منفصل
        threading.Thread(
            target=self.backup_manager.create_backup,
            args=("S_ERP_SYSTEM", backup_type),
            daemon=True
        ).start()

    def on_backup_progress(self, progress: int, message: str):
        """معالج تقدم النسخ الاحتياطي"""
        self.backup_progress.setValue(progress)
        self.statusBar().showMessage(self.format_arabic_text(message))

    def on_backup_completed(self, success: bool, message: str):
        """معالج انتهاء النسخ الاحتياطي"""
        self.backup_progress.setVisible(False)

        if success:
            QMessageBox.information(self, "نجح", self.format_arabic_text(message))
            self.refresh_backups_list()
        else:
            QMessageBox.critical(self, "خطأ", self.format_arabic_text(message))

        self.statusBar().showMessage(self.format_arabic_text("جاهز"))

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        backups = self.backup_manager.list_backups()

        self.backups_table.setRowCount(len(backups))

        for row, backup in enumerate(backups):
            self.backups_table.setItem(row, 0, QTableWidgetItem(backup['filename']))
            self.backups_table.setItem(row, 1, QTableWidgetItem(backup['table_name']))
            self.backups_table.setItem(row, 2, QTableWidgetItem(backup['backup_type']))
            self.backups_table.setItem(row, 3, QTableWidgetItem(
                backup['created_date'].strftime('%Y-%m-%d %H:%M:%S')
            ))
            self.backups_table.setItem(row, 4, QTableWidgetItem(str(backup['record_count'])))
            self.backups_table.setItem(row, 5, QTableWidgetItem(
                f"{backup['file_size'] / 1024:.1f} KB"
            ))

        self.backups_table.resizeColumnsToContents()

    def toggle_monitoring(self):
        """تبديل حالة المراقبة"""
        if not hasattr(self, '_monitoring_active'):
            self._monitoring_active = False

        if self._monitoring_active:
            self.performance_monitor.stop_monitoring()
            self.monitor_btn.setText("▶ بدء المراقبة")
            self._monitoring_active = False
        else:
            interval = self.monitor_interval.value()
            self.performance_monitor.start_monitoring(interval)
            self.monitor_btn.setText("⏸ إيقاف المراقبة")
            self._monitoring_active = True

    def on_performance_update(self, performance_data: dict):
        """معالج تحديث الأداء"""
        timestamp = performance_data['timestamp'].strftime('%H:%M:%S')
        connection_status = performance_data['connection_status']
        query_performance = performance_data['query_performance']
        system_stats = performance_data['system_stats']

        info_text = f"""
<h3>تحديث الأداء - {timestamp}</h3>
<table border="1" cellpadding="5" cellspacing="0" style="width:100%">
<tr><td><b>حالة الاتصال:</b></td><td>{'متصل' if connection_status['connected'] else 'غير متصل'}</td></tr>
<tr><td><b>زمن الاستجابة:</b></td><td>{connection_status['response_time_ms']:.2f} ms</td></tr>
<tr><td><b>أداء الاستعلام البسيط:</b></td><td>{query_performance['simple_select']['execution_time_ms']:.2f} ms</td></tr>
<tr><td><b>إجمالي الأنظمة:</b></td><td>{system_stats['total_systems']}</td></tr>
<tr><td><b>الأنظمة النشطة:</b></td><td>{system_stats['active_systems']}</td></tr>
<tr><td><b>الأنظمة الجذر:</b></td><td>{system_stats['root_systems']}</td></tr>
</table>
        """

        self.performance_info.setHtml(info_text)

    def validate_data(self):
        """بدء التحقق من البيانات"""
        self.validation_progress.setVisible(True)
        self.validation_progress.setRange(0, 100)
        self.validation_results.clear()

        # تشغيل التحقق في خيط منفصل
        threading.Thread(
            target=self.data_validator.validate_systems_data,
            daemon=True
        ).start()

    def on_validation_progress(self, progress: int, message: str):
        """معالج تقدم التحقق"""
        self.validation_progress.setValue(progress)
        self.statusBar().showMessage(self.format_arabic_text(message))

    def on_validation_completed(self, success: bool, results: dict):
        """معالج انتهاء التحقق"""
        self.validation_progress.setVisible(False)

        if success:
            # عرض نتائج التحقق
            total_systems = results['total_systems']
            issues = results['issues']
            warnings = results['warnings']

            results_text = f"""
<h3>نتائج التحقق من البيانات</h3>
<p><b>إجمالي الأنظمة المفحوصة:</b> {total_systems}</p>
<p><b>عدد المشاكل:</b> {len(issues)}</p>
<p><b>عدد التحذيرات:</b> {len(warnings)}</p>

<h4>المشاكل المكتشفة:</h4>
<ul>
"""

            for issue in issues:
                results_text += f"<li style='color: red;'>{issue}</li>"

            results_text += "</ul><h4>التحذيرات:</h4><ul>"

            for warning in warnings:
                results_text += f"<li style='color: orange;'>{warning}</li>"

            results_text += "</ul>"

            self.validation_results.setHtml(results_text)
        else:
            error_msg = results.get('error', 'خطأ غير معروف')
            self.validation_results.setHtml(f"<p style='color: red;'>خطأ في التحقق: {error_msg}</p>")

        self.statusBar().showMessage(self.format_arabic_text("جاهز"))

    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)

        # أدوات النسخ الاحتياطي
        backup_controls = QGroupBox("إنشاء نسخة احتياطية")
        controls_layout = QHBoxLayout(backup_controls)

        # نوع النسخة الاحتياطية
        controls_layout.addWidget(QLabel("نوع النسخة:"))
        self.backup_type_combo = QComboBox()
        self.backup_type_combo.addItems(["كاملة", "البنية فقط", "البيانات فقط"])
        controls_layout.addWidget(self.backup_type_combo)

        # زر إنشاء النسخة
        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.clicked.connect(self.create_backup)
        controls_layout.addWidget(create_backup_btn)

        layout.addWidget(backup_controls)

        # شريط التقدم
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        layout.addWidget(self.backup_progress)

        # قائمة النسخ الاحتياطية
        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(6)
        self.backups_table.setHorizontalHeaderLabels([
            "اسم الملف", "الجدول", "النوع", "التاريخ", "عدد السجلات", "الحجم"
        ])
        self.backups_table.horizontalHeader().setStretchLastSection(True)

        layout.addWidget(QLabel("📋 النسخ الاحتياطية المتاحة:"))
        layout.addWidget(self.backups_table)

        # أزرار إدارة النسخ
        backup_buttons = QHBoxLayout()

        refresh_backups_btn = QPushButton("🔄 تحديث القائمة")
        refresh_backups_btn.clicked.connect(self.refresh_backups_list)
        backup_buttons.addWidget(refresh_backups_btn)

        backup_buttons.addStretch()

        layout.addLayout(backup_buttons)

        self.tab_widget.addTab(backup_widget, "💾 النسخ الاحتياطي")

        # تحديث قائمة النسخ الاحتياطية
        self.refresh_backups_list()

    def create_performance_tab(self):
        """إنشاء تبويب مراقبة الأداء"""
        performance_widget = QWidget()
        layout = QVBoxLayout(performance_widget)

        # أدوات المراقبة
        monitor_controls = QGroupBox("مراقبة الأداء")
        controls_layout = QHBoxLayout(monitor_controls)

        self.monitor_btn = QPushButton("▶ بدء المراقبة")
        self.monitor_btn.clicked.connect(self.toggle_monitoring)
        controls_layout.addWidget(self.monitor_btn)

        controls_layout.addWidget(QLabel("فترة المراقبة (ثانية):"))
        self.monitor_interval = QSpinBox()
        self.monitor_interval.setRange(10, 300)
        self.monitor_interval.setValue(30)
        controls_layout.addWidget(self.monitor_interval)

        controls_layout.addStretch()

        layout.addWidget(monitor_controls)

        # معلومات الأداء
        self.performance_info = QTextEdit()
        self.performance_info.setReadOnly(True)
        self.performance_info.setMaximumHeight(200)
        layout.addWidget(self.performance_info)

        self.tab_widget.addTab(performance_widget, "📊 مراقبة الأداء")

    def create_validation_tab(self):
        """إنشاء تبويب التحقق من البيانات"""
        validation_widget = QWidget()
        layout = QVBoxLayout(validation_widget)

        # أدوات التحقق
        validation_controls = QGroupBox("التحقق من صحة البيانات")
        controls_layout = QHBoxLayout(validation_controls)

        validate_btn = QPushButton("🔍 بدء التحقق")
        validate_btn.clicked.connect(self.validate_data)
        controls_layout.addWidget(validate_btn)

        controls_layout.addStretch()

        layout.addWidget(validation_controls)

        # شريط تقدم التحقق
        self.validation_progress = QProgressBar()
        self.validation_progress.setVisible(False)
        layout.addWidget(self.validation_progress)

        # نتائج التحقق
        self.validation_results = QTextEdit()
        self.validation_results.setReadOnly(True)
        layout.addWidget(self.validation_results)

        self.tab_widget.addTab(validation_widget, "🔍 التحقق من البيانات")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين خط افتراضي
    font = QFont("Tahoma", 11)
    app.setFont(font)
    
    # إنشاء لوحة الإدارة
    panel = DatabaseAdminPanel()
    panel.show()
    
    print("🗃️ تم تشغيل لوحة إدارة قاعدة البيانات")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
