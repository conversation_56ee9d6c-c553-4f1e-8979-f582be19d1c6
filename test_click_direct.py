#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر للنقر على نظام الإعدادات العامة
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox

def test_direct_click():
    """اختبار مباشر للنقر"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إعداد التطبيق للنصوص العربية
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
        
        print("✅ تم إعداد التطبيق")
        
        # اختبار فتح نظام الإعدادات العامة مباشرة
        print("🔄 فتح نظام الإعدادات العامة مباشرة...")
        
        from general_settings_system import GeneralSettingsSystem
        from PySide6.QtCore import Qt
        
        # إنشاء النافذة
        settings_window = GeneralSettingsSystem()
        
        # تعيين النافذة لتكون في المقدمة
        settings_window.setWindowFlags(
            settings_window.windowFlags() | Qt.WindowStaysOnTopHint
        )
        
        # تعيين عنوان واضح
        settings_window.setWindowTitle("🚀 SHP ERP - نظام الإعدادات العامة - اختبار مباشر")
        
        # عرض النافذة
        settings_window.show()
        settings_window.raise_()
        settings_window.activateWindow()
        
        print("✅ تم فتح نافذة الإعدادات العامة")
        print("🎯 يجب أن ترى النافذة الآن في المقدمة!")
        
        # عرض رسالة تأكيد
        QMessageBox.information(
            None, 
            "نجح الاختبار", 
            "تم فتح نافذة الإعدادات العامة بنجاح!\n\nيجب أن تراها في المقدمة الآن."
        )
        
        # اختبار فتح قسم محدد
        print("🔄 اختبار فتح قسم إعدادات المظهر...")
        settings_window.open_specific_section('APPEARANCE')
        print("✅ تم فتح قسم إعدادات المظهر")
        
        # إبقاء النافذة مفتوحة
        print("\n🎉 الاختبار مكتمل!")
        print("📋 النافذة مفتوحة الآن - يمكنك التفاعل معها")
        print("🔧 أغلق النافذة عندما تنتهي من الاختبار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار مباشر للنقر على نظام الإعدادات العامة")
    print("=" * 80)
    
    success = test_direct_click()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ نجح الاختبار المباشر!")
        print("🎯 نافذة الإعدادات العامة مفتوحة الآن")
        
        print("\n📋 ما يجب أن تراه:")
        print("• نافذة بعنوان 'SHP ERP - نظام الإعدادات العامة'")
        print("• قائمة جانبية بالإعدادات المختلفة")
        print("• منطقة محتوى رئيسية")
        print("• أزرار حفظ وتصدير في الأسفل")
        
        print("\n🔧 إذا لم تر النافذة:")
        print("• تحقق من شريط المهام")
        print("• جرب Alt+Tab")
        print("• تأكد من عدم وجود نوافذ أخرى تحجبها")
        
    else:
        print("❌ فشل الاختبار!")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
