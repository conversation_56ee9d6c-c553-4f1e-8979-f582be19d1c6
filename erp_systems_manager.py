#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Systems Manager
مدير أنظمة ERP
"""

from typing import List, Dict, Any, Optional
from database_connection import get_db_connection
import arabic_reshaper
from bidi.algorithm import get_display

class ERPSystemsManager:
    """مدير أنظمة ERP"""
    
    def __init__(self):
        self.db = get_db_connection()
        self.systems_data = []
        
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(
                text, 
                configuration={
                    'delete_harakat': False,
                    'support_zwj': True,
                    'use_unshaped_instead_of_isolated': False
                }
            )
            return get_display(reshaped_text, base_dir='R')
        except ImportError:
            print("⚠️ مكتبات دعم العربية غير متوفرة")
            return text
        except Exception:
            return text
    
    def connect_to_database(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            # محاولة الاتصال بالمستخدم SHIP2025
            success = self.db.connect(
                username="SHIP2025",
                password="",  # سيتم طلب كلمة المرور من المستخدم
                host="localhost",
                port=1521,
                service_name="orcl"
            )
            
            if success:
                print("✅ تم الاتصال بقاعدة البيانات بنجاح")
                return True
            else:
                print("❌ فشل الاتصال بقاعدة البيانات")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def get_table_info(self) -> Dict[str, Any]:
        """الحصول على معلومات جدول S_ERP_SYSTEM"""
        try:
            # الحصول على هيكل الجدول
            structure = self.db.get_table_structure("S_ERP_SYSTEM")
            
            # الحصول على التعليقات
            comments = self.db.get_table_comments("S_ERP_SYSTEM")
            
            print("📋 معلومات جدول S_ERP_SYSTEM:")
            print(f"📝 تعليق الجدول: {comments.get('TABLE_COMMENT', 'غير متوفر')}")
            print("\n📊 هيكل الجدول:")
            
            for column in structure:
                column_name = column['COLUMN_NAME']
                data_type = column['DATA_TYPE']
                nullable = column['NULLABLE']
                comment = comments.get(column_name, 'غير متوفر')
                
                print(f"  • {column_name} ({data_type}) - {comment}")
                if nullable == 'N':
                    print(f"    ⚠️ مطلوب (NOT NULL)")
            
            return {
                'structure': structure,
                'comments': comments
            }
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الجدول: {e}")
            return {}
    
    def load_systems_data(self) -> List[Dict[str, Any]]:
        """تحميل بيانات الأنظمة من جدول S_ERP_SYSTEM"""
        try:
            query = """
            SELECT 
                SYSTEM_ID,
                SYSTEM_NAME,
                SYSTEM_NAME_AR,
                PARENT_SYSTEM_ID,
                SYSTEM_LEVEL,
                SYSTEM_ORDER,
                IS_ACTIVE,
                SYSTEM_ICON,
                SYSTEM_URL,
                SYSTEM_DESCRIPTION,
                CREATED_DATE,
                CREATED_BY
            FROM S_ERP_SYSTEM
            WHERE IS_ACTIVE = 'Y'
            ORDER BY SYSTEM_LEVEL, SYSTEM_ORDER, SYSTEM_ID
            """
            
            self.systems_data = self.db.execute_query(query)
            
            print(f"✅ تم تحميل {len(self.systems_data)} نظام من قاعدة البيانات")
            
            # عرض البيانات المحملة
            self.display_systems_summary()
            
            return self.systems_data
            
        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الأنظمة: {e}")
            return []
    
    def display_systems_summary(self):
        """عرض ملخص الأنظمة المحملة"""
        if not self.systems_data:
            print("⚠️ لا توجد بيانات أنظمة محملة")
            return
        
        print("\n📋 ملخص الأنظمة المحملة:")
        print("=" * 60)
        
        # تجميع الأنظمة حسب المستوى
        levels = {}
        for system in self.systems_data:
            level = system.get('SYSTEM_LEVEL', 0)
            if level not in levels:
                levels[level] = []
            levels[level].append(system)
        
        # عرض الأنظمة حسب المستوى
        for level in sorted(levels.keys()):
            print(f"\n📊 المستوى {level}:")
            for system in levels[level]:
                system_id = system.get('SYSTEM_ID', '')
                system_name = system.get('SYSTEM_NAME', '')
                system_name_ar = system.get('SYSTEM_NAME_AR', '')
                parent_id = system.get('PARENT_SYSTEM_ID', '')
                icon = system.get('SYSTEM_ICON', '📋')
                
                # تنسيق النص العربي
                formatted_name_ar = self.format_arabic_text(system_name_ar) if system_name_ar else ''
                
                indent = "  " * level
                parent_info = f" (Parent: {parent_id})" if parent_id else ""
                
                print(f"{indent}{icon} {system_id}: {formatted_name_ar} | {system_name}{parent_info}")
    
    def build_systems_tree(self) -> Dict[str, Any]:
        """بناء شجرة الأنظمة الهرمية"""
        if not self.systems_data:
            print("⚠️ لا توجد بيانات لبناء الشجرة")
            return {}
        
        # إنشاء قاموس للأنظمة
        systems_dict = {}
        root_systems = []
        
        # تحويل البيانات إلى قاموس
        for system in self.systems_data:
            system_id = system.get('SYSTEM_ID')
            systems_dict[system_id] = {
                'data': system,
                'children': []
            }
        
        # بناء الشجرة الهرمية
        for system in self.systems_data:
            system_id = system.get('SYSTEM_ID')
            parent_id = system.get('PARENT_SYSTEM_ID')
            
            if parent_id and parent_id in systems_dict:
                # إضافة كنظام فرعي
                systems_dict[parent_id]['children'].append(systems_dict[system_id])
            else:
                # نظام جذر
                root_systems.append(systems_dict[system_id])
        
        tree = {
            'root_systems': root_systems,
            'all_systems': systems_dict
        }
        
        print(f"🌳 تم بناء شجرة الأنظمة: {len(root_systems)} نظام جذر")
        
        return tree
    
    def get_system_by_id(self, system_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على نظام بواسطة المعرف"""
        for system in self.systems_data:
            if system.get('SYSTEM_ID') == system_id:
                return system
        return None
    
    def get_child_systems(self, parent_id: str) -> List[Dict[str, Any]]:
        """الحصول على الأنظمة الفرعية لنظام معين"""
        children = []
        for system in self.systems_data:
            if system.get('PARENT_SYSTEM_ID') == parent_id:
                children.append(system)
        
        # ترتيب حسب SYSTEM_ORDER
        children.sort(key=lambda x: x.get('SYSTEM_ORDER', 0))
        return children
    
    def get_root_systems(self) -> List[Dict[str, Any]]:
        """الحصول على الأنظمة الجذر (بدون parent)"""
        root_systems = []
        for system in self.systems_data:
            parent_id = system.get('PARENT_SYSTEM_ID')
            if not parent_id or parent_id == '':
                root_systems.append(system)
        
        # ترتيب حسب SYSTEM_ORDER
        root_systems.sort(key=lambda x: x.get('SYSTEM_ORDER', 0))
        return root_systems
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        self.db.disconnect()

def test_systems_manager():
    """اختبار مدير الأنظمة"""
    print("🔄 اختبار مدير أنظمة ERP...")
    
    manager = ERPSystemsManager()
    
    # محاولة الاتصال
    if manager.connect_to_database():
        # الحصول على معلومات الجدول
        table_info = manager.get_table_info()
        
        # تحميل بيانات الأنظمة
        systems = manager.load_systems_data()
        
        if systems:
            # بناء الشجرة
            tree = manager.build_systems_tree()
            
            # عرض الأنظمة الجذر
            root_systems = manager.get_root_systems()
            print(f"\n🌳 الأنظمة الجذر: {len(root_systems)}")
            
        # قطع الاتصال
        manager.disconnect()
    else:
        print("❌ فشل في الاتصال بقاعدة البيانات")

if __name__ == "__main__":
    test_systems_manager()
