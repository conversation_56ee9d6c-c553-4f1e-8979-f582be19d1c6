#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Systems Manager
مدير أنظمة ERP
"""

from typing import List, Dict, Any, Optional
from database_connection import get_db_connection
import arabic_reshaper
from bidi.algorithm import get_display

class ERPSystemsManager:
    """مدير أنظمة ERP"""
    
    def __init__(self):
        self.db = get_db_connection()
        self.systems_data = []
        
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text, base_dir='R')
        except ImportError:
            print("⚠️ مكتبات دعم العربية غير متوفرة")
            return text
        except Exception:
            return text
    
    def connect_to_database(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            # محاولة الاتصال بالمستخدم ship2025
            success = self.db.connect(
                username="ship2025",
                password="ys123",
                host="localhost",
                port=1521,
                service_name="orcl"
            )
            
            if success:
                print("✅ تم الاتصال بقاعدة البيانات بنجاح")
                return True
            else:
                print("❌ فشل الاتصال بقاعدة البيانات")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def get_table_info(self) -> Dict[str, Any]:
        """الحصول على معلومات جدول S_ERP_SYSTEM"""
        try:
            # الحصول على هيكل الجدول
            structure = self.db.get_table_structure("S_ERP_SYSTEM")
            
            # الحصول على التعليقات
            comments = self.db.get_table_comments("S_ERP_SYSTEM")
            
            print("📋 معلومات جدول S_ERP_SYSTEM:")
            print(f"📝 تعليق الجدول: {comments.get('TABLE_COMMENT', 'غير متوفر')}")
            print("\n📊 هيكل الجدول:")
            
            for column in structure:
                column_name = column['COLUMN_NAME']
                data_type = column['DATA_TYPE']
                nullable = column['NULLABLE']
                comment = comments.get(column_name, 'غير متوفر')
                
                print(f"  • {column_name} ({data_type}) - {comment}")
                if nullable == 'N':
                    print(f"    ⚠️ مطلوب (NOT NULL)")
            
            return {
                'structure': structure,
                'comments': comments
            }
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الجدول: {e}")
            return {}
    
    def load_systems_data(self) -> List[Dict[str, Any]]:
        """تحميل بيانات الأنظمة من جدول S_ERP_SYSTEM"""
        try:
            # محاولة تحميل البيانات من قاعدة البيانات
            query = """
            SELECT
                SYS_NO as SYSTEM_ID,
                SYS_CODE as SYSTEM_CODE,
                SYS_NAME as SYSTEM_NAME,
                SYS_PARNT as PARENT_SYSTEM_ID,
                ORDR_NO as SYSTEM_ORDER,
                INACTIVE,
                FORM_NO
            FROM S_ERP_SYSTEM
            WHERE (INACTIVE = 0 OR INACTIVE IS NULL)
            ORDER BY ORDR_NO, SYS_NO
            """

            self.systems_data = self.db.execute_query(query)

            # إذا فشل تحميل البيانات من قاعدة البيانات، استخدم البيانات التجريبية
            if not self.systems_data:
                print("⚠️ فشل تحميل البيانات من قاعدة البيانات، استخدام البيانات التجريبية...")
                self.systems_data = self.get_sample_systems_data()

            print(f"✅ تم تحميل {len(self.systems_data)} نظام")

            # عرض البيانات المحملة
            self.display_systems_summary()

            return self.systems_data

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الأنظمة: {e}")
            print("🔄 استخدام البيانات التجريبية...")
            self.systems_data = self.get_sample_systems_data()
            return self.systems_data

    def get_sample_systems_data(self) -> List[Dict[str, Any]]:
        """الحصول على بيانات الأنظمة التجريبية"""
        return [
            # النظام الرئيسي
            {
                'SYSTEM_ID': 1,
                'SYSTEM_CODE': 'ERP',
                'SYSTEM_NAME': 'نظام ERP الرئيسي',
                'PARENT_SYSTEM_ID': None,
                'SYSTEM_ORDER': 1,
                'INACTIVE': 0,
                'FORM_NO': None
            },

            # نظام الإعدادات العامة
            {
                'SYSTEM_ID': 10,
                'SYSTEM_CODE': 'GENSETTINGS',
                'SYSTEM_NAME': 'نظام الإعدادات العامة الشامل',
                'PARENT_SYSTEM_ID': 1,
                'SYSTEM_ORDER': 10,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },

            # الأنظمة الفرعية للإعدادات العامة
            {
                'SYSTEM_ID': 101,
                'SYSTEM_CODE': 'APPEARANCE',
                'SYSTEM_NAME': 'إعدادات المظهر',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 1,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 102,
                'SYSTEM_CODE': 'COMPANY',
                'SYSTEM_NAME': 'بيانات الشركة',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 2,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 103,
                'SYSTEM_CODE': 'CURRENCIES',
                'SYSTEM_NAME': 'إعدادات العملات',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 3,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 104,
                'SYSTEM_CODE': 'FISCALYEAR',
                'SYSTEM_NAME': 'السنة المالية',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 4,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 105,
                'SYSTEM_CODE': 'LANGUAGE',
                'SYSTEM_NAME': 'إعدادات اللغة',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 5,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 106,
                'SYSTEM_CODE': 'SECURITY',
                'SYSTEM_NAME': 'إعدادات الأمان',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 6,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 107,
                'SYSTEM_CODE': 'EMAIL',
                'SYSTEM_NAME': 'إعدادات البريد الإلكتروني',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 7,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 108,
                'SYSTEM_CODE': 'PRINTING',
                'SYSTEM_NAME': 'إعدادات الطباعة',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 8,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 109,
                'SYSTEM_CODE': 'REPORTS',
                'SYSTEM_NAME': 'إعدادات التقارير',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 9,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 110,
                'SYSTEM_CODE': 'BACKUPSETTINGS',
                'SYSTEM_NAME': 'إعدادات النسخ الاحتياطي',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 10,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 111,
                'SYSTEM_CODE': 'SYSTEMSETTINGS',
                'SYSTEM_NAME': 'إعدادات النظام',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 11,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },
            {
                'SYSTEM_ID': 112,
                'SYSTEM_CODE': 'NOTIFICATIONS',
                'SYSTEM_NAME': 'إعدادات الإشعارات',
                'PARENT_SYSTEM_ID': 10,
                'SYSTEM_ORDER': 12,
                'INACTIVE': 0,
                'FORM_NO': 'general_settings_system.py'
            },

            # نظام إدارة قاعدة البيانات
            {
                'SYSTEM_ID': 201,
                'SYSTEM_CODE': 'DBADM',
                'SYSTEM_NAME': 'نظام إدارة قاعدة البيانات Oracle',
                'PARENT_SYSTEM_ID': 1,
                'SYSTEM_ORDER': 201,
                'INACTIVE': 0,
                'FORM_NO': 'database_admin_window.py'
            },
            {
                'SYSTEM_ID': 202,
                'SYSTEM_CODE': 'DBCON',
                'SYSTEM_NAME': 'إدارة الاتصالات',
                'PARENT_SYSTEM_ID': 201,
                'SYSTEM_ORDER': 1,
                'INACTIVE': 0,
                'FORM_NO': 'database_connection_dialog.py'
            },
            {
                'SYSTEM_ID': 203,
                'SYSTEM_CODE': 'SQLED',
                'SYSTEM_NAME': 'محرر SQL المتقدم',
                'PARENT_SYSTEM_ID': 201,
                'SYSTEM_ORDER': 2,
                'INACTIVE': 0,
                'FORM_NO': 'advanced_sql_editor.py'
            },
            {
                'SYSTEM_ID': 204,
                'SYSTEM_CODE': 'DBBAK',
                'SYSTEM_NAME': 'النسخ الاحتياطي',
                'PARENT_SYSTEM_ID': 201,
                'SYSTEM_ORDER': 3,
                'INACTIVE': 0,
                'FORM_NO': 'advanced_backup_system.py'
            },
            {
                'SYSTEM_ID': 205,
                'SYSTEM_CODE': 'DBSTA',
                'SYSTEM_NAME': 'إحصائيات قاعدة البيانات',
                'PARENT_SYSTEM_ID': 201,
                'SYSTEM_ORDER': 4,
                'INACTIVE': 0,
                'FORM_NO': 'database_admin_window.py'
            },
            {
                'SYSTEM_ID': 206,
                'SYSTEM_CODE': 'DBTBL',
                'SYSTEM_NAME': 'إدارة الجداول',
                'PARENT_SYSTEM_ID': 201,
                'SYSTEM_ORDER': 5,
                'INACTIVE': 0,
                'FORM_NO': 'database_admin_window.py'
            }
        ]
    
    def display_systems_summary(self):
        """عرض ملخص الأنظمة المحملة"""
        if not self.systems_data:
            print("⚠️ لا توجد بيانات أنظمة محملة")
            return
        
        print("\n📋 ملخص الأنظمة المحملة:")
        print("=" * 60)
        
        # تجميع الأنظمة حسب المستوى
        levels = {}
        for system in self.systems_data:
            level = system.get('SYSTEM_LEVEL', 0)
            if level not in levels:
                levels[level] = []
            levels[level].append(system)
        
        # عرض الأنظمة حسب المستوى
        for level in sorted(levels.keys()):
            print(f"\n📊 المستوى {level}:")
            for system in levels[level]:
                system_id = system.get('SYSTEM_ID', '')
                system_name = system.get('SYSTEM_NAME', '')
                system_name_ar = system.get('SYSTEM_NAME_AR', '')
                parent_id = system.get('PARENT_SYSTEM_ID', '')
                icon = system.get('SYSTEM_ICON', '📋')
                
                # تنسيق النص العربي
                formatted_name_ar = self.format_arabic_text(system_name_ar) if system_name_ar else ''
                
                indent = "  " * level
                parent_info = f" (Parent: {parent_id})" if parent_id else ""
                
                print(f"{indent}{icon} {system_id}: {formatted_name_ar} | {system_name}{parent_info}")
    
    def build_systems_tree(self) -> Dict[str, Any]:
        """بناء شجرة الأنظمة الهرمية"""
        if not self.systems_data:
            print("⚠️ لا توجد بيانات لبناء الشجرة")
            return {}
        
        # إنشاء قاموس للأنظمة
        systems_dict = {}
        root_systems = []
        
        # تحويل البيانات إلى قاموس
        for system in self.systems_data:
            system_id = system.get('SYSTEM_ID')
            systems_dict[system_id] = {
                'data': system,
                'children': []
            }
        
        # بناء الشجرة الهرمية
        for system in self.systems_data:
            system_id = system.get('SYSTEM_ID')
            parent_id = system.get('PARENT_SYSTEM_ID')
            
            if parent_id and parent_id in systems_dict:
                # إضافة كنظام فرعي
                systems_dict[parent_id]['children'].append(systems_dict[system_id])
            else:
                # نظام جذر
                root_systems.append(systems_dict[system_id])
        
        tree = {
            'root_systems': root_systems,
            'all_systems': systems_dict
        }
        
        print(f"🌳 تم بناء شجرة الأنظمة: {len(root_systems)} نظام جذر")
        
        return tree
    
    def get_system_by_id(self, system_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على نظام بواسطة المعرف"""
        for system in self.systems_data:
            if system.get('SYSTEM_ID') == system_id:
                return system
        return None
    
    def get_child_systems(self, parent_id: int) -> List[Dict[str, Any]]:
        """الحصول على الأنظمة الفرعية لنظام معين"""
        children = []
        for system in self.systems_data:
            if system.get('PARENT_SYSTEM_ID') == parent_id:
                children.append(system)

        # ترتيب حسب SYSTEM_ORDER
        children.sort(key=lambda x: x.get('SYSTEM_ORDER', 0) or 0)
        return children
    
    def get_root_systems(self) -> List[Dict[str, Any]]:
        """الحصول على الأنظمة الجذر (بدون parent)"""
        root_systems = []
        for system in self.systems_data:
            parent_id = system.get('PARENT_SYSTEM_ID')
            if not parent_id or parent_id == 0:
                root_systems.append(system)

        # ترتيب حسب SYSTEM_ORDER
        root_systems.sort(key=lambda x: x.get('SYSTEM_ORDER', 0) or 0)
        return root_systems
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        self.db.disconnect()

def test_systems_manager():
    """اختبار مدير الأنظمة"""
    print("🔄 اختبار مدير أنظمة ERP...")
    
    manager = ERPSystemsManager()
    
    # محاولة الاتصال
    if manager.connect_to_database():
        # الحصول على معلومات الجدول
        table_info = manager.get_table_info()
        
        # تحميل بيانات الأنظمة
        systems = manager.load_systems_data()
        
        if systems:
            # بناء الشجرة
            tree = manager.build_systems_tree()
            
            # عرض الأنظمة الجذر
            root_systems = manager.get_root_systems()
            print(f"\n🌳 الأنظمة الجذر: {len(root_systems)}")
            
        # قطع الاتصال
        manager.disconnect()
    else:
        print("❌ فشل في الاتصال بقاعدة البيانات")

if __name__ == "__main__":
    test_systems_manager()
