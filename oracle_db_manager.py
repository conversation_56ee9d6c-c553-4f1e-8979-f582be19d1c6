#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Oracle Database Manager
نظام إدارة قاعدة بيانات Oracle المتقدم
"""

import cx_Oracle
import threading
import time
import logging
from typing import Optional, List, Dict, Any, Callable
from datetime import datetime
from contextlib import contextmanager

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('oracle_db.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ConnectionPool:
    """مجموعة اتصالات قاعدة البيانات"""
    
    def __init__(self, username: str, password: str, dsn: str, 
                 min_connections: int = 2, max_connections: int = 10):
        self.username = username
        self.password = password
        self.dsn = dsn
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.pool = None
        self.logger = logging.getLogger(f"{__name__}.ConnectionPool")
        
    def create_pool(self) -> bool:
        """إنشاء مجموعة الاتصالات"""
        try:
            self.pool = cx_Oracle.SessionPool(
                user=self.username,
                password=self.password,
                dsn=self.dsn,
                min=self.min_connections,
                max=self.max_connections,
                increment=1,
                encoding="UTF-8",
                threaded=True
            )
            self.logger.info(f"تم إنشاء مجموعة الاتصالات: {self.min_connections}-{self.max_connections}")
            return True
        except Exception as e:
            self.logger.error(f"فشل في إنشاء مجموعة الاتصالات: {e}")
            return False
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال من المجموعة"""
        connection = None
        try:
            if not self.pool:
                raise Exception("مجموعة الاتصالات غير متاحة")
            
            connection = self.pool.acquire()
            yield connection
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الاتصال: {e}")
            raise
        finally:
            if connection:
                self.pool.release(connection)
    
    def close_pool(self):
        """إغلاق مجموعة الاتصالات"""
        if self.pool:
            self.pool.close()
            self.logger.info("تم إغلاق مجموعة الاتصالات")

class OracleDBManager:
    """مدير قاعدة بيانات Oracle المتقدم"""
    
    def __init__(self):
        self.connection_pool = None
        self.connection_config = {
            'username': 'SHIP2025',
            'password': 'ys123',
            'host': 'localhost',
            'port': 1521,
            'service_name': 'orcl'
        }
        self.logger = logging.getLogger(f"{__name__}.OracleDBManager")
        self.connection_timeout = 30  # ثواني
        self.query_timeout = 60  # ثواني
        
    def create_dsn(self) -> str:
        """إنشاء DSN للاتصال"""
        return f"""(DESCRIPTION=
            (ADDRESS=
                (PROTOCOL=TCP)
                (HOST={self.connection_config['host']})
                (PORT={self.connection_config['port']})
            )
            (CONNECT_DATA=
                (SERVER=dedicated)
                (SERVICE_NAME={self.connection_config['service_name']})
            )
        )"""
    
    def test_connection(self) -> tuple[bool, str]:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            self.logger.info("بدء اختبار الاتصال...")
            
            dsn = self.create_dsn()
            
            # استخدام timeout للاتصال
            connection = cx_Oracle.connect(
                user=self.connection_config['username'],
                password=self.connection_config['password'],
                dsn=dsn,
                encoding="UTF-8"
            )
            
            # اختبار استعلام بسيط
            cursor = connection.cursor()
            cursor.execute("SELECT SYSDATE FROM DUAL")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            self.logger.info("نجح اختبار الاتصال")
            return True, f"نجح الاتصال - التاريخ: {result[0]}"
            
        except cx_Oracle.Error as e:
            error_obj, = e.args
            error_msg = f"خطأ Oracle: {error_obj.message}"
            self.logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"خطأ عام: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def initialize_connection_pool(self) -> bool:
        """تهيئة مجموعة الاتصالات"""
        try:
            dsn = self.create_dsn()
            self.connection_pool = ConnectionPool(
                username=self.connection_config['username'],
                password=self.connection_config['password'],
                dsn=dsn
            )
            
            return self.connection_pool.create_pool()
        except Exception as e:
            self.logger.error(f"فشل في تهيئة مجموعة الاتصالات: {e}")
            return False
    
    def execute_query_safe(self, query: str, params: tuple = None,
                          fetch_all: bool = True, timeout: int = None) -> tuple[bool, Any]:
        """تنفيذ استعلام بشكل آمن مع timeout"""
        if not self.connection_pool:
            return False, "مجموعة الاتصالات غير متاحة"

        timeout = timeout or self.query_timeout

        def execute_with_timeout():
            try:
                with self.connection_pool.get_connection() as connection:
                    cursor = connection.cursor()

                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)

                    if query.strip().upper().startswith('SELECT'):
                        if fetch_all:
                            # تحويل النتائج إلى قاموس
                            columns = [col[0] for col in cursor.description]
                            rows = cursor.fetchall()
                            result = [dict(zip(columns, row)) for row in rows]
                        else:
                            result = cursor.fetchone()
                    else:
                        connection.commit()
                        result = cursor.rowcount

                    cursor.close()
                    return True, result

            except cx_Oracle.Error as e:
                error_obj, = e.args
                error_msg = f"خطأ Oracle: {error_obj.message}"
                self.logger.error(f"خطأ في تنفيذ الاستعلام: {error_msg}")
                return False, error_msg
            except Exception as e:
                error_msg = f"خطأ عام: {str(e)}"
                self.logger.error(f"خطأ في تنفيذ الاستعلام: {error_msg}")
                return False, error_msg

        # تنفيذ الاستعلام في thread منفصل مع timeout
        result_container = [None]

        def target():
            result_container[0] = execute_with_timeout()

        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout)

        if thread.is_alive():
            self.logger.error(f"انتهت مهلة الاستعلام ({timeout} ثانية)")
            return False, f"انتهت مهلة الاستعلام ({timeout} ثانية)"

        return result_container[0] if result_container[0] else (False, "فشل في تنفيذ الاستعلام")
    
    def get_table_info(self, table_name: str) -> tuple[bool, Dict]:
        """الحصول على معلومات الجدول"""
        try:
            # معلومات الأعمدة
            columns_query = """
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                DATA_LENGTH,
                NULLABLE,
                DATA_DEFAULT
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = :table_name
            ORDER BY COLUMN_ID
            """
            
            success, columns = self.execute_query_safe(columns_query, (table_name,))
            if not success:
                return False, {"error": columns}
            
            # تعليقات الأعمدة
            comments_query = """
            SELECT 
                COLUMN_NAME,
                COMMENTS
            FROM USER_COL_COMMENTS 
            WHERE TABLE_NAME = :table_name
            AND COMMENTS IS NOT NULL
            """
            
            success, comments = self.execute_query_safe(comments_query, (table_name,))
            if not success:
                comments = []
            
            # تعليق الجدول
            table_comment_query = """
            SELECT COMMENTS 
            FROM USER_TAB_COMMENTS 
            WHERE TABLE_NAME = :table_name
            """
            
            success, table_comment = self.execute_query_safe(table_comment_query, (table_name,), False)
            
            return True, {
                'columns': columns,
                'comments': comments,
                'table_comment': table_comment[0] if table_comment else None
            }
            
        except Exception as e:
            return False, {"error": str(e)}
    
    def get_systems_data(self) -> tuple[bool, List[Dict]]:
        """تحميل بيانات الأنظمة بشكل آمن"""
        query = """
        SELECT 
            SYS_NO as SYSTEM_ID,
            SYS_CODE as SYSTEM_CODE,
            SYS_NAME as SYSTEM_NAME,
            SYS_PARNT as PARENT_SYSTEM_ID,
            ORDR_NO as SYSTEM_ORDER,
            INACTIVE,
            FORM_NO
        FROM S_ERP_SYSTEM
        WHERE (INACTIVE = 0 OR INACTIVE IS NULL)
        ORDER BY ORDR_NO, SYS_NO
        """
        
        return self.execute_query_safe(query)
    
    def close_connections(self):
        """إغلاق جميع الاتصالات"""
        if self.connection_pool:
            self.connection_pool.close_pool()
            self.connection_pool = None
        self.logger.info("تم إغلاق جميع الاتصالات")

# إنشاء مثيل عام
db_manager = OracleDBManager()

def get_db_manager() -> OracleDBManager:
    """الحصول على مدير قاعدة البيانات"""
    return db_manager
