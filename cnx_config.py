#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CnX ERP - ملف الإعدادات والتكوين
Configuration and Settings for CnX ERP System
"""

# إعدادات النافذة الرئيسية
WINDOW_CONFIG = {
    'title': 'CnX ERP - شركة القدس للتجارة والتوريدات المحدودة - الإدارة المالية 1.7/2024',
    'width': 1200,
    'height': 800,
    'min_width': 1024,
    'min_height': 768,
    'icon': None  # يمكن إضافة مسار الأيقونة هنا
}

# إعدادات الألوان
COLORS = {
    # الألوان الأساسية
    'primary': '#2196F3',           # الأزرق الأساسي
    'secondary': '#4CAF50',         # الأخضر
    'accent': '#FF9800',            # البرتقالي
    'danger': '#E53E3E',            # الأحمر
    'warning': '#FFC107',           # الأصفر
    'info': '#17A2B8',              # الأزرق الفاتح
    'success': '#28A745',           # الأخضر الداكن
    
    # ألوان الخلفية
    'background': '#FAFAFA',        # خلفية رئيسية
    'surface': '#FFFFFF',           # خلفية العناصر
    'card': '#F5F5F5',              # خلفية البطاقات
    
    # ألوان النصوص
    'text_primary': '#333333',      # نص أساسي
    'text_secondary': '#666666',    # نص ثانوي
    'text_muted': '#888888',        # نص خافت
    'text_light': '#AAAAAA',        # نص فاتح
    
    # ألوان الحدود
    'border': '#E0E0E0',            # حدود عادية
    'border_light': '#F0F0F0',      # حدود فاتحة
    'border_dark': '#CCCCCC',       # حدود داكنة
    
    # ألوان التدرج للخلفية الفنية
    'gradient_red': '#E53E3E',      # أحمر التدرج
    'gradient_blue': '#3182CE',     # أزرق التدرج
    'gradient_green': '#38A169',    # أخضر التدرج
}

# إعدادات الخطوط
FONTS = {
    'family': 'Arial',              # عائلة الخط الأساسية
    'size_small': 9,                # حجم صغير
    'size_normal': 10,              # حجم عادي
    'size_medium': 12,              # حجم متوسط
    'size_large': 14,               # حجم كبير
    'size_xlarge': 16,              # حجم كبير جداً
    'size_title': 24,               # حجم العناوين
    'size_logo': 48,                # حجم الشعار
}

# إعدادات التخطيط
LAYOUT = {
    'left_sidebar_width': 240,      # عرض الشريط الأيسر
    'right_sidebar_width': 220,     # عرض الشريط الأيمن
    'toolbar_height': 60,           # ارتفاع شريط الأدوات
    'statusbar_height': 25,         # ارتفاع شريط الحالة
    'margin': 8,                    # الهامش العام
    'spacing': 5,                   # المسافة بين العناصر
    'border_radius': 6,             # نصف قطر الحدود
}

# قائمة الأدوات في شريط الأدوات
TOOLBAR_TOOLS = [
    {
        'icon': 'ℹ️',
        'text': 'معلومات',
        'tooltip': 'عرض معلومات النظام',
        'action': 'show_info'
    },
    {
        'icon': '🔊',
        'text': 'الصوت',
        'tooltip': 'تشغيل/إيقاف الأصوات',
        'action': 'toggle_sound'
    },
    {
        'icon': '🔔',
        'text': 'التنبيهات',
        'tooltip': 'عرض الإشعارات والتنبيهات',
        'action': 'show_notifications'
    },
    {
        'icon': '⚙️',
        'text': 'الإعدادات',
        'tooltip': 'إعدادات النظام',
        'action': 'show_settings'
    },
    {
        'icon': '🖨️',
        'text': 'طباعة',
        'tooltip': 'طباعة التقرير الحالي',
        'action': 'print_document'
    },
    {
        'icon': '💾',
        'text': 'حفظ',
        'tooltip': 'حفظ البيانات الحالية',
        'action': 'save_document'
    },
    {
        'icon': '⭐',
        'text': 'المفضلة',
        'tooltip': 'إضافة/إزالة من المفضلة',
        'action': 'toggle_favorite'
    },
    {
        'icon': '🔧',
        'text': 'الأدوات',
        'tooltip': 'أدوات إضافية',
        'action': 'show_tools'
    },
    {
        'icon': '📧',
        'text': 'البريد',
        'tooltip': 'البريد الإلكتروني',
        'action': 'open_email'
    }
]

# قائمة القوائم الرئيسية
MAIN_MENU_ITEMS = [
    {
        'icon': '📊',
        'text': 'التقرير الإحصائي',
        'description': 'عرض التقارير الإحصائية والرسوم البيانية',
        'action': 'open_statistics'
    },
    {
        'icon': '🏢',
        'text': 'مركز التكلفة',
        'description': 'إدارة مراكز التكلفة والأقسام',
        'action': 'open_cost_center'
    },
    {
        'icon': '📋',
        'text': 'أوامر الشراء',
        'description': 'إدارة أوامر الشراء والموردين',
        'action': 'open_purchase_orders'
    },
    {
        'icon': '📦',
        'text': 'بيانات الأصناف',
        'description': 'إدارة بيانات المنتجات والأصناف',
        'action': 'open_items_data'
    },
    {
        'icon': '📈',
        'text': 'بيانات وحسابات',
        'description': 'إدارة الحسابات والبيانات المالية',
        'action': 'open_accounts_data'
    },
    {
        'icon': '💰',
        'text': 'سجل الأرصدة',
        'description': 'عرض وإدارة أرصدة الحسابات',
        'action': 'open_balances'
    },
    {
        'icon': '📋',
        'text': 'قائمة الجرد/العمل',
        'description': 'إدارة عمليات الجرد والمخزون',
        'action': 'open_inventory'
    },
    {
        'icon': '📊',
        'text': 'تقرير الأرصدة الحالية',
        'description': 'تقرير مفصل للأرصدة الحالية',
        'action': 'open_current_balances'
    },
    {
        'icon': '📈',
        'text': 'تقرير حركة المخزون',
        'description': 'تقرير حركة المخزون والمواد',
        'action': 'open_inventory_movement'
    },
    {
        'icon': '📋',
        'text': 'تقارير الحركات المالية',
        'description': 'تقارير شاملة للحركات المالية',
        'action': 'open_financial_reports'
    }
]

# عناصر لوحة التحكم
CONTROL_PANEL_ITEMS = [
    {
        'type': 'search',
        'icon': '🔍',
        'label': 'البحث',
        'placeholder': 'ابحث في النظام...',
        'tooltip': 'البحث في جميع البيانات'
    },
    {
        'type': 'date',
        'icon': '📅',
        'label': 'التاريخ',
        'tooltip': 'اختيار التاريخ للتصفية'
    },
    {
        'type': 'combo',
        'icon': '📂',
        'label': 'النوع',
        'items': ['الكل', 'مبيعات', 'مشتريات', 'مخزون', 'حسابات', 'تقارير'],
        'tooltip': 'تصفية حسب نوع العملية'
    },
    {
        'type': 'combo',
        'icon': '📊',
        'label': 'الحالة',
        'items': ['الكل', 'نشط', 'معلق', 'مكتمل', 'ملغي', 'قيد المراجعة'],
        'tooltip': 'تصفية حسب حالة السجل'
    }
]

# أزرار العمليات
ACTION_BUTTONS = [
    {
        'icon': '🔍',
        'text': 'بحث',
        'color': '#4CAF50',
        'tooltip': 'تنفيذ عملية البحث',
        'action': 'execute_search'
    },
    {
        'icon': '🔽',
        'text': 'تصفية',
        'color': '#2196F3',
        'tooltip': 'تطبيق المرشحات',
        'action': 'apply_filters'
    },
    {
        'icon': '📤',
        'text': 'تصدير',
        'color': '#FF9800',
        'tooltip': 'تصدير البيانات',
        'action': 'export_data'
    },
    {
        'icon': '🖨️',
        'text': 'طباعة',
        'color': '#9C27B0',
        'tooltip': 'طباعة التقرير',
        'action': 'print_report'
    }
]

# إعدادات الخلفية الفنية
ARTISTIC_BACKGROUND = {
    'enabled': True,                # تفعيل الخلفية الفنية
    'curves_count': {
        'red': 12,                  # عدد المنحنيات الحمراء
        'blue': 10,                 # عدد المنحنيات الزرقاء
        'green': 8                  # عدد المنحنيات الخضراء
    },
    'opacity': {
        'red': 40,                  # شفافية المنحنيات الحمراء
        'blue': 35,                 # شفافية المنحنيات الزرقاء
        'green': 30                 # شفافية المنحنيات الخضراء
    },
    'animation': False              # تفعيل الحركة (قيد التطوير)
}

# إعدادات النصوص
TEXT_CONFIG = {
    'rtl_support': True,            # دعم الكتابة من اليمين لليسار
    'arabic_reshaping': True,       # إعادة تشكيل النصوص العربية
    'font_fallback': ['Arial', 'Tahoma', 'Segoe UI'],  # خطوط احتياطية
}

# إعدادات النظام
SYSTEM_CONFIG = {
    'version': '1.7.2024',         # إصدار النظام
    'company': 'شركة القدس للتجارة والتوريدات المحدودة',
    'department': 'الإدارة المالية',
    'language': 'ar',              # اللغة الافتراضية
    'theme': 'light',              # السمة الافتراضية
    'auto_save': True,             # الحفظ التلقائي
    'backup_enabled': True,        # تفعيل النسخ الاحتياطي
}

# رسائل النظام
MESSAGES = {
    'welcome': 'مرحباً بك في نظام CnX ERP',
    'ready': 'النظام جاهز للعمل',
    'loading': 'جاري التحميل...',
    'saving': 'جاري الحفظ...',
    'error': 'حدث خطأ غير متوقع',
    'success': 'تمت العملية بنجاح',
    'confirm_exit': 'هل تريد الخروج من النظام؟',
    'data_saved': 'تم حفظ البيانات بنجاح',
    'no_data': 'لا توجد بيانات للعرض'
}

# إعدادات قاعدة البيانات (للتطوير المستقبلي)
DATABASE_CONFIG = {
    'type': 'sqlite',              # نوع قاعدة البيانات
    'host': 'localhost',           # الخادم
    'port': 5432,                  # المنفذ
    'name': 'cnx_erp',             # اسم قاعدة البيانات
    'backup_interval': 24,         # فترة النسخ الاحتياطي (ساعات)
}

def get_style_sheet():
    """إرجاع ورقة الأنماط الكاملة للتطبيق"""
    return f"""
    QMainWindow {{
        background-color: {COLORS['background']};
        font-family: {FONTS['family']};
        font-size: {FONTS['size_normal']}px;
    }}
    
    QFrame {{
        background-color: {COLORS['surface']};
        border: 1px solid {COLORS['border']};
        border-radius: {LAYOUT['border_radius']}px;
    }}
    
    QTreeWidget {{
        background-color: {COLORS['surface']};
        border: 1px solid {COLORS['border']};
        border-radius: {LAYOUT['border_radius']}px;
        font-size: {FONTS['size_normal']}px;
        padding: {LAYOUT['margin']}px;
        outline: none;
    }}
    
    QTreeWidget::item {{
        padding: 10px 8px;
        border-bottom: 1px solid {COLORS['border_light']};
        border-radius: 4px;
        margin: 1px;
    }}
    
    QTreeWidget::item:hover {{
        background-color: {COLORS['primary']}20;
        color: {COLORS['primary']};
    }}
    
    QTreeWidget::item:selected {{
        background-color: {COLORS['primary']};
        color: white;
        font-weight: bold;
    }}
    
    QPushButton {{
        background-color: {COLORS['primary']};
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: {LAYOUT['border_radius']}px;
        font-weight: bold;
        font-size: {FONTS['size_normal']}px;
    }}
    
    QPushButton:hover {{
        background-color: {COLORS['primary']}DD;
    }}
    
    QPushButton:pressed {{
        background-color: {COLORS['primary']}BB;
    }}
    
    QLineEdit, QComboBox, QDateEdit {{
        padding: 8px;
        border: 2px solid {COLORS['border']};
        border-radius: {LAYOUT['border_radius']}px;
        background-color: {COLORS['surface']};
        font-size: {FONTS['size_normal']}px;
    }}
    
    QLineEdit:focus, QComboBox:focus, QDateEdit:focus {{
        border-color: {COLORS['primary']};
    }}
    
    QLabel {{
        color: {COLORS['text_primary']};
        font-size: {FONTS['size_normal']}px;
    }}
    
    QToolBar {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 {COLORS['primary']}30, stop:1 {COLORS['primary']}20);
        border: none;
        spacing: {LAYOUT['spacing']}px;
        padding: {LAYOUT['margin']}px;
        font-size: {FONTS['size_small']}px;
    }}
    
    QStatusBar {{
        background-color: {COLORS['card']};
        border-top: 1px solid {COLORS['border']};
        color: {COLORS['text_secondary']};
        font-weight: bold;
    }}
    """
