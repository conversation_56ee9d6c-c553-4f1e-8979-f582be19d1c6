#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Database Connection Dialog
نافذة ربط قواعد البيانات
"""

import sys
from typing import Optional, Dict, Any

from PySide6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox, QCheckBox, QPushButton,
    QGroupBox, QTabWidget, QWidget, QTextEdit, QProgressBar,
    QMessageBox, QFileDialog, QFrame
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QPixmap, QIcon

from arabic_text_helper import format_arabic_text, setup_arabic_widget
from multi_database_manager import DatabaseConnection, DatabaseType, MultiDatabaseManager

class ConnectionTestThread(QThread):
    """خيط اختبار الاتصال"""
    test_finished = Signal(bool, str)
    progress_updated = Signal(int)
    
    def __init__(self, manager, connection):
        super().__init__()
        self.manager = manager
        self.connection = connection
    
    def run(self):
        """تنفيذ اختبار الاتصال"""
        try:
            # محاكاة تقدم الاختبار
            for i in range(0, 101, 20):
                self.progress_updated.emit(i)
                self.msleep(200)
            
            # إضافة الاتصال مؤقتاً للاختبار
            temp_name = f"test_{self.connection.name}"
            temp_conn = DatabaseConnection(
                name=temp_name,
                db_type=self.connection.db_type,
                host=self.connection.host,
                port=self.connection.port,
                database=self.connection.database,
                service_name=self.connection.service_name,
                username=self.connection.username,
                password=self.connection.password
            )
            
            self.manager.connections[temp_name] = temp_conn
            success, message = self.manager.test_connection(temp_name)
            
            # حذف الاتصال المؤقت
            if temp_name in self.manager.connections:
                del self.manager.connections[temp_name]
            
            self.test_finished.emit(success, message)
            
        except Exception as e:
            self.test_finished.emit(False, str(e))

class DatabaseConnectionDialog(QDialog):
    """نافذة ربط قواعد البيانات"""
    
    def __init__(self, parent=None, connection=None, manager=None):
        super().__init__(parent)
        self.connection = connection
        self.manager = manager or MultiDatabaseManager()
        self.test_thread = None
        self.is_edit_mode = connection is not None
        
        self.init_ui()
        self.setup_connections()
        
        if self.connection:
            self.load_connection_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل اتصال قاعدة البيانات" if self.is_edit_mode else "إضافة اتصال قاعدة بيانات جديد"
        self.setWindowTitle(format_arabic_text(title))
        self.setFixedSize(600, 700)
        
        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel(format_arabic_text(title))
        title_label.setFont(QFont("Tahoma", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الإعدادات الأساسية
        self.create_basic_settings_tab()
        
        # تبويب الإعدادات المتقدمة
        self.create_advanced_settings_tab()
        
        # تبويب اختبار الاتصال
        self.create_test_tab()
        
        main_layout.addWidget(self.tabs)
        
        # أزرار التحكم
        self.create_control_buttons()
        main_layout.addLayout(self.buttons_layout)
        
    def create_basic_settings_tab(self):
        """إنشاء تبويب الإعدادات الأساسية"""
        basic_widget = QWidget()
        layout = QVBoxLayout(basic_widget)
        
        # معلومات الاتصال الأساسية
        basic_group = QGroupBox(format_arabic_text("معلومات الاتصال الأساسية"))
        basic_form = QFormLayout(basic_group)
        
        # اسم الاتصال
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(format_arabic_text("اسم فريد للاتصال"))
        basic_form.addRow(format_arabic_text("اسم الاتصال:"), self.name_edit)
        
        # نوع قاعدة البيانات
        self.db_type_combo = QComboBox()
        for db_type in DatabaseType:
            self.db_type_combo.addItem(db_type.value, db_type)
        self.db_type_combo.currentTextChanged.connect(self.on_db_type_changed)
        basic_form.addRow(format_arabic_text("نوع قاعدة البيانات:"), self.db_type_combo)
        
        # عنوان الخادم
        self.host_edit = QLineEdit()
        self.host_edit.setText("localhost")
        self.host_edit.setPlaceholderText(format_arabic_text("عنوان IP أو اسم الخادم"))
        basic_form.addRow(format_arabic_text("عنوان الخادم:"), self.host_edit)
        
        # منفذ الاتصال
        self.port_spin = QSpinBox()
        self.port_spin.setRange(1, 65535)
        self.port_spin.setValue(1521)
        basic_form.addRow(format_arabic_text("منفذ الاتصال:"), self.port_spin)
        
        layout.addWidget(basic_group)
        
        # معلومات قاعدة البيانات
        db_group = QGroupBox(format_arabic_text("معلومات قاعدة البيانات"))
        db_form = QFormLayout(db_group)
        
        # اسم قاعدة البيانات / اسم الخدمة
        self.database_edit = QLineEdit()
        self.database_label = QLabel(format_arabic_text("اسم قاعدة البيانات:"))
        db_form.addRow(self.database_label, self.database_edit)
        
        # اسم الخدمة (للـ Oracle)
        self.service_name_edit = QLineEdit()
        self.service_name_edit.setText("orcl")
        self.service_name_label = QLabel(format_arabic_text("اسم الخدمة:"))
        db_form.addRow(self.service_name_label, self.service_name_edit)
        
        layout.addWidget(db_group)
        
        # معلومات المصادقة
        auth_group = QGroupBox(format_arabic_text("معلومات المصادقة"))
        auth_form = QFormLayout(auth_group)
        
        # اسم المستخدم
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText(format_arabic_text("اسم المستخدم"))
        auth_form.addRow(format_arabic_text("اسم المستخدم:"), self.username_edit)
        
        # كلمة المرور
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText(format_arabic_text("كلمة المرور"))
        auth_form.addRow(format_arabic_text("كلمة المرور:"), self.password_edit)
        
        # إظهار كلمة المرور
        self.show_password_check = QCheckBox(format_arabic_text("إظهار كلمة المرور"))
        self.show_password_check.toggled.connect(self.toggle_password_visibility)
        auth_form.addRow("", self.show_password_check)
        
        layout.addWidget(auth_group)
        
        # حالة الاتصال
        status_group = QGroupBox(format_arabic_text("حالة الاتصال"))
        status_form = QFormLayout(status_group)
        
        self.active_check = QCheckBox(format_arabic_text("تفعيل الاتصال"))
        self.active_check.setChecked(True)
        status_form.addRow("", self.active_check)
        
        layout.addWidget(status_group)
        
        self.tabs.addTab(basic_widget, format_arabic_text("الإعدادات الأساسية"))
        
    def create_advanced_settings_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        advanced_widget = QWidget()
        layout = QVBoxLayout(advanced_widget)
        
        # إعدادات الاتصال المتقدمة
        conn_group = QGroupBox(format_arabic_text("إعدادات الاتصال المتقدمة"))
        conn_form = QFormLayout(conn_group)
        
        # مهلة الاتصال
        self.connection_timeout_spin = QSpinBox()
        self.connection_timeout_spin.setRange(5, 300)
        self.connection_timeout_spin.setValue(30)
        self.connection_timeout_spin.setSuffix(" ثانية")
        conn_form.addRow(format_arabic_text("مهلة الاتصال:"), self.connection_timeout_spin)
        
        # مهلة الاستعلام
        self.query_timeout_spin = QSpinBox()
        self.query_timeout_spin.setRange(10, 3600)
        self.query_timeout_spin.setValue(60)
        self.query_timeout_spin.setSuffix(" ثانية")
        conn_form.addRow(format_arabic_text("مهلة الاستعلام:"), self.query_timeout_spin)
        
        # تشفير الاتصال
        self.ssl_check = QCheckBox(format_arabic_text("استخدام SSL/TLS"))
        conn_form.addRow("", self.ssl_check)
        
        layout.addWidget(conn_group)
        
        # إعدادات مجموعة الاتصالات
        pool_group = QGroupBox(format_arabic_text("إعدادات مجموعة الاتصالات"))
        pool_form = QFormLayout(pool_group)
        
        # الحد الأدنى للاتصالات
        self.min_connections_spin = QSpinBox()
        self.min_connections_spin.setRange(1, 50)
        self.min_connections_spin.setValue(2)
        pool_form.addRow(format_arabic_text("الحد الأدنى:"), self.min_connections_spin)
        
        # الحد الأقصى للاتصالات
        self.max_connections_spin = QSpinBox()
        self.max_connections_spin.setRange(1, 100)
        self.max_connections_spin.setValue(10)
        pool_form.addRow(format_arabic_text("الحد الأقصى:"), self.max_connections_spin)
        
        layout.addWidget(pool_group)
        
        # سلسلة الاتصال المخصصة
        custom_group = QGroupBox(format_arabic_text("سلسلة الاتصال المخصصة"))
        custom_layout = QVBoxLayout(custom_group)
        
        self.custom_connection_check = QCheckBox(format_arabic_text("استخدام سلسلة اتصال مخصصة"))
        self.custom_connection_check.toggled.connect(self.toggle_custom_connection)
        custom_layout.addWidget(self.custom_connection_check)
        
        self.custom_connection_edit = QTextEdit()
        self.custom_connection_edit.setMaximumHeight(100)
        self.custom_connection_edit.setEnabled(False)
        self.custom_connection_edit.setPlaceholderText(
            format_arabic_text("أدخل سلسلة الاتصال المخصصة هنا...")
        )
        custom_layout.addWidget(self.custom_connection_edit)
        
        layout.addWidget(custom_group)
        
        self.tabs.addTab(advanced_widget, format_arabic_text("الإعدادات المتقدمة"))
        
    def create_test_tab(self):
        """إنشاء تبويب اختبار الاتصال"""
        test_widget = QWidget()
        layout = QVBoxLayout(test_widget)
        
        # معلومات الاختبار
        info_group = QGroupBox(format_arabic_text("معلومات الاختبار"))
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel(format_arabic_text("""
اختبار الاتصال يتحقق من:
• إمكانية الوصول للخادم
• صحة بيانات المصادقة
• توفر قاعدة البيانات
• سرعة الاستجابة
        """))
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        # منطقة الاختبار
        test_group = QGroupBox(format_arabic_text("تنفيذ الاختبار"))
        test_layout = QVBoxLayout(test_group)
        
        # زر الاختبار
        self.test_button = QPushButton(format_arabic_text("🔍 اختبار الاتصال"))
        self.test_button.setFixedHeight(40)
        self.test_button.clicked.connect(self.test_connection)
        test_layout.addWidget(self.test_button)
        
        # شريط التقدم
        self.test_progress = QProgressBar()
        self.test_progress.setVisible(False)
        test_layout.addWidget(self.test_progress)
        
        # نتائج الاختبار
        self.test_results = QTextEdit()
        self.test_results.setReadOnly(True)
        self.test_results.setMaximumHeight(200)
        self.test_results.setPlaceholderText(format_arabic_text("نتائج الاختبار ستظهر هنا..."))
        test_layout.addWidget(self.test_results)
        
        layout.addWidget(test_group)
        
        self.tabs.addTab(test_widget, format_arabic_text("اختبار الاتصال"))
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        self.buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_button = QPushButton(format_arabic_text("💾 حفظ"))
        self.save_button.setFixedHeight(35)
        self.save_button.clicked.connect(self.save_connection)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        
        # زر الإلغاء
        self.cancel_button = QPushButton(format_arabic_text("❌ إلغاء"))
        self.cancel_button.setFixedHeight(35)
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        # زر اختبار سريع
        self.quick_test_button = QPushButton(format_arabic_text("⚡ اختبار سريع"))
        self.quick_test_button.setFixedHeight(35)
        self.quick_test_button.clicked.connect(self.quick_test)
        
        self.buttons_layout.addWidget(self.quick_test_button)
        self.buttons_layout.addStretch()
        self.buttons_layout.addWidget(self.save_button)
        self.buttons_layout.addWidget(self.cancel_button)
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث الحقول عند تغيير نوع قاعدة البيانات
        self.on_db_type_changed()
        
    def on_db_type_changed(self):
        """عند تغيير نوع قاعدة البيانات"""
        db_type = self.db_type_combo.currentData()
        
        if db_type == DatabaseType.ORACLE:
            self.port_spin.setValue(1521)
            self.service_name_label.setVisible(True)
            self.service_name_edit.setVisible(True)
            self.database_label.setText(format_arabic_text("اسم قاعدة البيانات (اختياري):"))
            
        elif db_type == DatabaseType.MYSQL:
            self.port_spin.setValue(3306)
            self.service_name_label.setVisible(False)
            self.service_name_edit.setVisible(False)
            self.database_label.setText(format_arabic_text("اسم قاعدة البيانات:"))
            
        elif db_type == DatabaseType.POSTGRESQL:
            self.port_spin.setValue(5432)
            self.service_name_label.setVisible(False)
            self.service_name_edit.setVisible(False)
            self.database_label.setText(format_arabic_text("اسم قاعدة البيانات:"))
            
        elif db_type == DatabaseType.SQLITE:
            self.port_spin.setValue(0)
            self.service_name_label.setVisible(False)
            self.service_name_edit.setVisible(False)
            self.database_label.setText(format_arabic_text("مسار ملف قاعدة البيانات:"))
    
    def toggle_password_visibility(self, checked):
        """تبديل إظهار كلمة المرور"""
        if checked:
            self.password_edit.setEchoMode(QLineEdit.Normal)
        else:
            self.password_edit.setEchoMode(QLineEdit.Password)
    
    def toggle_custom_connection(self, checked):
        """تبديل سلسلة الاتصال المخصصة"""
        self.custom_connection_edit.setEnabled(checked)
        
        # تعطيل الحقول الأخرى إذا تم تفعيل السلسلة المخصصة
        fields = [
            self.host_edit, self.port_spin, self.database_edit,
            self.service_name_edit, self.username_edit, self.password_edit
        ]
        
        for field in fields:
            field.setEnabled(not checked)
    
    def load_connection_data(self):
        """تحميل بيانات الاتصال للتعديل"""
        if not self.connection:
            return
            
        self.name_edit.setText(self.connection.name)
        
        # تحديد نوع قاعدة البيانات
        for i in range(self.db_type_combo.count()):
            if self.db_type_combo.itemData(i) == self.connection.db_type:
                self.db_type_combo.setCurrentIndex(i)
                break
        
        self.host_edit.setText(self.connection.host)
        self.port_spin.setValue(self.connection.port)
        self.database_edit.setText(self.connection.database)
        self.service_name_edit.setText(self.connection.service_name)
        self.username_edit.setText(self.connection.username)
        self.password_edit.setText(self.connection.password)
        self.active_check.setChecked(self.connection.is_active)
    
    def create_connection_from_form(self) -> DatabaseConnection:
        """إنشاء كائن اتصال من بيانات النموذج"""
        return DatabaseConnection(
            name=self.name_edit.text().strip(),
            db_type=self.db_type_combo.currentData(),
            host=self.host_edit.text().strip(),
            port=self.port_spin.value(),
            database=self.database_edit.text().strip(),
            service_name=self.service_name_edit.text().strip(),
            username=self.username_edit.text().strip(),
            password=self.password_edit.text(),
            is_active=self.active_check.isChecked()
        )
    
    def validate_form(self) -> tuple[bool, str]:
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            return False, "يجب إدخال اسم الاتصال"
        
        if not self.host_edit.text().strip():
            return False, "يجب إدخال عنوان الخادم"
        
        if not self.username_edit.text().strip():
            return False, "يجب إدخال اسم المستخدم"
        
        db_type = self.db_type_combo.currentData()
        if db_type != DatabaseType.SQLITE and not self.password_edit.text():
            return False, "يجب إدخال كلمة المرور"
        
        if db_type in [DatabaseType.MYSQL, DatabaseType.POSTGRESQL] and not self.database_edit.text().strip():
            return False, "يجب إدخال اسم قاعدة البيانات"
        
        return True, ""
    
    def test_connection(self):
        """اختبار الاتصال"""
        # التحقق من صحة البيانات أولاً
        is_valid, error_msg = self.validate_form()
        if not is_valid:
            QMessageBox.warning(self, "خطأ في البيانات", format_arabic_text(error_msg))
            return
        
        # إنشاء كائن الاتصال للاختبار
        test_connection = self.create_connection_from_form()
        
        # بدء الاختبار
        self.test_button.setEnabled(False)
        self.test_progress.setVisible(True)
        self.test_progress.setValue(0)
        
        self.test_results.clear()
        self.test_results.append(format_arabic_text("🔄 بدء اختبار الاتصال..."))
        
        # إنشاء خيط الاختبار
        self.test_thread = ConnectionTestThread(self.manager, test_connection)
        self.test_thread.test_finished.connect(self.on_test_finished)
        self.test_thread.progress_updated.connect(self.test_progress.setValue)
        self.test_thread.start()
    
    def on_test_finished(self, success, message):
        """عند انتهاء اختبار الاتصال"""
        self.test_button.setEnabled(True)
        self.test_progress.setVisible(False)
        
        if success:
            self.test_results.append(format_arabic_text(f"✅ {message}"))
            self.test_results.setStyleSheet("color: green;")
        else:
            self.test_results.append(format_arabic_text(f"❌ {message}"))
            self.test_results.setStyleSheet("color: red;")
    
    def quick_test(self):
        """اختبار سريع"""
        self.tabs.setCurrentIndex(2)  # الانتقال لتبويب الاختبار
        self.test_connection()
    
    def save_connection(self):
        """حفظ الاتصال"""
        # التحقق من صحة البيانات
        is_valid, error_msg = self.validate_form()
        if not is_valid:
            QMessageBox.warning(self, "خطأ في البيانات", format_arabic_text(error_msg))
            return
        
        # إنشاء كائن الاتصال
        connection = self.create_connection_from_form()
        
        try:
            if self.is_edit_mode:
                # تحديث الاتصال الموجود
                self.manager.connections[connection.name] = connection
                self.manager.save_connections()
                QMessageBox.information(self, "نجح", format_arabic_text("تم تحديث الاتصال بنجاح"))
            else:
                # إضافة اتصال جديد
                if self.manager.add_connection(connection):
                    QMessageBox.information(self, "نجح", format_arabic_text("تم إضافة الاتصال بنجاح"))
                else:
                    QMessageBox.warning(self, "خطأ", format_arabic_text("فشل في إضافة الاتصال"))
                    return
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", format_arabic_text(f"خطأ في حفظ الاتصال: {str(e)}"))

def main():
    """اختبار النافذة"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق للنصوص العربية
    from arabic_text_helper import setup_arabic_application
    setup_arabic_application(app)
    
    # إنشاء النافذة
    dialog = DatabaseConnectionDialog()
    
    if dialog.exec() == QDialog.Accepted:
        print("تم حفظ الاتصال")
    else:
        print("تم إلغاء العملية")

if __name__ == "__main__":
    main()
