#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية جدول S_ERP_SYSTEM
"""

import cx_Oracle

def main():
    """فحص بنية الجدول"""
    try:
        print("🔄 الاتصال بقاعدة البيانات...")
        conn = cx_Oracle.connect('ship2025', 'ys123', 'localhost:1521/orcl')
        cursor = conn.cursor()
        print("✅ تم الاتصال")
        
        # فحص بنية الجدول
        print("\n📋 بنية جدول S_ERP_SYSTEM:")
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, DATA_DEFAULT
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'S_ERP_SYSTEM'
            ORDER BY COLUMN_ID
        """)
        
        columns = cursor.fetchall()
        print("-" * 80)
        print(f"{'العمود':<20} {'النوع':<15} {'الطول':<10} {'NULL':<8} {'افتراضي':<15}")
        print("-" * 80)
        
        for col in columns:
            column_name, data_type, data_length, nullable, default_val = col
            default_str = str(default_val) if default_val else ''
            print(f"{column_name:<20} {data_type:<15} {str(data_length):<10} {nullable:<8} {default_str:<15}")
        
        # عرض عينة من البيانات
        print(f"\n📊 عينة من البيانات (آخر 5 سجلات):")
        cursor.execute("""
            SELECT * FROM (
                SELECT * FROM S_ERP_SYSTEM ORDER BY SYS_NO DESC
            ) WHERE ROWNUM <= 5
        """)
        
        sample_data = cursor.fetchall()
        print("-" * 100)
        
        for row in sample_data:
            print(f"SYS_NO: {row[0]}, SYS_CODE: {row[1]}, SYS_NAME: {row[2]}, SYS_PARNT: {row[3]}, ORDR_NO: {row[4]}")
        
        # فحص أعلى SYS_NO
        print(f"\n📈 أعلى SYS_NO:")
        cursor.execute("SELECT MAX(SYS_NO) FROM S_ERP_SYSTEM")
        max_id = cursor.fetchone()[0]
        print(f"   أعلى SYS_NO: {max_id}")
        
        cursor.close()
        conn.close()
        print("\n✅ تم الانتهاء")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
