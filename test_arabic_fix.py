#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Test Arabic Text Fix
اختبار إصلاح النصوص العربية
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, <PERSON><PERSON><PERSON><PERSON>
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from arabic_text_helper import format_arabic_text, setup_arabic_widget, setup_arabic_application

class TestWindow(QMainWindow):
    """نافذة اختبار النصوص العربية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار النصوص العربية - SHP ERP")
        self.setGeometry(300, 300, 600, 400)
        
        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # نصوص تجريبية
        test_texts = [
            "مرحباً بك في نظام SHP ERP",
            "نظام تخطيط موارد المؤسسة البحرية",
            "قاعدة البيانات: Oracle Database",
            "المستخدم: ship2025",
            "عدد الأنظمة: 21 نظام نشط",
            "الحالة: متصل بنجاح ✅"
        ]
        
        for text in test_texts:
            label = QLabel(format_arabic_text(text))
            label.setAlignment(Qt.AlignCenter)
            label.setFont(QFont("Tahoma", 12))
            label.setStyleSheet("""
                QLabel {
                    padding: 10px;
                    margin: 5px;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    background-color: #f9f9f9;
                }
            """)
            layout.addWidget(label)
        
        # رسالة النتيجة
        result_label = QLabel("إذا كانت النصوص أعلاه تظهر بشكل صحيح، فقد تم حل المشكلة!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setFont(QFont("Tahoma", 10, QFont.Bold))
        result_label.setStyleSheet("""
            QLabel {
                color: green;
                padding: 15px;
                background-color: #e8f5e8;
                border: 2px solid green;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(result_label)

def main():
    """الدالة الرئيسية"""
    print("🔄 اختبار إصلاح النصوص العربية...")
    
    app = QApplication(sys.argv)
    
    # إعداد التطبيق للنصوص العربية
    setup_arabic_application(app)
    
    # إنشاء نافذة الاختبار
    window = TestWindow()
    window.show()
    
    print("✅ تم فتح نافذة الاختبار")
    print("👀 تحقق من النافذة لرؤية النصوص العربية")
    print("🔚 اضغط Ctrl+C لإغلاق النافذة")
    
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n👋 تم إغلاق نافذة الاختبار")

if __name__ == "__main__":
    main()
