#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - General Settings System
نظام الإعدادات العامة الشامل والمتقدم
"""

import sys
import json
import os
from datetime import datetime, date
from typing import Dict, List, Any, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox, QFormLayout,
    QGridLayout, QFrame, QScrollArea, QColorDialog, QFontDialog,
    QFileDialog, QMessageBox, QDateEdit, QTimeEdit, QSlider,
    QProgressBar, QListWidget, QListWidgetItem, QTableWidget,
    QTableWidgetItem, Q<PERSON><PERSON>er<PERSON>iew, <PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ree<PERSON>idge<PERSON>, QTreeWidgetItem
)
from PySide6.QtCore import Qt, QDate, QTime, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QIcon

from arabic_text_helper import format_arabic_text, setup_arabic_widget

class GeneralSettingsSystem(QMainWindow):
    """نظام الإعدادات العامة الشامل"""
    
    def __init__(self):
        super().__init__()
        self.settings_file = "general_settings.json"
        self.settings_data = {}
        
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(format_arabic_text("SHP ERP - نظام الإعدادات العامة"))
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        
        # القائمة الجانبية
        self.create_sidebar()
        
        # منطقة المحتوى الرئيسي
        self.create_main_content()
        
        # إضافة المكونات
        main_layout.addWidget(self.sidebar, 1)
        main_layout.addWidget(self.main_content, 3)
        
        # تطبيق الأنماط
        self.apply_styles()
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QFrame()
        self.sidebar.setFrameStyle(QFrame.StyledPanel)
        self.sidebar.setMaximumWidth(300)
        
        layout = QVBoxLayout(self.sidebar)
        
        # عنوان القسم
        title = QLabel(format_arabic_text("الإعدادات العامة"))
        title.setFont(QFont("Tahoma", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # قائمة الإعدادات
        self.settings_list = QListWidget()
        self.settings_list.setLayoutDirection(Qt.RightToLeft)
        
        # إضافة عناصر القائمة
        settings_items = [
            {"icon": "🎨", "name": "إعدادات المظهر", "key": "appearance"},
            {"icon": "🏢", "name": "بيانات الشركة", "key": "company"},
            {"icon": "💰", "name": "إعدادات العملات", "key": "currencies"},
            {"icon": "📅", "name": "السنة المالية", "key": "fiscal_year"},
            {"icon": "🌐", "name": "إعدادات اللغة", "key": "language"},
            {"icon": "🔐", "name": "إعدادات الأمان", "key": "security"},
            {"icon": "📧", "name": "إعدادات البريد", "key": "email"},
            {"icon": "🖨️", "name": "إعدادات الطباعة", "key": "printing"},
            {"icon": "📊", "name": "إعدادات التقارير", "key": "reports"},
            {"icon": "🔄", "name": "النسخ الاحتياطي", "key": "backup"},
            {"icon": "⚙️", "name": "إعدادات النظام", "key": "system"},
            {"icon": "📱", "name": "إعدادات الإشعارات", "key": "notifications"}
        ]
        
        for item_data in settings_items:
            item = QListWidgetItem(f"{item_data['icon']} {item_data['name']}")
            item.setData(Qt.UserRole, item_data['key'])
            item.setFont(QFont("Tahoma", 11))
            self.settings_list.addItem(item)
        
        self.settings_list.itemClicked.connect(self.on_setting_selected)
        layout.addWidget(self.settings_list)
        
        # أزرار الإجراءات
        buttons_layout = QVBoxLayout()
        
        self.save_btn = QPushButton(format_arabic_text("💾 حفظ الإعدادات"))
        self.save_btn.clicked.connect(self.save_settings)
        
        self.reset_btn = QPushButton(format_arabic_text("🔄 إعادة تعيين"))
        self.reset_btn.clicked.connect(self.reset_settings)
        
        self.export_btn = QPushButton(format_arabic_text("📤 تصدير الإعدادات"))
        self.export_btn.clicked.connect(self.export_settings)
        
        self.import_btn = QPushButton(format_arabic_text("📥 استيراد الإعدادات"))
        self.import_btn.clicked.connect(self.import_settings)
        
        for btn in [self.save_btn, self.reset_btn, self.export_btn, self.import_btn]:
            btn.setFont(QFont("Tahoma", 10))
            buttons_layout.addWidget(btn)
        
        layout.addLayout(buttons_layout)
        
    def create_main_content(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        self.main_content = QFrame()
        self.main_content.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(self.main_content)
        
        # عنوان القسم المحدد
        self.section_title = QLabel(format_arabic_text("اختر قسم من القائمة الجانبية"))
        self.section_title.setFont(QFont("Tahoma", 16, QFont.Bold))
        self.section_title.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.section_title)
        
        # منطقة التمرير للمحتوى
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setLayoutDirection(Qt.RightToLeft)
        
        # ويدجت المحتوى
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        
        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)
        
        # عرض الشاشة الافتراضية
        self.show_welcome_screen()
        
    def show_welcome_screen(self):
        """عرض شاشة الترحيب"""
        self.clear_content()
        
        welcome_layout = QVBoxLayout()
        welcome_layout.setAlignment(Qt.AlignCenter)
        
        # أيقونة كبيرة
        icon_label = QLabel("⚙️")
        icon_label.setFont(QFont("Arial", 72))
        icon_label.setAlignment(Qt.AlignCenter)
        welcome_layout.addWidget(icon_label)
        
        # نص الترحيب
        welcome_text = QLabel(format_arabic_text("مرحباً بك في نظام الإعدادات العامة"))
        welcome_text.setFont(QFont("Tahoma", 18, QFont.Bold))
        welcome_text.setAlignment(Qt.AlignCenter)
        welcome_layout.addWidget(welcome_text)
        
        # وصف
        desc_text = QLabel(format_arabic_text("اختر قسماً من القائمة الجانبية لبدء تخصيص إعدادات النظام"))
        desc_text.setFont(QFont("Tahoma", 12))
        desc_text.setAlignment(Qt.AlignCenter)
        desc_text.setWordWrap(True)
        welcome_layout.addWidget(desc_text)
        
        self.content_layout.addLayout(welcome_layout)
        
    def clear_content(self):
        """مسح المحتوى الحالي"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
    def on_setting_selected(self, item):
        """عند اختيار إعداد من القائمة"""
        setting_key = item.data(Qt.UserRole)
        setting_name = item.text().split(' ', 1)[1]  # إزالة الأيقونة
        
        self.section_title.setText(format_arabic_text(setting_name))
        
        # عرض الشاشة المناسبة
        if setting_key == "appearance":
            self.show_appearance_settings()
        elif setting_key == "company":
            self.show_company_settings()
        elif setting_key == "currencies":
            self.show_currency_settings()
        elif setting_key == "fiscal_year":
            self.show_fiscal_year_settings()
        elif setting_key == "language":
            self.show_language_settings()
        elif setting_key == "security":
            self.show_security_settings()
        elif setting_key == "email":
            self.show_email_settings()
        elif setting_key == "printing":
            self.show_printing_settings()
        elif setting_key == "reports":
            self.show_reports_settings()
        elif setting_key == "backup":
            self.show_backup_settings()
        elif setting_key == "system":
            self.show_system_settings()
        elif setting_key == "notifications":
            self.show_notifications_settings()
        else:
            self.show_welcome_screen()
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 5px;
            }
            
            QListWidget {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                font-family: "Tahoma";
                font-size: 11px;
                padding: 5px;
            }
            
            QListWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #F0F0F0;
                border-radius: 4px;
                margin: 2px;
            }
            
            QListWidget::item:hover {
                background-color: #E8F5E8;
                color: #2E7D32;
            }
            
            QListWidget::item:selected {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
            }
            
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 15px;
                font-family: "Tahoma";
                font-size: 11px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            
            QLabel {
                color: #333333;
                font-family: "Tahoma";
            }
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                border: 2px solid #E0E0E0;
                border-radius: 6px;
                padding: 8px;
                font-family: "Tahoma";
                font-size: 11px;
                background-color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #4CAF50;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                font-family: "Tahoma";
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #4CAF50;
            }
        """)
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.settings_data = json.load(f)
            else:
                self.settings_data = self.get_default_settings()
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل الإعدادات: {str(e)}")
            self.settings_data = self.get_default_settings()
    
    def get_default_settings(self):
        """الحصول على الإعدادات الافتراضية"""
        return {
            "appearance": {
                "theme": "light",
                "font_family": "Tahoma",
                "font_size": 11,
                "primary_color": "#4CAF50",
                "secondary_color": "#2196F3"
            },
            "company": {
                "name": "شركة SHP للبرمجيات",
                "name_en": "SHP Software Company",
                "address": "",
                "phone": "",
                "email": "",
                "website": "",
                "tax_number": "",
                "commercial_register": ""
            },
            "currencies": {
                "base_currency": "YER",
                "currencies": [
                    {"code": "YER", "name": "ريال يمني", "symbol": "﷼", "rate": 1.0},
                    {"code": "USD", "name": "دولار أمريكي", "symbol": "$", "rate": 0.004},
                    {"code": "SAR", "name": "ريال سعودي", "symbol": "ر.س", "rate": 0.015}
                ]
            },
            "fiscal_year": {
                "start_date": "2025-01-01",
                "end_date": "2025-12-31",
                "current_period": 1
            }
        }
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings_data, f, ensure_ascii=False, indent=2)
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.settings_data = self.get_default_settings()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات")
    
    def export_settings(self):
        """تصدير الإعدادات"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "تصدير الإعدادات", 
            f"settings_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.settings_data, f, ensure_ascii=False, indent=2)
                QMessageBox.information(self, "نجح", "تم تصدير الإعدادات بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تصدير الإعدادات: {str(e)}")
    
    def import_settings(self):
        """استيراد الإعدادات"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "استيراد الإعدادات", "",
            "JSON Files (*.json)"
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    imported_settings = json.load(f)
                self.settings_data.update(imported_settings)
                QMessageBox.information(self, "نجح", "تم استيراد الإعدادات بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في استيراد الإعدادات: {str(e)}")

    def show_appearance_settings(self):
        """عرض إعدادات المظهر"""
        self.clear_content()

        # مجموعة الألوان والثيمات
        theme_group = QGroupBox(format_arabic_text("الثيمات والألوان"))
        theme_layout = QFormLayout(theme_group)

        # اختيار الثيم
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن", "تلقائي"])
        theme_layout.addRow(format_arabic_text("الثيم:"), self.theme_combo)

        # اللون الأساسي
        self.primary_color_btn = QPushButton("اختيار اللون الأساسي")
        self.primary_color_btn.clicked.connect(lambda: self.choose_color("primary"))
        theme_layout.addRow(format_arabic_text("اللون الأساسي:"), self.primary_color_btn)

        # اللون الثانوي
        self.secondary_color_btn = QPushButton("اختيار اللون الثانوي")
        self.secondary_color_btn.clicked.connect(lambda: self.choose_color("secondary"))
        theme_layout.addRow(format_arabic_text("اللون الثانوي:"), self.secondary_color_btn)

        self.content_layout.addWidget(theme_group)

        # مجموعة الخطوط
        font_group = QGroupBox(format_arabic_text("إعدادات الخطوط"))
        font_layout = QFormLayout(font_group)

        # نوع الخط
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems(["Tahoma", "Arial", "Calibri", "Times New Roman", "Segoe UI"])
        font_layout.addRow(format_arabic_text("نوع الخط:"), self.font_family_combo)

        # حجم الخط
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(11)
        font_layout.addRow(format_arabic_text("حجم الخط:"), self.font_size_spin)

        # خط عريض
        self.bold_font_check = QCheckBox(format_arabic_text("خط عريض"))
        font_layout.addRow("", self.bold_font_check)

        self.content_layout.addWidget(font_group)

        # مجموعة التخطيط
        layout_group = QGroupBox(format_arabic_text("إعدادات التخطيط"))
        layout_layout = QFormLayout(layout_group)

        # اتجاه النص
        self.text_direction_combo = QComboBox()
        self.text_direction_combo.addItems(["من اليمين لليسار", "من اليسار لليمين"])
        layout_layout.addRow(format_arabic_text("اتجاه النص:"), self.text_direction_combo)

        # عرض الشريط الجانبي
        self.sidebar_width_spin = QSpinBox()
        self.sidebar_width_spin.setRange(200, 500)
        self.sidebar_width_spin.setValue(300)
        layout_layout.addRow(format_arabic_text("عرض الشريط الجانبي:"), self.sidebar_width_spin)

        self.content_layout.addWidget(layout_group)

        # معاينة
        preview_group = QGroupBox(format_arabic_text("معاينة"))
        preview_layout = QVBoxLayout(preview_group)

        self.preview_label = QLabel(format_arabic_text("هذا نص تجريبي لمعاينة الإعدادات"))
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("padding: 20px; border: 1px solid #ccc; border-radius: 5px;")
        preview_layout.addWidget(self.preview_label)

        self.content_layout.addWidget(preview_group)

    def choose_color(self, color_type):
        """اختيار لون"""
        color = QColorDialog.getColor()
        if color.isValid():
            if color_type == "primary":
                self.primary_color_btn.setStyleSheet(f"background-color: {color.name()};")
            elif color_type == "secondary":
                self.secondary_color_btn.setStyleSheet(f"background-color: {color.name()};")

    def show_company_settings(self):
        """عرض إعدادات الشركة"""
        self.clear_content()

        # معلومات الشركة الأساسية
        basic_group = QGroupBox(format_arabic_text("المعلومات الأساسية"))
        basic_layout = QFormLayout(basic_group)

        self.company_name_ar = QLineEdit()
        self.company_name_ar.setPlaceholderText("اسم الشركة بالعربية")
        basic_layout.addRow(format_arabic_text("اسم الشركة (عربي):"), self.company_name_ar)

        self.company_name_en = QLineEdit()
        self.company_name_en.setPlaceholderText("Company Name in English")
        basic_layout.addRow(format_arabic_text("اسم الشركة (إنجليزي):"), self.company_name_en)

        self.company_address = QTextEdit()
        self.company_address.setMaximumHeight(80)
        self.company_address.setPlaceholderText("عنوان الشركة التفصيلي")
        basic_layout.addRow(format_arabic_text("العنوان:"), self.company_address)

        self.content_layout.addWidget(basic_group)

        # معلومات الاتصال
        contact_group = QGroupBox(format_arabic_text("معلومات الاتصال"))
        contact_layout = QFormLayout(contact_group)

        self.company_phone = QLineEdit()
        self.company_phone.setPlaceholderText("+967-1-234567")
        contact_layout.addRow(format_arabic_text("الهاتف:"), self.company_phone)

        self.company_mobile = QLineEdit()
        self.company_mobile.setPlaceholderText("+967-77-1234567")
        contact_layout.addRow(format_arabic_text("الجوال:"), self.company_mobile)

        self.company_email = QLineEdit()
        self.company_email.setPlaceholderText("<EMAIL>")
        contact_layout.addRow(format_arabic_text("البريد الإلكتروني:"), self.company_email)

        self.company_website = QLineEdit()
        self.company_website.setPlaceholderText("www.company.com")
        contact_layout.addRow(format_arabic_text("الموقع الإلكتروني:"), self.company_website)

        self.content_layout.addWidget(contact_group)

        # المعلومات القانونية
        legal_group = QGroupBox(format_arabic_text("المعلومات القانونية"))
        legal_layout = QFormLayout(legal_group)

        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("الرقم الضريبي")
        legal_layout.addRow(format_arabic_text("الرقم الضريبي:"), self.tax_number)

        self.commercial_register = QLineEdit()
        self.commercial_register.setPlaceholderText("رقم السجل التجاري")
        legal_layout.addRow(format_arabic_text("السجل التجاري:"), self.commercial_register)

        self.license_number = QLineEdit()
        self.license_number.setPlaceholderText("رقم الترخيص")
        legal_layout.addRow(format_arabic_text("رقم الترخيص:"), self.license_number)

        self.content_layout.addWidget(legal_group)

        # الشعار
        logo_group = QGroupBox(format_arabic_text("شعار الشركة"))
        logo_layout = QVBoxLayout(logo_group)

        logo_buttons_layout = QHBoxLayout()

        self.upload_logo_btn = QPushButton(format_arabic_text("📁 رفع شعار"))
        self.upload_logo_btn.clicked.connect(self.upload_logo)

        self.remove_logo_btn = QPushButton(format_arabic_text("🗑️ إزالة الشعار"))
        self.remove_logo_btn.clicked.connect(self.remove_logo)

        logo_buttons_layout.addWidget(self.upload_logo_btn)
        logo_buttons_layout.addWidget(self.remove_logo_btn)

        self.logo_preview = QLabel("لا يوجد شعار")
        self.logo_preview.setAlignment(Qt.AlignCenter)
        self.logo_preview.setStyleSheet("border: 2px dashed #ccc; padding: 20px; min-height: 100px;")

        logo_layout.addLayout(logo_buttons_layout)
        logo_layout.addWidget(self.logo_preview)

        self.content_layout.addWidget(logo_group)

    def upload_logo(self):
        """رفع شعار الشركة"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "اختيار شعار الشركة", "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if filename:
            pixmap = QPixmap(filename)
            if not pixmap.isNull():
                # تغيير حجم الصورة
                scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.logo_preview.setPixmap(scaled_pixmap)
                self.logo_preview.setText("")

    def remove_logo(self):
        """إزالة شعار الشركة"""
        self.logo_preview.clear()
        self.logo_preview.setText("لا يوجد شعار")

    def show_currency_settings(self):
        """عرض إعدادات العملات"""
        self.clear_content()

        # العملة الأساسية
        base_currency_group = QGroupBox(format_arabic_text("العملة الأساسية"))
        base_layout = QFormLayout(base_currency_group)

        self.base_currency_combo = QComboBox()
        self.base_currency_combo.addItems(["ريال يمني (YER)", "دولار أمريكي (USD)", "ريال سعودي (SAR)"])
        base_layout.addRow(format_arabic_text("العملة الأساسية:"), self.base_currency_combo)

        self.content_layout.addWidget(base_currency_group)

        # جدول العملات
        currencies_group = QGroupBox(format_arabic_text("إدارة العملات"))
        currencies_layout = QVBoxLayout(currencies_group)

        # أزرار الإدارة
        currency_buttons_layout = QHBoxLayout()

        self.add_currency_btn = QPushButton(format_arabic_text("➕ إضافة عملة"))
        self.add_currency_btn.clicked.connect(self.add_currency)

        self.edit_currency_btn = QPushButton(format_arabic_text("✏️ تعديل"))
        self.edit_currency_btn.clicked.connect(self.edit_currency)

        self.delete_currency_btn = QPushButton(format_arabic_text("🗑️ حذف"))
        self.delete_currency_btn.clicked.connect(self.delete_currency)

        self.update_rates_btn = QPushButton(format_arabic_text("🔄 تحديث الأسعار"))
        self.update_rates_btn.clicked.connect(self.update_exchange_rates)

        currency_buttons_layout.addWidget(self.add_currency_btn)
        currency_buttons_layout.addWidget(self.edit_currency_btn)
        currency_buttons_layout.addWidget(self.delete_currency_btn)
        currency_buttons_layout.addWidget(self.update_rates_btn)
        currency_buttons_layout.addStretch()

        currencies_layout.addLayout(currency_buttons_layout)

        # جدول العملات
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(5)
        self.currencies_table.setHorizontalHeaderLabels([
            format_arabic_text("الرمز"),
            format_arabic_text("الاسم"),
            format_arabic_text("الرمز المالي"),
            format_arabic_text("سعر الصرف"),
            format_arabic_text("آخر تحديث")
        ])

        # إضافة بيانات تجريبية
        sample_currencies = [
            ["YER", "ريال يمني", "﷼", "1.0000", "2025-01-13"],
            ["USD", "دولار أمريكي", "$", "0.0040", "2025-01-13"],
            ["SAR", "ريال سعودي", "ر.س", "0.0150", "2025-01-13"],
            ["EUR", "يورو", "€", "0.0037", "2025-01-13"]
        ]

        self.currencies_table.setRowCount(len(sample_currencies))
        for row, currency in enumerate(sample_currencies):
            for col, value in enumerate(currency):
                self.currencies_table.setItem(row, col, QTableWidgetItem(value))

        self.currencies_table.horizontalHeader().setStretchLastSection(True)
        currencies_layout.addWidget(self.currencies_table)

        self.content_layout.addWidget(currencies_group)

        # إعدادات التقريب
        rounding_group = QGroupBox(format_arabic_text("إعدادات التقريب"))
        rounding_layout = QFormLayout(rounding_group)

        self.decimal_places_spin = QSpinBox()
        self.decimal_places_spin.setRange(0, 6)
        self.decimal_places_spin.setValue(2)
        rounding_layout.addRow(format_arabic_text("عدد الخانات العشرية:"), self.decimal_places_spin)

        self.rounding_method_combo = QComboBox()
        self.rounding_method_combo.addItems(["تقريب عادي", "تقريب لأعلى", "تقريب لأسفل"])
        rounding_layout.addRow(format_arabic_text("طريقة التقريب:"), self.rounding_method_combo)

        self.content_layout.addWidget(rounding_group)

    def add_currency(self):
        """إضافة عملة جديدة"""
        QMessageBox.information(self, "إضافة عملة", "سيتم فتح نافذة إضافة عملة جديدة")

    def edit_currency(self):
        """تعديل عملة"""
        current_row = self.currencies_table.currentRow()
        if current_row >= 0:
            QMessageBox.information(self, "تعديل عملة", "سيتم فتح نافذة تعديل العملة")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للتعديل")

    def delete_currency(self):
        """حذف عملة"""
        current_row = self.currencies_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", "هل تريد حذف هذه العملة؟")
            if reply == QMessageBox.Yes:
                self.currencies_table.removeRow(current_row)
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للحذف")

    def update_exchange_rates(self):
        """تحديث أسعار الصرف"""
        QMessageBox.information(self, "تحديث الأسعار", "سيتم تحديث أسعار الصرف من مصادر خارجية")

    def show_fiscal_year_settings(self):
        """عرض إعدادات السنة المالية"""
        self.clear_content()

        # السنة المالية الحالية
        current_year_group = QGroupBox(format_arabic_text("السنة المالية الحالية"))
        current_layout = QFormLayout(current_year_group)

        self.fiscal_start_date = QDateEdit()
        self.fiscal_start_date.setDate(QDate(2025, 1, 1))
        self.fiscal_start_date.setCalendarPopup(True)
        current_layout.addRow(format_arabic_text("تاريخ البداية:"), self.fiscal_start_date)

        self.fiscal_end_date = QDateEdit()
        self.fiscal_end_date.setDate(QDate(2025, 12, 31))
        self.fiscal_end_date.setCalendarPopup(True)
        current_layout.addRow(format_arabic_text("تاريخ النهاية:"), self.fiscal_end_date)

        self.current_period_spin = QSpinBox()
        self.current_period_spin.setRange(1, 12)
        self.current_period_spin.setValue(1)
        current_layout.addRow(format_arabic_text("الفترة الحالية:"), self.current_period_spin)

        self.content_layout.addWidget(current_year_group)

        # الفترات المالية
        periods_group = QGroupBox(format_arabic_text("الفترات المالية"))
        periods_layout = QVBoxLayout(periods_group)

        # أزرار إدارة الفترات
        periods_buttons_layout = QHBoxLayout()

        self.generate_periods_btn = QPushButton(format_arabic_text("🔄 توليد الفترات"))
        self.generate_periods_btn.clicked.connect(self.generate_periods)

        self.close_period_btn = QPushButton(format_arabic_text("🔒 إغلاق فترة"))
        self.close_period_btn.clicked.connect(self.close_period)

        self.reopen_period_btn = QPushButton(format_arabic_text("🔓 إعادة فتح فترة"))
        self.reopen_period_btn.clicked.connect(self.reopen_period)

        periods_buttons_layout.addWidget(self.generate_periods_btn)
        periods_buttons_layout.addWidget(self.close_period_btn)
        periods_buttons_layout.addWidget(self.reopen_period_btn)
        periods_buttons_layout.addStretch()

        periods_layout.addLayout(periods_buttons_layout)

        # جدول الفترات
        self.periods_table = QTableWidget()
        self.periods_table.setColumnCount(5)
        self.periods_table.setHorizontalHeaderLabels([
            format_arabic_text("الفترة"),
            format_arabic_text("من تاريخ"),
            format_arabic_text("إلى تاريخ"),
            format_arabic_text("الحالة"),
            format_arabic_text("تاريخ الإغلاق")
        ])

        # إضافة بيانات تجريبية للفترات
        sample_periods = [
            ["1", "2025-01-01", "2025-01-31", "مفتوحة", ""],
            ["2", "2025-02-01", "2025-02-28", "مفتوحة", ""],
            ["3", "2025-03-01", "2025-03-31", "مفتوحة", ""],
            ["12", "2024-12-01", "2024-12-31", "مغلقة", "2025-01-05"]
        ]

        self.periods_table.setRowCount(len(sample_periods))
        for row, period in enumerate(sample_periods):
            for col, value in enumerate(period):
                item = QTableWidgetItem(value)
                if col == 3:  # عمود الحالة
                    if value == "مغلقة":
                        item.setBackground(QColor("#ffebee"))
                    else:
                        item.setBackground(QColor("#e8f5e8"))
                self.periods_table.setItem(row, col, item)

        self.periods_table.horizontalHeader().setStretchLastSection(True)
        periods_layout.addWidget(self.periods_table)

        self.content_layout.addWidget(periods_group)

    def generate_periods(self):
        """توليد الفترات المالية"""
        QMessageBox.information(self, "توليد الفترات", "سيتم توليد الفترات المالية تلقائياً")

    def close_period(self):
        """إغلاق فترة مالية"""
        current_row = self.periods_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الإغلاق", "هل تريد إغلاق هذه الفترة المالية؟")
            if reply == QMessageBox.Yes:
                self.periods_table.setItem(current_row, 3, QTableWidgetItem("مغلقة"))
                self.periods_table.setItem(current_row, 4, QTableWidgetItem(datetime.now().strftime("%Y-%m-%d")))
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فترة للإغلاق")

    def reopen_period(self):
        """إعادة فتح فترة مالية"""
        current_row = self.periods_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد إعادة الفتح", "هل تريد إعادة فتح هذه الفترة المالية؟")
            if reply == QMessageBox.Yes:
                self.periods_table.setItem(current_row, 3, QTableWidgetItem("مفتوحة"))
                self.periods_table.setItem(current_row, 4, QTableWidgetItem(""))
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فترة لإعادة الفتح")

    def show_language_settings(self):
        """عرض إعدادات اللغة"""
        self.clear_content()

        # اللغة الافتراضية
        default_lang_group = QGroupBox(format_arabic_text("اللغة الافتراضية"))
        default_layout = QFormLayout(default_lang_group)

        self.default_language_combo = QComboBox()
        self.default_language_combo.addItems(["العربية", "English", "Français"])
        default_layout.addRow(format_arabic_text("اللغة الافتراضية:"), self.default_language_combo)

        self.rtl_support_check = QCheckBox(format_arabic_text("دعم الكتابة من اليمين لليسار"))
        self.rtl_support_check.setChecked(True)
        default_layout.addRow("", self.rtl_support_check)

        self.content_layout.addWidget(default_lang_group)

        # إعدادات التاريخ والوقت
        datetime_group = QGroupBox(format_arabic_text("تنسيق التاريخ والوقت"))
        datetime_layout = QFormLayout(datetime_group)

        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems(["dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd"])
        datetime_layout.addRow(format_arabic_text("تنسيق التاريخ:"), self.date_format_combo)

        self.time_format_combo = QComboBox()
        self.time_format_combo.addItems(["24 ساعة", "12 ساعة"])
        datetime_layout.addRow(format_arabic_text("تنسيق الوقت:"), self.time_format_combo)

        self.content_layout.addWidget(datetime_group)

    def show_security_settings(self):
        """عرض إعدادات الأمان"""
        self.clear_content()

        # إعدادات كلمة المرور
        password_group = QGroupBox(format_arabic_text("إعدادات كلمة المرور"))
        password_layout = QFormLayout(password_group)

        self.min_password_length_spin = QSpinBox()
        self.min_password_length_spin.setRange(4, 20)
        self.min_password_length_spin.setValue(8)
        password_layout.addRow(format_arabic_text("الحد الأدنى لطول كلمة المرور:"), self.min_password_length_spin)

        self.require_uppercase_check = QCheckBox(format_arabic_text("يتطلب أحرف كبيرة"))
        password_layout.addRow("", self.require_uppercase_check)

        self.require_numbers_check = QCheckBox(format_arabic_text("يتطلب أرقام"))
        password_layout.addRow("", self.require_numbers_check)

        self.require_symbols_check = QCheckBox(format_arabic_text("يتطلب رموز خاصة"))
        password_layout.addRow("", self.require_symbols_check)

        self.content_layout.addWidget(password_group)

        # إعدادات الجلسة
        session_group = QGroupBox(format_arabic_text("إعدادات الجلسة"))
        session_layout = QFormLayout(session_group)

        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 480)
        self.session_timeout_spin.setValue(60)
        self.session_timeout_spin.setSuffix(" دقيقة")
        session_layout.addRow(format_arabic_text("انتهاء الجلسة بعد:"), self.session_timeout_spin)

        self.auto_lock_check = QCheckBox(format_arabic_text("قفل تلقائي عند عدم النشاط"))
        session_layout.addRow("", self.auto_lock_check)

        self.content_layout.addWidget(session_group)

    def show_email_settings(self):
        """عرض إعدادات البريد الإلكتروني"""
        self.clear_content()

        # إعدادات خادم البريد الصادر
        smtp_group = QGroupBox(format_arabic_text("خادم البريد الصادر (SMTP)"))
        smtp_layout = QFormLayout(smtp_group)

        self.smtp_server = QLineEdit()
        self.smtp_server.setPlaceholderText("smtp.gmail.com")
        smtp_layout.addRow(format_arabic_text("خادم SMTP:"), self.smtp_server)

        self.smtp_port = QSpinBox()
        self.smtp_port.setRange(1, 65535)
        self.smtp_port.setValue(587)
        smtp_layout.addRow(format_arabic_text("المنفذ:"), self.smtp_port)

        self.smtp_username = QLineEdit()
        self.smtp_username.setPlaceholderText("<EMAIL>")
        smtp_layout.addRow(format_arabic_text("اسم المستخدم:"), self.smtp_username)

        self.smtp_password = QLineEdit()
        self.smtp_password.setEchoMode(QLineEdit.Password)
        smtp_layout.addRow(format_arabic_text("كلمة المرور:"), self.smtp_password)

        self.smtp_ssl_check = QCheckBox(format_arabic_text("استخدام SSL/TLS"))
        self.smtp_ssl_check.setChecked(True)
        smtp_layout.addRow("", self.smtp_ssl_check)

        self.content_layout.addWidget(smtp_group)

        # اختبار الإعدادات
        test_group = QGroupBox(format_arabic_text("اختبار الإعدادات"))
        test_layout = QVBoxLayout(test_group)

        test_buttons_layout = QHBoxLayout()

        self.test_email_btn = QPushButton(format_arabic_text("📧 إرسال بريد تجريبي"))
        self.test_email_btn.clicked.connect(self.test_email_settings)

        test_buttons_layout.addWidget(self.test_email_btn)
        test_buttons_layout.addStretch()

        test_layout.addLayout(test_buttons_layout)
        self.content_layout.addWidget(test_group)

    def test_email_settings(self):
        """اختبار إعدادات البريد الإلكتروني"""
        QMessageBox.information(self, "اختبار البريد", "سيتم إرسال بريد تجريبي لاختبار الإعدادات")

    def show_printing_settings(self):
        """عرض إعدادات الطباعة"""
        self.clear_content()

        # الطابعة الافتراضية
        printer_group = QGroupBox(format_arabic_text("إعدادات الطابعة"))
        printer_layout = QFormLayout(printer_group)

        self.default_printer_combo = QComboBox()
        self.default_printer_combo.addItems(["الطابعة الافتراضية", "HP LaserJet", "Canon Printer"])
        printer_layout.addRow(format_arabic_text("الطابعة الافتراضية:"), self.default_printer_combo)

        self.paper_size_combo = QComboBox()
        self.paper_size_combo.addItems(["A4", "A3", "Letter", "Legal"])
        printer_layout.addRow(format_arabic_text("حجم الورق:"), self.paper_size_combo)

        self.print_quality_combo = QComboBox()
        self.print_quality_combo.addItems(["عادية", "عالية", "مسودة"])
        printer_layout.addRow(format_arabic_text("جودة الطباعة:"), self.print_quality_combo)

        self.content_layout.addWidget(printer_group)

    def show_reports_settings(self):
        """عرض إعدادات التقارير"""
        self.clear_content()

        # إعدادات التقارير العامة
        reports_group = QGroupBox(format_arabic_text("إعدادات التقارير"))
        reports_layout = QFormLayout(reports_group)

        self.default_report_format_combo = QComboBox()
        self.default_report_format_combo.addItems(["PDF", "Excel", "Word", "HTML"])
        reports_layout.addRow(format_arabic_text("تنسيق التقرير الافتراضي:"), self.default_report_format_combo)

        self.auto_open_reports_check = QCheckBox(format_arabic_text("فتح التقارير تلقائياً"))
        reports_layout.addRow("", self.auto_open_reports_check)

        self.content_layout.addWidget(reports_group)

    def show_backup_settings(self):
        """عرض إعدادات النسخ الاحتياطي"""
        self.clear_content()

        # إعدادات النسخ الاحتياطي
        backup_group = QGroupBox(format_arabic_text("إعدادات النسخ الاحتياطي"))
        backup_layout = QFormLayout(backup_group)

        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems(["يومي", "أسبوعي", "شهري", "يدوي"])
        backup_layout.addRow(format_arabic_text("تكرار النسخ:"), self.backup_frequency_combo)

        self.backup_time = QTimeEdit()
        self.backup_time.setTime(QTime(2, 0))
        backup_layout.addRow(format_arabic_text("وقت النسخ:"), self.backup_time)

        self.content_layout.addWidget(backup_group)

    def show_system_settings(self):
        """عرض إعدادات النظام"""
        self.clear_content()

        # إعدادات الأداء
        performance_group = QGroupBox(format_arabic_text("إعدادات الأداء"))
        performance_layout = QFormLayout(performance_group)

        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 1000)
        self.cache_size_spin.setValue(100)
        self.cache_size_spin.setSuffix(" MB")
        performance_layout.addRow(format_arabic_text("حجم الذاكرة المؤقتة:"), self.cache_size_spin)

        self.auto_save_check = QCheckBox(format_arabic_text("حفظ تلقائي"))
        performance_layout.addRow("", self.auto_save_check)

        self.content_layout.addWidget(performance_group)

    def show_notifications_settings(self):
        """عرض إعدادات الإشعارات"""
        self.clear_content()

        # إعدادات الإشعارات
        notifications_group = QGroupBox(format_arabic_text("إعدادات الإشعارات"))
        notifications_layout = QFormLayout(notifications_group)

        self.enable_notifications_check = QCheckBox(format_arabic_text("تفعيل الإشعارات"))
        self.enable_notifications_check.setChecked(True)
        notifications_layout.addRow("", self.enable_notifications_check)

        self.notification_sound_check = QCheckBox(format_arabic_text("صوت الإشعار"))
        notifications_layout.addRow("", self.notification_sound_check)

        self.content_layout.addWidget(notifications_group)

    def open_specific_section(self, system_code):
        """فتح قسم محدد بناءً على رمز النظام"""
        # خريطة ربط رموز الأنظمة بمفاتيح الإعدادات
        code_mapping = {
            'APPEARANCE': 'appearance',
            'COMPANY': 'company',
            'CURRENCIES': 'currencies',
            'FISCALYEAR': 'fiscal_year',
            'LANGUAGE': 'language',
            'SECURITY': 'security',
            'EMAIL': 'email',
            'PRINTING': 'printing',
            'REPORTS': 'reports',
            'BACKUPSETTINGS': 'backup',
            'SYSTEMSETTINGS': 'system',
            'NOTIFICATIONS': 'notifications'
        }

        setting_key = code_mapping.get(system_code)
        if setting_key:
            # البحث عن العنصر في القائمة وتحديده
            for i in range(self.settings_list.count()):
                item = self.settings_list.item(i)
                if item.data(Qt.UserRole) == setting_key:
                    self.settings_list.setCurrentItem(item)
                    self.on_setting_selected(item)
                    break

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق للنصوص العربية
    from arabic_text_helper import setup_arabic_application
    setup_arabic_application(app)
    
    # إنشاء النافذة
    window = GeneralSettingsSystem()
    window.show()
    
    print("🚀 تم تشغيل نظام الإعدادات العامة")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
