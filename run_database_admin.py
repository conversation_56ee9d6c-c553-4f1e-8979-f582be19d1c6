#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Database Administration System Launcher
مشغل نظام إدارة قاعدة البيانات
"""

import sys
import os
from pathlib import Path

# إضافة المسار الحالي لـ Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication, QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap

from arabic_text_helper import format_arabic_text, setup_arabic_application

class DatabaseAdminLauncher(QDialog):
    """مشغل نظام إدارة قاعدة البيانات"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(format_arabic_text("SHP ERP - نظام إدارة قاعدة البيانات Oracle"))
        self.setFixedSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self)
        
        # عنوان النظام
        title = QLabel(format_arabic_text("🚢 SHP ERP"))
        title.setFont(QFont("Tahoma", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النظام
        description = QLabel(format_arabic_text("""
نظام إدارة قاعدة البيانات Oracle المتقدم

🔗 إدارة اتصالات متعددة
💾 نسخ احتياطي ذكي
📝 محرر SQL متطور
📊 إحصائيات شاملة
🌐 واجهة عربية كاملة
        """))
        description.setFont(QFont("Tahoma", 11))
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # أزرار التشغيل
        buttons_layout = QVBoxLayout()
        
        # زر النافذة الرئيسية
        main_btn = QPushButton(format_arabic_text("🏠 النافذة الرئيسية"))
        main_btn.setFont(QFont("Tahoma", 12, QFont.Bold))
        main_btn.setFixedHeight(50)
        main_btn.clicked.connect(self.launch_main_window)
        main_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
        """)
        buttons_layout.addWidget(main_btn)
        
        # زر محرر SQL
        sql_btn = QPushButton(format_arabic_text("📝 محرر SQL المتقدم"))
        sql_btn.setFont(QFont("Tahoma", 12))
        sql_btn.setFixedHeight(45)
        sql_btn.clicked.connect(self.launch_sql_editor)
        sql_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #58d68d, stop:1 #27ae60);
            }
        """)
        buttons_layout.addWidget(sql_btn)
        
        # زر إدارة الاتصالات
        conn_btn = QPushButton(format_arabic_text("🔗 إدارة الاتصالات"))
        conn_btn.setFont(QFont("Tahoma", 12))
        conn_btn.setFixedHeight(45)
        conn_btn.clicked.connect(self.launch_connection_manager)
        conn_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f7dc6f, stop:1 #f39c12);
            }
        """)
        buttons_layout.addWidget(conn_btn)
        
        # زر الاختبار
        test_btn = QPushButton(format_arabic_text("🔍 اختبار النظام"))
        test_btn.setFont(QFont("Tahoma", 12))
        test_btn.setFixedHeight(45)
        test_btn.clicked.connect(self.run_system_test)
        test_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9b59b6, stop:1 #8e44ad);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #bb8fce, stop:1 #9b59b6);
            }
        """)
        buttons_layout.addWidget(test_btn)
        
        layout.addLayout(buttons_layout)
        
        # زر الخروج
        exit_layout = QHBoxLayout()
        exit_layout.addStretch()
        
        exit_btn = QPushButton(format_arabic_text("❌ خروج"))
        exit_btn.setFont(QFont("Tahoma", 10))
        exit_btn.setFixedSize(100, 35)
        exit_btn.clicked.connect(self.close)
        exit_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        exit_layout.addWidget(exit_btn)
        layout.addLayout(exit_layout)
    
    def launch_main_window(self):
        """تشغيل النافذة الرئيسية"""
        try:
            from database_admin_window import DatabaseAdminWindow

            self.hide()
            self.main_window = DatabaseAdminWindow()
            self.main_window.show()

            # العودة للمشغل عند إغلاق النافذة الرئيسية
            self.main_window.destroyed.connect(self.show)

        except Exception as e:
            self.show()  # إظهار المشغل مرة أخرى في حالة الخطأ
            QMessageBox.critical(self, "خطأ", format_arabic_text(f"فشل في تشغيل النافذة الرئيسية: {str(e)}"))
    
    def launch_sql_editor(self):
        """تشغيل محرر SQL"""
        try:
            from advanced_sql_editor import AdvancedSQLEditor
            from multi_database_manager import MultiDatabaseManager
            
            db_manager = MultiDatabaseManager()
            
            self.hide()
            self.sql_editor = AdvancedSQLEditor(db_manager)
            self.sql_editor.load_connections()
            self.sql_editor.resize(1000, 700)
            self.sql_editor.show()
            
            # العودة للمشغل عند الإغلاق
            self.sql_editor.destroyed.connect(self.show)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", format_arabic_text(f"فشل في تشغيل محرر SQL: {str(e)}"))
    
    def launch_connection_manager(self):
        """تشغيل مدير الاتصالات"""
        try:
            from database_connection_dialog import DatabaseConnectionDialog
            from multi_database_manager import MultiDatabaseManager
            
            db_manager = MultiDatabaseManager()
            dialog = DatabaseConnectionDialog(self, manager=db_manager)
            
            if dialog.exec() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", format_arabic_text("تم حفظ الاتصال بنجاح"))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", format_arabic_text(f"فشل في تشغيل مدير الاتصالات: {str(e)}"))
    
    def run_system_test(self):
        """تشغيل اختبار النظام"""
        try:
            # تشغيل اختبار سريع
            from multi_database_manager import MultiDatabaseManager
            
            db_manager = MultiDatabaseManager()
            connections = db_manager.get_connections_list()
            
            if connections:
                connection_name = connections[0]['name']
                success, message = db_manager.test_connection(connection_name)
                
                if success:
                    QMessageBox.information(
                        self, "نتيجة الاختبار", 
                        format_arabic_text(f"✅ نجح الاختبار\n\n{message}\n\nعدد الاتصالات: {len(connections)}")
                    )
                else:
                    QMessageBox.warning(
                        self, "نتيجة الاختبار", 
                        format_arabic_text(f"❌ فشل الاختبار\n\n{message}")
                    )
            else:
                QMessageBox.information(
                    self, "نتيجة الاختبار", 
                    format_arabic_text("⚠️ لا توجد اتصالات محفوظة\n\nيرجى إضافة اتصال أولاً")
                )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", format_arabic_text(f"فشل في تشغيل الاختبار: {str(e)}"))

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق للنصوص العربية
    setup_arabic_application(app)
    
    # إنشاء المشغل
    launcher = DatabaseAdminLauncher()
    launcher.show()
    
    print("🚀 تم تشغيل مشغل نظام إدارة قاعدة البيانات")
    print("📱 النافذة مفتوحة - اختر الوظيفة المطلوبة")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
