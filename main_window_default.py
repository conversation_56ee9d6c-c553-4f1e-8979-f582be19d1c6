#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - النافذة الرئيسية (الوضع الافتراضي بدون قاعدة البيانات)
"""

import sys
import os
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTreeWidget, QTreeWidgetItem, QLabel, QPushButton, QFrame,
    QSplitter, QScrollArea, QMessageBox, QStatusBar, QMenuBar,
    QMenu, QToolBar, QTextEdit, QGroupBox, QGridLayout
)
from PySide6.QtCore import Qt, QTimer, QSize, Signal
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QPen, QAction

# استيراد مساعد النصوص العربية
try:
    from arabic_text_helper import format_arabic_text, setup_arabic_widget
except ImportError:
    def format_arabic_text(text):
        return text
    def setup_arabic_widget(widget):
        pass

class SHPERPMainWindow(QMainWindow):
    """النافذة الرئيسية لنظام SHP ERP - الوضع الافتراضي"""
    
    def __init__(self):
        super().__init__()
        
        # إعداد النافذة الرئيسية
        self.setWindowTitle("🚢 SHP ERP - نظام تخطيط موارد المؤسسات")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        # متغيرات النظام
        self.systems_data = []
        self.systems_tree_data = {}
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # تحميل البيانات الافتراضية
        self.load_default_systems_data()
        
        # تطبيق الأنماط
        self.apply_styles()
        
        print("✅ تم تشغيل النافذة الرئيسية (الوضع الافتراضي)")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # الشريط الجانبي للأنظمة
        self.systems_panel = self.create_systems_panel()
        splitter.addWidget(self.systems_panel)
        
        # منطقة المحتوى الرئيسية
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # تعيين نسب المقسم
        splitter.setSizes([400, 1000])  # 30% للأنظمة، 70% للمحتوى
    
    def create_systems_panel(self):
        """إنشاء لوحة الأنظمة الجانبية"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMaximumWidth(450)
        panel.setMinimumWidth(350)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # عنوان اللوحة
        title_label = QLabel("🏗️ أنظمة SHP ERP")
        title_label.setFont(QFont("Tahoma", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # شجرة الأنظمة
        self.systems_tree = QTreeWidget()
        self.systems_tree.setHeaderHidden(True)
        self.systems_tree.setRootIsDecorated(True)
        self.systems_tree.setAlternatingRowColors(True)
        self.systems_tree.itemClicked.connect(self.on_system_clicked)
        self.systems_tree.itemDoubleClicked.connect(self.on_system_double_clicked)
        
        layout.addWidget(self.systems_tree)
        
        # معلومات سريعة
        info_frame = self.create_info_panel()
        layout.addWidget(info_frame)
        
        return panel
    
    def create_info_panel(self):
        """إنشاء لوحة المعلومات السريعة"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMaximumHeight(150)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان
        title = QLabel("📊 معلومات النظام")
        title.setFont(QFont("Tahoma", 10, QFont.Bold))
        layout.addWidget(title)
        
        # معلومات النظام
        self.info_text = QLabel()
        self.info_text.setWordWrap(True)
        self.update_info_text()
        layout.addWidget(self.info_text)
        
        return frame
    
    def create_content_area(self):
        """إنشاء منطقة المحتوى الرئيسية"""
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(content_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان المحتوى
        self.content_title = QLabel("🚢 مرحباً بك في نظام SHP ERP")
        self.content_title.setFont(QFont("Tahoma", 18, QFont.Bold))
        self.content_title.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.content_title)
        
        # منطقة التمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # ويدجت المحتوى
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)
        
        # إضافة محتوى ترحيبي افتراضي
        self.show_welcome_content()
        
        scroll_area.setWidget(self.content_widget)
        layout.addWidget(scroll_area)
        
        return content_frame
    
    def load_default_systems_data(self):
        """تحميل البيانات الافتراضية للأنظمة"""
        self.systems_data = [
            {
                'SYSTEM_NO': 1,
                'SYSTEM_CODE': 'ERP',
                'SYSTEM_NAME': 'نظام ERP الرئيسي',
                'PARENT_NO': 0,
                'ORDER_NO': 1,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 10,
                'SYSTEM_CODE': 'STP',
                'SYSTEM_NAME': 'نظام الإعدادات العامة',
                'PARENT_NO': 1,
                'ORDER_NO': 10,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 20,
                'SYSTEM_CODE': 'USR',
                'SYSTEM_NAME': 'نظام إدارة المستخدمين',
                'PARENT_NO': 1,
                'ORDER_NO': 20,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 30,
                'SYSTEM_CODE': 'ACC',
                'SYSTEM_NAME': 'أنظمة الحسابات',
                'PARENT_NO': 1,
                'ORDER_NO': 30,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 31,
                'SYSTEM_CODE': 'GL',
                'SYSTEM_NAME': 'نظام الأستاذ العام',
                'PARENT_NO': 30,
                'ORDER_NO': 31,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 32,
                'SYSTEM_CODE': 'AP',
                'SYSTEM_NAME': 'نظام الحسابات الدائنة',
                'PARENT_NO': 30,
                'ORDER_NO': 32,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 33,
                'SYSTEM_CODE': 'AR',
                'SYSTEM_NAME': 'نظام الحسابات المدينة',
                'PARENT_NO': 30,
                'ORDER_NO': 33,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 40,
                'SYSTEM_CODE': 'INV',
                'SYSTEM_NAME': 'أنظمة المخازن',
                'PARENT_NO': 1,
                'ORDER_NO': 40,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 41,
                'SYSTEM_CODE': 'WM',
                'SYSTEM_NAME': 'نظام إدارة المخزون',
                'PARENT_NO': 40,
                'ORDER_NO': 41,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 50,
                'SYSTEM_CODE': 'PUR',
                'SYSTEM_NAME': 'أنظمة المشتريات',
                'PARENT_NO': 1,
                'ORDER_NO': 50,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 51,
                'SYSTEM_CODE': 'PO',
                'SYSTEM_NAME': 'نظام أوامر الشراء',
                'PARENT_NO': 50,
                'ORDER_NO': 51,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 60,
                'SYSTEM_CODE': 'SAL',
                'SYSTEM_NAME': 'أنظمة المبيعات',
                'PARENT_NO': 1,
                'ORDER_NO': 60,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 61,
                'SYSTEM_CODE': 'SO',
                'SYSTEM_NAME': 'نظام أوامر البيع',
                'PARENT_NO': 60,
                'ORDER_NO': 61,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 70,
                'SYSTEM_CODE': 'HR',
                'SYSTEM_NAME': 'أنظمة الموارد البشرية',
                'PARENT_NO': 1,
                'ORDER_NO': 70,
                'INACTIVE': 0
            },
            {
                'SYSTEM_NO': 80,
                'SYSTEM_CODE': 'RPT',
                'SYSTEM_NAME': 'أنظمة التقارير',
                'PARENT_NO': 1,
                'ORDER_NO': 80,
                'INACTIVE': 0
            }
        ]
        
        # بناء شجرة الأنظمة
        self.build_systems_tree()
        
        # تحديث شجرة القوائم
        self.update_systems_tree()
        
        print(f"✅ تم تحميل {len(self.systems_data)} نظام افتراضي")
    
    def build_systems_tree(self):
        """بناء شجرة الأنظمة من البيانات"""
        self.systems_tree_data = {}
        
        # تنظيم البيانات حسب المستوى
        for system in self.systems_data:
            parent_no = system.get('PARENT_NO', 0)
            if parent_no not in self.systems_tree_data:
                self.systems_tree_data[parent_no] = []
            self.systems_tree_data[parent_no].append(system)
        
        # ترتيب كل مستوى حسب ORDER_NO
        for parent_no in self.systems_tree_data:
            self.systems_tree_data[parent_no].sort(key=lambda x: x.get('ORDER_NO', 0))
    
    def update_systems_tree(self):
        """تحديث شجرة الأنظمة في الواجهة"""
        self.systems_tree.clear()
        
        # إضافة الأنظمة الجذر (PARENT_NO = 0)
        if 0 in self.systems_tree_data:
            for system in self.systems_tree_data[0]:
                self.add_system_to_tree(system, None)
        
        # توسيع العقد الرئيسية
        self.systems_tree.expandAll()
        
        # تحديث معلومات النظام
        self.update_info_text()
    
    def add_system_to_tree(self, system, parent_item):
        """إضافة نظام إلى الشجرة"""
        system_name = system.get('SYSTEM_NAME', 'نظام غير معروف')
        system_code = system.get('SYSTEM_CODE', '')
        
        # تنسيق النص
        display_text = f"{system_name}"
        if system_code:
            display_text += f" ({system_code})"
        
        # إنشاء العنصر
        if parent_item:
            item = QTreeWidgetItem(parent_item, [display_text])
        else:
            item = QTreeWidgetItem(self.systems_tree, [display_text])
        
        # حفظ بيانات النظام
        item.setData(0, Qt.UserRole, system)
        
        # إضافة الأنظمة الفرعية
        system_no = system.get('SYSTEM_NO')
        if system_no in self.systems_tree_data:
            for child_system in self.systems_tree_data[system_no]:
                self.add_system_to_tree(child_system, item)
        
        return item

    def show_welcome_content(self):
        """عرض المحتوى الترحيبي"""
        # مسح المحتوى الحالي
        self.clear_content_layout()

        # بطاقة ترحيبية
        welcome_card = QFrame()
        welcome_card.setFrameStyle(QFrame.StyledPanel)
        welcome_card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                color: white;
                padding: 30px;
                margin: 20px;
            }
        """)

        welcome_layout = QVBoxLayout(welcome_card)
        welcome_layout.setContentsMargins(30, 30, 30, 30)
        welcome_layout.setSpacing(15)

        # العنوان الرئيسي
        title = QLabel("🚢 مرحباً بك في نظام SHP ERP")
        title.setFont(QFont("Tahoma", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white;")
        welcome_layout.addWidget(title)

        # الوصف
        description = QLabel(
            "نظام تخطيط موارد المؤسسات الشامل\n"
            "يمكنك من هنا الوصول إلى جميع أنظمة الشركة وإدارة العمليات بكفاءة.\n\n"
            "استخدم الشريط الجانبي للتنقل بين الأنظمة المختلفة."
        )
        description.setFont(QFont("Tahoma", 12))
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        description.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        welcome_layout.addWidget(description)

        self.content_layout.addWidget(welcome_card)

        # إحصائيات سريعة
        stats_frame = self.create_stats_frame()
        self.content_layout.addWidget(stats_frame)

        # مساحة مرنة
        self.content_layout.addStretch()

    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)

        layout = QGridLayout(frame)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان الإحصائيات
        stats_title = QLabel("📊 إحصائيات النظام")
        stats_title.setFont(QFont("Tahoma", 14, QFont.Bold))
        stats_title.setAlignment(Qt.AlignCenter)
        layout.addWidget(stats_title, 0, 0, 1, 3)

        # بطاقات الإحصائيات
        stats_data = [
            ("🏗️", "إجمالي الأنظمة", str(len(self.systems_data)), "#FF6B6B"),
            ("📋", "الأنظمة النشطة", str(len([s for s in self.systems_data if s.get('INACTIVE', 0) == 0])), "#4ECDC4"),
            ("🔧", "الوضع الحالي", "افتراضي", "#45B7D1")
        ]

        for i, (icon, title, value, color) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, color)
            layout.addWidget(card, 1, i)

        return frame

    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 10px;
                color: white;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(5)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Tahoma", 24))
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Tahoma", 16, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Tahoma", 10))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: rgba(255, 255, 255, 0.8);")
        layout.addWidget(title_label)

        return card

    def clear_content_layout(self):
        """مسح محتوى التخطيط"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def update_info_text(self):
        """تحديث نص المعلومات"""
        total_systems = len(self.systems_data)
        active_systems = len([s for s in self.systems_data if s.get('INACTIVE', 0) == 0])

        info_text = f"""
🔢 إجمالي الأنظمة: {total_systems}
✅ الأنظمة النشطة: {active_systems}
🔧 الوضع: افتراضي (بدون قاعدة بيانات)
👤 المستخدم: مدير النظام
        """.strip()

        self.info_text.setText(info_text)

    def on_system_clicked(self, item, column):
        """معالج النقر على نظام"""
        system_data = item.data(0, Qt.UserRole)
        if system_data:
            system_name = system_data.get('SYSTEM_NAME', 'نظام غير معروف')
            system_code = system_data.get('SYSTEM_CODE', '')

            self.content_title.setText(f"📋 {system_name}")

            # مسح المحتوى الحالي
            self.clear_content_layout()

            # عرض معلومات النظام
            self.show_system_info(system_data)

    def on_system_double_clicked(self, item, column):
        """معالج النقر المزدوج على نظام"""
        system_data = item.data(0, Qt.UserRole)
        if system_data:
            system_code = system_data.get('SYSTEM_CODE', '')
            system_name = system_data.get('SYSTEM_NAME', 'نظام غير معروف')

            # معالجة خاصة للنظام رقم 10 (STP)
            if system_code == 'STP':
                self.launch_advanced_settings_system(system_data)
                return

            # رسالة افتراضية للأنظمة الأخرى
            QMessageBox.information(
                self,
                "معلومات النظام",
                f"تم النقر المزدوج على:\n{system_name} ({system_code})\n\nهذا النظام في الوضع الافتراضي."
            )

    def show_system_info(self, system_data):
        """عرض معلومات النظام المحدد"""
        # إطار معلومات النظام
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.StyledPanel)

        layout = QVBoxLayout(info_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النظام
        system_name = system_data.get('SYSTEM_NAME', 'نظام غير معروف')
        system_code = system_data.get('SYSTEM_CODE', '')

        title = QLabel(f"🔧 {system_name}")
        title.setFont(QFont("Tahoma", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تفاصيل النظام
        details_group = QGroupBox("📋 تفاصيل النظام")
        details_layout = QGridLayout(details_group)

        details_data = [
            ("رقم النظام:", str(system_data.get('SYSTEM_NO', 'غير محدد'))),
            ("رمز النظام:", system_code or 'غير محدد'),
            ("اسم النظام:", system_name),
            ("النظام الأب:", str(system_data.get('PARENT_NO', 'غير محدد'))),
            ("ترتيب العرض:", str(system_data.get('ORDER_NO', 'غير محدد'))),
            ("الحالة:", "نشط" if system_data.get('INACTIVE', 0) == 0 else "غير نشط")
        ]

        for i, (label, value) in enumerate(details_data):
            details_layout.addWidget(QLabel(label), i, 0)
            value_label = QLabel(value)
            value_label.setStyleSheet("font-weight: bold; color: #007bff;")
            details_layout.addWidget(value_label, i, 1)

        layout.addWidget(details_group)

        # أزرار الإجراءات
        if system_code == 'STP':
            actions_group = QGroupBox("⚡ إجراءات سريعة")
            actions_layout = QVBoxLayout(actions_group)

            launch_btn = QPushButton("🚀 تشغيل نظام الإعدادات الشامل والمتقدم")
            launch_btn.clicked.connect(lambda: self.launch_advanced_settings_system(system_data))
            actions_layout.addWidget(launch_btn)

            layout.addWidget(actions_group)

        self.content_layout.addWidget(info_frame)

        # مساحة مرنة
        self.content_layout.addStretch()

    def launch_advanced_settings_system(self, system_data=None):
        """تشغيل نظام الإعدادات الشامل والمتقدم (STP)"""
        try:
            # عرض رسالة تأكيد
            reply = QMessageBox.question(
                self,
                "تأكيد",
                "هل تريد فتح نظام الإعدادات الشامل والمتقدم؟\n\nسيتم فتح النظام في وضع ملء الشاشة",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            from simple_advanced_settings import SimpleAdvancedSettingsSystem
            from PySide6.QtCore import Qt

            # إنشاء نافذة جديدة في كل مرة لضمان الظهور
            self.advanced_settings_window = SimpleAdvancedSettingsSystem()

            # تعيين النافذة لتكون في المقدمة
            self.advanced_settings_window.setWindowFlags(
                self.advanced_settings_window.windowFlags() | Qt.WindowStaysOnTopHint
            )

            # تعيين عنوان واضح
            self.advanced_settings_window.setWindowTitle("🚢 SHP ERP - نظام الإعدادات الشامل والمتقدم (STP)")

            # عرض النافذة في وضع ملء الشاشة
            self.advanced_settings_window.showMaximized()
            self.advanced_settings_window.raise_()
            self.advanced_settings_window.activateWindow()

            # إزالة خاصية البقاء في المقدمة بعد ثانية واحدة
            from PySide6.QtCore import QTimer
            def remove_on_top():
                if hasattr(self, 'advanced_settings_window') and self.advanced_settings_window:
                    self.advanced_settings_window.setWindowFlags(
                        self.advanced_settings_window.windowFlags() & ~Qt.WindowStaysOnTopHint
                    )
                    self.advanced_settings_window.show()

            QTimer.singleShot(1000, remove_on_top)

            print("✅ تم فتح نظام الإعدادات الشامل والمتقدم")

            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                "نجح",
                "تم فتح نظام الإعدادات الشامل والمتقدم بنجاح!\n\nالنظام يعمل الآن في وضع ملء الشاشة مع جميع الميزات المتقدمة."
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نظام الإعدادات الشامل والمتقدم: {str(e)}")
            print(f"❌ خطأ في فتح نظام الإعدادات الشامل والمتقدم: {e}")

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة النظام
        system_menu = menubar.addMenu("🏗️ النظام")

        # إعادة تحميل
        reload_action = QAction("🔄 إعادة تحميل", self)
        reload_action.triggered.connect(self.reload_systems)
        system_menu.addAction(reload_action)

        system_menu.addSeparator()

        # خروج
        exit_action = QAction("🚪 خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        system_menu.addAction(exit_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("❓ مساعدة")

        # حول البرنامج
        about_action = QAction("ℹ️ حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات")

        # إعادة تحميل
        reload_action = QAction("🔄", self)
        reload_action.setToolTip("إعادة تحميل الأنظمة")
        reload_action.triggered.connect(self.reload_systems)
        toolbar.addAction(reload_action)

        toolbar.addSeparator()

        # معلومات
        info_action = QAction("ℹ️", self)
        info_action.setToolTip("معلومات النظام")
        info_action.triggered.connect(self.show_system_info_dialog)
        toolbar.addAction(info_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()

        # رسالة الحالة
        self.status_label = QLabel("جاهز - الوضع الافتراضي")
        status_bar.addWidget(self.status_label)

        # معلومات الأنظمة
        systems_label = QLabel(f"📋 {len(self.systems_data)} نظام")
        status_bar.addPermanentWidget(systems_label)

        # الوقت
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)

        # تحديث الوقت كل ثانية
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

    def update_time(self):
        """تحديث عرض الوقت"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"🕒 {current_time}")

    def reload_systems(self):
        """إعادة تحميل الأنظمة"""
        self.load_default_systems_data()
        self.show_welcome_content()
        self.status_label.setText("تم إعادة تحميل الأنظمة")

        QMessageBox.information(self, "إعادة التحميل", "تم إعادة تحميل جميع الأنظمة بنجاح!")

    def show_system_info_dialog(self):
        """عرض معلومات النظام في نافذة منفصلة"""
        total_systems = len(self.systems_data)
        active_systems = len([s for s in self.systems_data if s.get('INACTIVE', 0) == 0])

        info_text = f"""
🚢 SHP ERP - نظام تخطيط موارد المؤسسات

📊 إحصائيات النظام:
🔢 إجمالي الأنظمة: {total_systems}
✅ الأنظمة النشطة: {active_systems}
❌ الأنظمة غير النشطة: {total_systems - active_systems}

🔧 الوضع الحالي: افتراضي (بدون قاعدة بيانات)
👤 المستخدم: مدير النظام
📅 التاريخ: {self.get_current_date()}

ℹ️ ملاحظة: النظام يعمل في الوضع الافتراضي مع بيانات تجريبية.
        """

        QMessageBox.information(self, "معلومات النظام", info_text)

    def get_current_date(self):
        """الحصول على التاريخ الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
🚢 SHP ERP - نظام تخطيط موارد المؤسسات

الإصدار: 2.0.0 (الوضع الافتراضي)
تاريخ الإصدار: 2025-07-13

نظام شامل لإدارة موارد المؤسسات يوفر:
• إدارة الحسابات والمالية
• إدارة المخازن والمشتريات
• إدارة المبيعات والعملاء
• إدارة الموارد البشرية
• نظام التقارير الشامل
• نظام الإعدادات المتقدم

الوضع الحالي: افتراضي (بدون قاعدة بيانات)

تم التطوير بواسطة: فريق SHP ERP
        """

        QMessageBox.about(self, "حول البرنامج", about_text)

    def apply_styles(self):
        """تطبيق الأنماط المتقدمة"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }

            QFrame[frameShape="4"] {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                background-color: white;
                margin: 5px;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #6c757d;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }

            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }

            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #004085, stop:1 #002752);
            }

            QTreeWidget {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                selection-color: white;
            }

            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }

            QTreeWidget::item:hover {
                background-color: #e3f2fd;
            }

            QTreeWidget::item:selected {
                background-color: #007bff;
                color: white;
            }

            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background: #6c757d;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background: #495057;
            }
        """)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    # إعداد التطبيق للنصوص العربية
    try:
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
    except ImportError:
        pass

    # إنشاء وعرض النافذة
    window = SHPERPMainWindow()
    window.show()

    print("✅ تم تشغيل SHP ERP في الوضع الافتراضي")

    # تشغيل التطبيق إذا لم يكن يعمل
    if not app.instance():
        sys.exit(app.exec())

if __name__ == "__main__":
    main()
