#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - نظام الإعدادات الشامل والمتقدم (نسخة مبسطة)
"""

import sys
import json
import os
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QScrollArea, QFrame, QLabel, QPushButton, QLineEdit,
    QComboBox, QSpinBox, QCheckBox, QGroupBox, QGridLayout, 
    QFormLayout, QMessageBox, QSplitter, QTreeWidget, QTreeWidgetItem,
    QTableWidget, QTableWidgetItem, QDateEdit, QSlider, QProgressBar, 
    QToolBar, QStatusBar, QMenuBar, QMenu, QFileDialog, QColorDialog,
    QFontDialog
)
from PySide6.Qt<PERSON>ore import Qt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, QSize
from PySide6.QtGui import QFont, QColor, QAction

class SimpleAdvancedSettingsSystem(QMainWindow):
    """نظام الإعدادات الشامل والمتقدم - نسخة مبسطة"""
    
    def __init__(self):
        super().__init__()
        
        # إعداد النافذة الرئيسية
        self.setWindowTitle("🚢 SHP ERP - نظام الإعدادات الشامل والمتقدم")
        self.setWindowState(Qt.WindowMaximized)  # ملء الشاشة
        self.setMinimumSize(1200, 800)
        
        # متغيرات النظام
        self.settings_file = "advanced_settings.json"
        self.current_settings = {}
        self.unsaved_changes = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # تحميل الإعدادات
        self.load_settings()
        
        # تطبيق الأنماط
        self.apply_styles()
        
        print("✅ تم تشغيل نظام الإعدادات الشامل والمتقدم")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # الشريط الجانبي للتنقل
        self.navigation_panel = self.create_navigation_panel()
        splitter.addWidget(self.navigation_panel)
        
        # منطقة المحتوى الرئيسية
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # تعيين نسب المقسم
        splitter.setSizes([300, 900])  # 25% للتنقل، 75% للمحتوى
    
    def create_navigation_panel(self):
        """إنشاء لوحة التنقل الجانبية"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMaximumWidth(350)
        panel.setMinimumWidth(280)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # عنوان اللوحة
        title_label = QLabel("🎛️ لوحة التحكم الشاملة")
        title_label.setFont(QFont("Tahoma", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # شجرة التنقل
        self.navigation_tree = QTreeWidget()
        self.navigation_tree.setHeaderHidden(True)
        self.navigation_tree.setRootIsDecorated(True)
        self.navigation_tree.setAlternatingRowColors(True)
        self.navigation_tree.itemClicked.connect(self.on_navigation_item_clicked)
        
        # إضافة عناصر التنقل
        self.populate_navigation_tree()
        
        layout.addWidget(self.navigation_tree)
        
        return panel
    
    def populate_navigation_tree(self):
        """ملء شجرة التنقل بالعناصر"""
        # 1. إعدادات المظهر
        appearance_item = QTreeWidgetItem(["🎨 إعدادات المظهر الشاملة"])
        appearance_item.setData(0, Qt.UserRole, "appearance")
        
        appearance_sub_items = [
            ("🌈 الألوان والثيمات", "appearance_colors"),
            ("🔤 الخطوط والنصوص", "appearance_fonts"),
        ]
        
        for text, data in appearance_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            appearance_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(appearance_item)
        
        # 2. إعدادات الشركة
        company_item = QTreeWidgetItem(["🏢 إعدادات الشركة الشاملة"])
        company_item.setData(0, Qt.UserRole, "company")
        
        company_sub_items = [
            ("📋 البيانات الأساسية", "company_basic"),
            ("📞 معلومات الاتصال", "company_contact"),
        ]
        
        for text, data in company_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            company_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(company_item)
        
        # 3. إعدادات العملات
        currency_item = QTreeWidgetItem(["💰 إعدادات العملات المتقدمة"])
        currency_item.setData(0, Qt.UserRole, "currency")
        self.navigation_tree.addTopLevelItem(currency_item)
        
        # 4. إعدادات السنة المالية
        fiscal_item = QTreeWidgetItem(["📅 إعدادات السنة المالية"])
        fiscal_item.setData(0, Qt.UserRole, "fiscal")
        self.navigation_tree.addTopLevelItem(fiscal_item)
        
        # توسيع العناصر الرئيسية
        self.navigation_tree.expandAll()
    
    def create_content_area(self):
        """إنشاء منطقة المحتوى الرئيسية"""
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(content_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان المحتوى
        self.content_title = QLabel("🎛️ مرحباً بك في نظام الإعدادات الشامل والمتقدم")
        self.content_title.setFont(QFont("Tahoma", 16, QFont.Bold))
        self.content_title.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.content_title)
        
        # منطقة التمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # ويدجت المحتوى
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)
        
        # إضافة محتوى ترحيبي افتراضي
        self.show_welcome_content()
        
        scroll_area.setWidget(self.content_widget)
        layout.addWidget(scroll_area)
        
        return content_frame
    
    def show_welcome_content(self):
        """عرض المحتوى الترحيبي"""
        # مسح المحتوى الحالي
        self.clear_content_layout()
        
        # بطاقة ترحيبية
        welcome_label = QLabel("""
        🚢 نظام الإعدادات الشامل والمتقدم
        
        مرحباً بك في نظام الإعدادات الأكثر تقدماً وشمولية في SHP ERP.
        يمكنك من هنا إدارة جميع إعدادات النظام بطريقة احترافية ومتقدمة.
        
        استخدم الشريط الجانبي للتنقل بين الأقسام المختلفة.
        """)
        welcome_label.setFont(QFont("Tahoma", 12))
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setWordWrap(True)
        welcome_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                color: white;
                padding: 30px;
                margin: 20px;
            }
        """)
        
        self.content_layout.addWidget(welcome_label)
        
        # مساحة مرنة
        self.content_layout.addStretch()
    
    def clear_content_layout(self):
        """مسح محتوى التخطيط"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def on_navigation_item_clicked(self, item, column):
        """معالج النقر على عنصر التنقل"""
        section = item.data(0, Qt.UserRole)
        if section:
            self.show_section_content(section)
    
    def show_section_content(self, section):
        """عرض محتوى القسم المحدد"""
        # تحديث عنوان المحتوى
        section_titles = {
            "appearance": "🎨 إعدادات المظهر الشاملة",
            "appearance_colors": "🌈 الألوان والثيمات",
            "appearance_fonts": "🔤 الخطوط والنصوص",
            "company": "🏢 إعدادات الشركة الشاملة",
            "company_basic": "📋 البيانات الأساسية",
            "company_contact": "📞 معلومات الاتصال",
            "currency": "💰 إعدادات العملات المتقدمة",
            "fiscal": "📅 إعدادات السنة المالية"
        }
        
        title = section_titles.get(section, "⚙️ إعدادات النظام")
        self.content_title.setText(title)
        
        # مسح المحتوى الحالي
        self.clear_content_layout()
        
        # عرض المحتوى حسب القسم
        if section.startswith("appearance"):
            self.show_appearance_content(section)
        elif section.startswith("company"):
            self.show_company_content(section)
        elif section == "currency":
            self.show_currency_content()
        elif section == "fiscal":
            self.show_fiscal_content()
        else:
            self.show_welcome_content()
    
    def show_appearance_content(self, section):
        """عرض محتوى إعدادات المظهر"""
        # إعدادات الألوان والثيمات
        colors_group = QGroupBox("🌈 إعدادات الألوان والثيمات")
        colors_layout = QGridLayout(colors_group)
        
        # اختيار الثيم
        colors_layout.addWidget(QLabel("🎨 الثيم الحالي:"), 0, 0)
        theme_combo = QComboBox()
        theme_combo.addItems(["فاتح", "داكن", "تلقائي", "أزرق", "أخضر", "بنفسجي"])
        colors_layout.addWidget(theme_combo, 0, 1)
        
        # اللون الأساسي
        colors_layout.addWidget(QLabel("🎯 اللون الأساسي:"), 1, 0)
        primary_color_btn = QPushButton("اختيار اللون")
        primary_color_btn.clicked.connect(lambda: self.choose_color("primary"))
        colors_layout.addWidget(primary_color_btn, 1, 1)
        
        self.content_layout.addWidget(colors_group)
        
        # مساحة مرنة
        self.content_layout.addStretch()
    
    def show_company_content(self, section):
        """عرض محتوى إعدادات الشركة"""
        # البيانات الأساسية
        basic_group = QGroupBox("📋 البيانات الأساسية للشركة")
        basic_layout = QFormLayout(basic_group)
        
        # اسم الشركة
        company_name_ar = QLineEdit()
        company_name_ar.setPlaceholderText("اسم الشركة بالعربية")
        basic_layout.addRow("🏢 اسم الشركة (عربي):", company_name_ar)
        
        company_name_en = QLineEdit()
        company_name_en.setPlaceholderText("Company Name in English")
        basic_layout.addRow("🏢 اسم الشركة (إنجليزي):", company_name_en)
        
        self.content_layout.addWidget(basic_group)
        
        # مساحة مرنة
        self.content_layout.addStretch()
    
    def show_currency_content(self):
        """عرض محتوى إعدادات العملات"""
        currency_group = QGroupBox("💱 إدارة العملات")
        currency_layout = QVBoxLayout(currency_group)
        
        # العملة الأساسية
        base_currency_layout = QHBoxLayout()
        base_currency_layout.addWidget(QLabel("💰 العملة الأساسية:"))
        
        base_currency_combo = QComboBox()
        base_currency_combo.addItems([
            "ريال سعودي (SAR)", "دولار أمريكي (USD)", "يورو (EUR)"
        ])
        base_currency_layout.addWidget(base_currency_combo)
        base_currency_layout.addStretch()
        
        currency_layout.addLayout(base_currency_layout)
        
        self.content_layout.addWidget(currency_group)
        
        # مساحة مرنة
        self.content_layout.addStretch()
    
    def show_fiscal_content(self):
        """عرض محتوى إعدادات السنة المالية"""
        fiscal_group = QGroupBox("📆 السنة المالية الحالية")
        fiscal_layout = QFormLayout(fiscal_group)
        
        # بداية السنة المالية
        fiscal_start = QDateEdit()
        fiscal_start.setDate(QDate(2025, 1, 1))
        fiscal_start.setCalendarPopup(True)
        fiscal_layout.addRow("📅 بداية السنة المالية:", fiscal_start)
        
        # نهاية السنة المالية
        fiscal_end = QDateEdit()
        fiscal_end.setDate(QDate(2025, 12, 31))
        fiscal_end.setCalendarPopup(True)
        fiscal_layout.addRow("📅 نهاية السنة المالية:", fiscal_end)
        
        self.content_layout.addWidget(fiscal_group)
        
        # مساحة مرنة
        self.content_layout.addStretch()
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("📁 ملف")
        
        # حفظ الإعدادات
        save_action = QAction("💾 حفظ الإعدادات", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        file_menu.addAction(save_action)
        
        # خروج
        exit_action = QAction("🚪 خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات")
        
        # حفظ سريع
        save_action = QAction("💾", self)
        save_action.setToolTip("حفظ سريع (Ctrl+S)")
        save_action.triggered.connect(self.save_settings)
        toolbar.addAction(save_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        
        # رسالة الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)
        
        # الوقت
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)
        
        # تحديث الوقت كل ثانية
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
    
    def update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"🕒 {current_time}")
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            
            QFrame[frameShape="4"] {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                background-color: white;
                margin: 5px;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #6c757d;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }
        """)
    
    def choose_color(self, color_type):
        """اختيار لون"""
        color = QColorDialog.getColor(Qt.blue, self, f"اختيار {color_type}")
        if color.isValid():
            self.current_settings[f"{color_type}_color"] = color.name()
            self.status_label.setText(f"تم تغيير لون {color_type}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, ensure_ascii=False, indent=2)
            
            self.status_label.setText("✅ تم حفظ الإعدادات بنجاح")
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ جميع الإعدادات بنجاح!")
            
        except Exception as e:
            self.status_label.setText(f"❌ فشل في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الإعدادات:\n{str(e)}")
    
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.current_settings = json.load(f)
                self.status_label.setText("✅ تم تحميل الإعدادات")
            else:
                self.current_settings = {
                    "theme": "فاتح",
                    "primary_color": "#007bff",
                    "company_name_ar": "",
                    "company_name_en": "",
                    "base_currency": "ريال سعودي (SAR)"
                }
                self.status_label.setText("تم تحميل الإعدادات الافتراضية")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في تحميل الإعدادات: {str(e)}")
            self.current_settings = {}

def main():
    """الدالة الرئيسية"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # إنشاء وعرض النافذة
    window = SimpleAdvancedSettingsSystem()
    window.show()
    
    print("✅ تم تشغيل نظام الإعدادات الشامل والمتقدم (النسخة المبسطة)")
    
    # تشغيل التطبيق إذا لم يكن يعمل
    if not app.instance():
        sys.exit(app.exec())

if __name__ == "__main__":
    main()
