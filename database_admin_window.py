#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Database Administration Window
نافذة إدارة قاعدة البيانات
"""

import sys
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QTableWidget, QTableWidgetItem, QTextEdit, QPushButton,
    QLabel, QLineEdit, QComboBox, QSpinBox, QCheckBox, QGroupBox,
    QSplitter, QTreeWidget, QTreeWidgetItem, QMessageBox, QProgressBar,
    QStatusBar, QToolBar, QFileDialog, QInputDialog, QFrame
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QAction, QIcon, QPixmap, QColor

from arabic_text_helper import format_arabic_text, setup_arabic_widget
from multi_database_manager import MultiDatabaseManager, DatabaseConnection, DatabaseType

class DatabaseQueryThread(QThread):
    """خيط تنفيذ الاستعلامات"""
    query_finished = Signal(bool, object)  # success, result
    progress_updated = Signal(int)
    
    def __init__(self, manager, connection_name, query):
        super().__init__()
        self.manager = manager
        self.connection_name = connection_name
        self.query = query
    
    def run(self):
        """تنفيذ الاستعلام"""
        try:
            # محاكاة تقدم العملية
            for i in range(0, 101, 10):
                self.progress_updated.emit(i)
                self.msleep(100)
            
            # هنا سيتم تنفيذ الاستعلام الفعلي
            result = {"message": "تم تنفيذ الاستعلام بنجاح", "rows": 0}
            self.query_finished.emit(True, result)
            
        except Exception as e:
            self.query_finished.emit(False, str(e))

class DatabaseAdminWindow(QMainWindow):
    """نافذة إدارة قاعدة البيانات الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = MultiDatabaseManager()
        self.current_connection = None
        self.query_thread = None
        
        self.init_ui()
        self.setup_connections()
        self.load_connections_list()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(format_arabic_text("SHP ERP - إدارة قاعدة البيانات Oracle"))
        self.setGeometry(100, 100, 1400, 900)
        
        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage(format_arabic_text("جاهز"))
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        
        # الشريط الجانبي الأيسر (قائمة الاتصالات)
        self.create_connections_sidebar()
        
        # المنطقة المركزية (التبويبات)
        self.create_main_tabs()
        
        # إضافة المكونات للتخطيط
        main_layout.addWidget(self.connections_sidebar, 1)
        main_layout.addWidget(self.main_tabs, 3)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # أزرار شريط الأدوات
        actions = [
            ("🔗", "اتصال جديد", self.new_connection),
            ("🔄", "تحديث", self.refresh_connections),
            ("💾", "حفظ", self.save_query),
            ("📂", "فتح", self.open_query),
            ("▶️", "تنفيذ", self.execute_query),
            ("⏹️", "إيقاف", self.stop_query),
            ("📊", "إحصائيات", self.show_statistics),
            ("🔧", "إعدادات", self.show_settings)
        ]
        
        for icon, tooltip, action in actions:
            btn = QPushButton(icon)
            btn.setToolTip(format_arabic_text(tooltip))
            btn.clicked.connect(action)
            btn.setFixedSize(40, 40)
            toolbar.addWidget(btn)
            
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        toolbar.addWidget(self.progress_bar)
        
    def create_connections_sidebar(self):
        """إنشاء الشريط الجانبي للاتصالات"""
        self.connections_sidebar = QFrame()
        self.connections_sidebar.setFrameStyle(QFrame.StyledPanel)
        self.connections_sidebar.setMaximumWidth(300)
        
        layout = QVBoxLayout(self.connections_sidebar)
        
        # عنوان القسم
        title = QLabel(format_arabic_text("اتصالات قواعد البيانات"))
        title.setFont(QFont("Tahoma", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        self.add_conn_btn = QPushButton("➕")
        self.add_conn_btn.setToolTip(format_arabic_text("إضافة اتصال"))
        self.add_conn_btn.clicked.connect(self.add_connection)
        
        self.edit_conn_btn = QPushButton("✏️")
        self.edit_conn_btn.setToolTip(format_arabic_text("تعديل اتصال"))
        self.edit_conn_btn.clicked.connect(self.edit_connection)
        
        self.delete_conn_btn = QPushButton("🗑️")
        self.delete_conn_btn.setToolTip(format_arabic_text("حذف اتصال"))
        self.delete_conn_btn.clicked.connect(self.delete_connection)
        
        self.test_conn_btn = QPushButton("🔍")
        self.test_conn_btn.setToolTip(format_arabic_text("اختبار اتصال"))
        self.test_conn_btn.clicked.connect(self.test_connection)
        
        for btn in [self.add_conn_btn, self.edit_conn_btn, self.delete_conn_btn, self.test_conn_btn]:
            btn.setFixedSize(35, 35)
            buttons_layout.addWidget(btn)
        
        layout.addLayout(buttons_layout)
        
        # قائمة الاتصالات
        self.connections_tree = QTreeWidget()
        self.connections_tree.setHeaderLabels([
            format_arabic_text("الاسم"),
            format_arabic_text("النوع"),
            format_arabic_text("الحالة")
        ])
        self.connections_tree.itemClicked.connect(self.on_connection_selected)
        layout.addWidget(self.connections_tree)
        
        # معلومات الاتصال المحدد
        self.connection_info = QTextEdit()
        self.connection_info.setMaximumHeight(150)
        self.connection_info.setReadOnly(True)
        layout.addWidget(self.connection_info)
        
    def create_main_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        self.main_tabs = QTabWidget()
        
        # تبويب محرر SQL
        self.create_sql_editor_tab()
        
        # تبويب إدارة الجداول
        self.create_tables_tab()
        
        # تبويب النسخ الاحتياطي
        self.create_backup_tab()
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
        
        # تبويب السجلات
        self.create_logs_tab()
        
    def create_sql_editor_tab(self):
        """إنشاء تبويب محرر SQL"""
        sql_widget = QWidget()
        layout = QVBoxLayout(sql_widget)
        
        # شريط أدوات المحرر
        editor_toolbar = QHBoxLayout()
        
        # أزرار سريعة للاستعلامات الشائعة
        quick_queries = [
            ("SELECT", "SELECT * FROM "),
            ("INSERT", "INSERT INTO "),
            ("UPDATE", "UPDATE "),
            ("DELETE", "DELETE FROM "),
            ("CREATE", "CREATE TABLE "),
            ("DROP", "DROP TABLE ")
        ]
        
        for text, query in quick_queries:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, q=query: self.insert_quick_query(q))
            editor_toolbar.addWidget(btn)
        
        editor_toolbar.addStretch()
        layout.addLayout(editor_toolbar)
        
        # محرر SQL
        self.sql_editor = QTextEdit()
        self.sql_editor.setFont(QFont("Consolas", 11))
        self.sql_editor.setPlaceholderText(format_arabic_text("اكتب استعلام SQL هنا..."))
        layout.addWidget(self.sql_editor)
        
        # منطقة النتائج
        results_splitter = QSplitter(Qt.Vertical)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        results_splitter.addWidget(self.results_table)
        
        # منطقة الرسائل
        self.messages_area = QTextEdit()
        self.messages_area.setMaximumHeight(150)
        self.messages_area.setReadOnly(True)
        results_splitter.addWidget(self.messages_area)
        
        layout.addWidget(results_splitter)
        
        self.main_tabs.addTab(sql_widget, format_arabic_text("محرر SQL"))
        
    def create_tables_tab(self):
        """إنشاء تبويب إدارة الجداول"""
        tables_widget = QWidget()
        layout = QHBoxLayout(tables_widget)
        
        # قائمة الجداول
        tables_splitter = QSplitter(Qt.Horizontal)
        
        # شجرة الجداول
        self.tables_tree = QTreeWidget()
        self.tables_tree.setHeaderLabels([format_arabic_text("الجداول والعروض")])
        self.tables_tree.itemClicked.connect(self.on_table_selected)
        tables_splitter.addWidget(self.tables_tree)
        
        # تفاصيل الجدول
        table_details = QWidget()
        details_layout = QVBoxLayout(table_details)
        
        # معلومات الجدول
        self.table_info = QTextEdit()
        self.table_info.setReadOnly(True)
        details_layout.addWidget(self.table_info)
        
        # أزرار إدارة الجدول
        table_buttons = QHBoxLayout()
        
        self.view_data_btn = QPushButton(format_arabic_text("عرض البيانات"))
        self.view_data_btn.clicked.connect(self.view_table_data)
        
        self.table_structure_btn = QPushButton(format_arabic_text("بنية الجدول"))
        self.table_structure_btn.clicked.connect(self.view_table_structure)
        
        self.export_table_btn = QPushButton(format_arabic_text("تصدير"))
        self.export_table_btn.clicked.connect(self.export_table)
        
        for btn in [self.view_data_btn, self.table_structure_btn, self.export_table_btn]:
            table_buttons.addWidget(btn)
        
        details_layout.addLayout(table_buttons)
        
        tables_splitter.addWidget(table_details)
        layout.addWidget(tables_splitter)
        
        self.main_tabs.addTab(tables_widget, format_arabic_text("إدارة الجداول"))
        
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)
        
        # قسم إنشاء النسخة الاحتياطية
        backup_group = QGroupBox(format_arabic_text("إنشاء نسخة احتياطية"))
        backup_layout = QVBoxLayout(backup_group)
        
        # خيارات النسخ الاحتياطي
        options_layout = QHBoxLayout()
        
        self.backup_type = QComboBox()
        self.backup_type.addItems([
            format_arabic_text("نسخة كاملة"),
            format_arabic_text("البيانات فقط"),
            format_arabic_text("البنية فقط")
        ])
        
        self.backup_tables = QComboBox()
        self.backup_tables.addItems([
            format_arabic_text("جميع الجداول"),
            format_arabic_text("جداول محددة")
        ])
        
        options_layout.addWidget(QLabel(format_arabic_text("نوع النسخة:")))
        options_layout.addWidget(self.backup_type)
        options_layout.addWidget(QLabel(format_arabic_text("الجداول:")))
        options_layout.addWidget(self.backup_tables)
        
        backup_layout.addLayout(options_layout)
        
        # أزرار النسخ الاحتياطي
        backup_buttons = QHBoxLayout()
        
        self.create_backup_btn = QPushButton(format_arabic_text("إنشاء نسخة احتياطية"))
        self.create_backup_btn.clicked.connect(self.create_backup)
        
        self.schedule_backup_btn = QPushButton(format_arabic_text("جدولة النسخ"))
        self.schedule_backup_btn.clicked.connect(self.schedule_backup)
        
        backup_buttons.addWidget(self.create_backup_btn)
        backup_buttons.addWidget(self.schedule_backup_btn)
        backup_layout.addLayout(backup_buttons)
        
        layout.addWidget(backup_group)
        
        # قائمة النسخ الاحتياطية
        backups_group = QGroupBox(format_arabic_text("النسخ الاحتياطية المتاحة"))
        backups_layout = QVBoxLayout(backups_group)
        
        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(5)
        self.backups_table.setHorizontalHeaderLabels([
            format_arabic_text("الاسم"),
            format_arabic_text("التاريخ"),
            format_arabic_text("الحجم"),
            format_arabic_text("النوع"),
            format_arabic_text("الحالة")
        ])
        backups_layout.addWidget(self.backups_table)
        
        # أزرار إدارة النسخ
        backups_buttons = QHBoxLayout()
        
        self.restore_backup_btn = QPushButton(format_arabic_text("استعادة"))
        self.restore_backup_btn.clicked.connect(self.restore_backup)
        
        self.delete_backup_btn = QPushButton(format_arabic_text("حذف"))
        self.delete_backup_btn.clicked.connect(self.delete_backup)
        
        self.verify_backup_btn = QPushButton(format_arabic_text("التحقق"))
        self.verify_backup_btn.clicked.connect(self.verify_backup)
        
        for btn in [self.restore_backup_btn, self.delete_backup_btn, self.verify_backup_btn]:
            backups_buttons.addWidget(btn)
        
        backups_layout.addLayout(backups_buttons)
        layout.addWidget(backups_group)
        
        self.main_tabs.addTab(backup_widget, format_arabic_text("النسخ الاحتياطي"))
        
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)
        
        # إحصائيات عامة
        general_stats = QGroupBox(format_arabic_text("إحصائيات عامة"))
        stats_layout = QVBoxLayout(general_stats)
        
        self.stats_display = QTextEdit()
        self.stats_display.setReadOnly(True)
        stats_layout.addWidget(self.stats_display)
        
        # أزرار الإحصائيات
        stats_buttons = QHBoxLayout()
        
        self.refresh_stats_btn = QPushButton(format_arabic_text("تحديث الإحصائيات"))
        self.refresh_stats_btn.clicked.connect(self.refresh_statistics)
        
        self.export_stats_btn = QPushButton(format_arabic_text("تصدير التقرير"))
        self.export_stats_btn.clicked.connect(self.export_statistics)
        
        stats_buttons.addWidget(self.refresh_stats_btn)
        stats_buttons.addWidget(self.export_stats_btn)
        stats_layout.addLayout(stats_buttons)
        
        layout.addWidget(general_stats)
        
        self.main_tabs.addTab(stats_widget, format_arabic_text("الإحصائيات"))
        
    def create_logs_tab(self):
        """إنشاء تبويب السجلات"""
        logs_widget = QWidget()
        layout = QVBoxLayout(logs_widget)
        
        # فلاتر السجلات
        filters_layout = QHBoxLayout()
        
        self.log_level = QComboBox()
        self.log_level.addItems(["الكل", "معلومات", "تحذير", "خطأ"])
        
        self.log_date = QLineEdit()
        self.log_date.setPlaceholderText("YYYY-MM-DD")
        
        self.filter_logs_btn = QPushButton(format_arabic_text("فلترة"))
        self.filter_logs_btn.clicked.connect(self.filter_logs)
        
        self.clear_logs_btn = QPushButton(format_arabic_text("مسح السجلات"))
        self.clear_logs_btn.clicked.connect(self.clear_logs)
        
        filters_layout.addWidget(QLabel(format_arabic_text("المستوى:")))
        filters_layout.addWidget(self.log_level)
        filters_layout.addWidget(QLabel(format_arabic_text("التاريخ:")))
        filters_layout.addWidget(self.log_date)
        filters_layout.addWidget(self.filter_logs_btn)
        filters_layout.addWidget(self.clear_logs_btn)
        filters_layout.addStretch()
        
        layout.addLayout(filters_layout)
        
        # منطقة عرض السجلات
        self.logs_display = QTextEdit()
        self.logs_display.setReadOnly(True)
        self.logs_display.setFont(QFont("Consolas", 10))
        layout.addWidget(self.logs_display)
        
        self.main_tabs.addTab(logs_widget, format_arabic_text("السجلات"))
        
    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # تحديث دوري للحالة
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # كل 5 ثوان
        
    def load_connections_list(self):
        """تحميل قائمة الاتصالات"""
        self.connections_tree.clear()
        
        connections = self.db_manager.get_connections_list()
        
        for conn in connections:
            item = QTreeWidgetItem([
                conn['name'],
                conn['type'],
                "🟢 متصل" if conn['is_connected'] else "🔴 غير متصل"
            ])
            
            # إضافة معلومات إضافية
            item.setData(0, Qt.UserRole, conn)
            
            self.connections_tree.addTopLevelItem(item)
        
        self.status_bar.showMessage(
            format_arabic_text(f"تم تحميل {len(connections)} اتصال")
        )
    
    # سأكمل باقي الدوال في الملف التالي...
    
    def on_connection_selected(self, item, column):
        """عند اختيار اتصال"""
        conn_data = item.data(0, Qt.UserRole)
        if conn_data:
            self.current_connection = conn_data['name']
            self.update_connection_info(conn_data)
    
    def update_connection_info(self, conn_data):
        """تحديث معلومات الاتصال"""
        info_text = f"""
الاسم: {conn_data['name']}
النوع: {conn_data['type']}
الخادم: {conn_data['host']}:{conn_data['port']}
قاعدة البيانات: {conn_data['database']}
المستخدم: {conn_data['username']}
تاريخ الإنشاء: {conn_data['created_at'][:19] if conn_data['created_at'] else 'غير محدد'}
آخر اتصال: {conn_data['last_connected'][:19] if conn_data['last_connected'] else 'لم يتم الاتصال'}
الحالة: {'متصل' if conn_data['is_connected'] else 'غير متصل'}
        """
        self.connection_info.setText(format_arabic_text(info_text.strip()))
    
    def update_status(self):
        """تحديث شريط الحالة"""
        current_time = datetime.now().strftime("%H:%M:%S")
        if self.current_connection:
            status = f"الاتصال النشط: {self.current_connection} | {current_time}"
        else:
            status = f"لا يوجد اتصال نشط | {current_time}"
        
        self.status_bar.showMessage(format_arabic_text(status))
    
    # دوال الأحداث
    def new_connection(self):
        """إضافة اتصال جديد"""
        from database_connection_dialog import DatabaseConnectionDialog
        dialog = DatabaseConnectionDialog(self, manager=self.db_manager)
        if dialog.exec() == dialog.Accepted:
            self.load_connections_list()

    def refresh_connections(self):
        self.load_connections_list()

    def save_query(self):
        if hasattr(self, 'sql_editor'):
            query = self.sql_editor.toPlainText()
            if query.strip():
                filename, _ = QFileDialog.getSaveFileName(self, "حفظ الاستعلام", "", "SQL Files (*.sql)")
                if filename:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(query)
                    QMessageBox.information(self, "نجح", format_arabic_text("تم حفظ الاستعلام"))

    def open_query(self):
        filename, _ = QFileDialog.getOpenFileName(self, "فتح استعلام", "", "SQL Files (*.sql)")
        if filename and hasattr(self, 'sql_editor'):
            with open(filename, 'r', encoding='utf-8') as f:
                self.sql_editor.setPlainText(f.read())

    def execute_query(self):
        if not self.current_connection:
            QMessageBox.warning(self, "تحذير", format_arabic_text("يرجى اختيار اتصال أولاً"))
            return

        query = self.sql_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", format_arabic_text("يرجى كتابة استعلام أولاً"))
            return

        # بدء تنفيذ الاستعلام
        self.progress_bar.setVisible(True)
        self.messages_area.append(format_arabic_text("🔄 جاري تنفيذ الاستعلام..."))

    def stop_query(self):
        self.progress_bar.setVisible(False)
        self.messages_area.append(format_arabic_text("⏹️ تم إيقاف الاستعلام"))

    def show_statistics(self):
        self.main_tabs.setCurrentIndex(3)  # تبويب الإحصائيات
        self.refresh_statistics()

    def show_settings(self):
        QMessageBox.information(self, "الإعدادات", format_arabic_text("نافذة الإعدادات قيد التطوير"))

    def add_connection(self):
        self.new_connection()

    def edit_connection(self):
        current_item = self.connections_tree.currentItem()
        if current_item:
            conn_data = current_item.data(0, Qt.UserRole)
            if conn_data:
                from database_connection_dialog import DatabaseConnectionDialog
                conn_info = self.db_manager.get_connection_info(conn_data['name'])
                dialog = DatabaseConnectionDialog(self, conn_info, self.db_manager)
                if dialog.exec() == dialog.Accepted:
                    self.load_connections_list()

    def delete_connection(self):
        current_item = self.connections_tree.currentItem()
        if current_item:
            conn_data = current_item.data(0, Qt.UserRole)
            if conn_data:
                reply = QMessageBox.question(
                    self, "تأكيد الحذف",
                    format_arabic_text(f"هل تريد حذف الاتصال '{conn_data['name']}'؟"),
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    if self.db_manager.remove_connection(conn_data['name']):
                        self.load_connections_list()
                        QMessageBox.information(self, "نجح", format_arabic_text("تم حذف الاتصال"))

    def test_connection(self):
        current_item = self.connections_tree.currentItem()
        if current_item:
            conn_data = current_item.data(0, Qt.UserRole)
            if conn_data:
                success, message = self.db_manager.test_connection(conn_data['name'])
                if success:
                    QMessageBox.information(self, "نجح الاختبار", format_arabic_text(message))
                else:
                    QMessageBox.warning(self, "فشل الاختبار", format_arabic_text(message))
                self.load_connections_list()  # تحديث الحالة

    def insert_quick_query(self, query):
        self.sql_editor.insertPlainText(query)

    def on_table_selected(self, item, column):
        table_name = item.text(0)
        self.table_info.setText(format_arabic_text(f"الجدول المحدد: {table_name}"))

    def view_table_data(self):
        QMessageBox.information(self, "عرض البيانات", format_arabic_text("ميزة عرض البيانات قيد التطوير"))

    def view_table_structure(self):
        QMessageBox.information(self, "بنية الجدول", format_arabic_text("ميزة عرض بنية الجدول قيد التطوير"))

    def export_table(self):
        QMessageBox.information(self, "تصدير الجدول", format_arabic_text("ميزة تصدير الجدول قيد التطوير"))

    def create_backup(self):
        QMessageBox.information(self, "النسخ الاحتياطي", format_arabic_text("ميزة النسخ الاحتياطي قيد التطوير"))

    def schedule_backup(self):
        QMessageBox.information(self, "جدولة النسخ", format_arabic_text("ميزة جدولة النسخ قيد التطوير"))

    def restore_backup(self):
        QMessageBox.information(self, "استعادة النسخة", format_arabic_text("ميزة الاستعادة قيد التطوير"))

    def delete_backup(self):
        QMessageBox.information(self, "حذف النسخة", format_arabic_text("ميزة حذف النسخة قيد التطوير"))

    def verify_backup(self):
        QMessageBox.information(self, "التحقق من النسخة", format_arabic_text("ميزة التحقق قيد التطوير"))

    def refresh_statistics(self):
        stats_text = format_arabic_text("""
📊 إحصائيات قاعدة البيانات:

🔗 الاتصالات:
  • إجمالي الاتصالات: 1
  • الاتصالات النشطة: 1
  • آخر اتصال: منذ دقائق

📋 الجداول:
  • إجمالي الجداول: 26
  • الجداول النشطة: 21
  • أكبر جدول: S_ERP_SYSTEM

💾 المساحة:
  • المساحة المستخدمة: 150 MB
  • المساحة المتاحة: 2.5 GB
  • نسبة الاستخدام: 6%

⚡ الأداء:
  • متوسط وقت الاستجابة: 0.15 ثانية
  • عدد الاستعلامات اليوم: 45
  • آخر نسخة احتياطية: لم يتم إنشاؤها
        """)
        self.stats_display.setText(stats_text)

    def export_statistics(self):
        filename, _ = QFileDialog.getSaveFileName(self, "تصدير الإحصائيات", "", "Text Files (*.txt)")
        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.stats_display.toPlainText())
            QMessageBox.information(self, "نجح", format_arabic_text("تم تصدير الإحصائيات"))

    def filter_logs(self):
        self.logs_display.append(format_arabic_text("🔍 تم تطبيق الفلتر"))

    def clear_logs(self):
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            format_arabic_text("هل تريد مسح جميع السجلات؟"),
            QMessageBox.Yes | QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.logs_display.clear()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق للنصوص العربية
    from arabic_text_helper import setup_arabic_application
    setup_arabic_application(app)
    
    # إنشاء النافذة
    window = DatabaseAdminWindow()
    window.show()
    
    print("🚀 تم تشغيل نافذة إدارة قاعدة البيانات")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
