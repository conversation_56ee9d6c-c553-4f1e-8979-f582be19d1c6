# 🚢 SHP ERP - نظام إدارة قاعدة البيانات Oracle المتقدم

## 📖 نظرة عامة

نظام إدارة قاعدة بيانات Oracle شامل ومتقدم مطور خصيصاً لتطبيق SHP ERP مع دعم كامل للغة العربية وإمكانيات ربط قواعد بيانات متعددة.

## ✨ الميزات الرئيسية

### 🔗 إدارة الاتصالات المتعددة
- دعم Oracle, MySQL, PostgreSQL, SQLite
- اختبار الاتصالات المباشر
- حفظ الإعدادات تلقائياً
- إعدادات أمان متقدمة

### 📝 محرر SQL المتقدم
- تلوين صيغة SQL التلقائي
- إكمال تلقائي للكلمات المحجوزة
- تنفيذ الاستعلامات غير المتزامن
- تصدير النتائج بتنسيقات متعددة

### 💾 نظام النسخ الاحتياطي الذكي
- 5 أنواع نسخ مختلفة
- جدولة تلقائية (يومي/أسبوعي/شهري)
- ضغط متقدم (GZIP/ZIP)
- مراقبة التقدم المباشر

### 🌐 واجهة عربية متكاملة
- دعم كامل للغة العربية
- اتجاه من اليمين لليسار
- خطوط محسنة للعربية
- رسائل وتسميات عربية

## 🚀 التشغيل السريع

### الطريقة الموصى بها:
```bash
python run_database_admin.py
```

### التشغيل المباشر:
```bash
# النافذة الرئيسية
python database_admin_window.py

# محرر SQL
python advanced_sql_editor.py

# إدارة الاتصالات
python database_connection_dialog.py
```

## 📋 المتطلبات

### البرمجيات المطلوبة:
- Python 3.8+
- PySide6
- cx_Oracle (للاتصال بـ Oracle)
- arabic-reshaper
- python-bidi

### تثبيت المتطلبات:
```bash
pip install PySide6 cx_Oracle arabic-reshaper python-bidi
```

### قواعد البيانات المدعومة:
- Oracle Database 11g+
- MySQL 5.7+
- PostgreSQL 9.6+
- SQLite 3+

## 🏗️ بنية النظام

```
SHP_ERP/
├── multi_database_manager.py      # مدير قواعد البيانات المتعددة
├── database_connection_dialog.py  # نافذة ربط قواعد البيانات
├── advanced_sql_editor.py         # محرر SQL المتقدم
├── advanced_backup_system.py      # نظام النسخ الاحتياطي
├── database_admin_window.py       # النافذة الرئيسية
├── run_database_admin.py          # مشغل النظام
├── arabic_text_helper.py          # مساعد النصوص العربية
├── test_database_admin_system.py  # اختبار شامل
├── database_connections.json      # ملف الاتصالات
└── backup_config.json            # إعدادات النسخ الاحتياطي
```

## 🔧 الإعداد والتكوين

### 1. إعداد قاعدة البيانات Oracle:
```sql
-- إنشاء مستخدم جديد
CREATE USER ship2025 IDENTIFIED BY ys123;
GRANT CONNECT, RESOURCE TO ship2025;
GRANT CREATE SESSION TO ship2025;
```

### 2. إعداد الاتصال:
- شغل `python run_database_admin.py`
- اختر "🔗 إدارة الاتصالات"
- أدخل بيانات الاتصال:
  - الخادم: localhost
  - المنفذ: 1521
  - الخدمة: orcl
  - المستخدم: ship2025
  - كلمة المرور: ys123

### 3. اختبار النظام:
```bash
python test_database_admin_system.py
```

## 📚 دليل الاستخدام

### إدارة الاتصالات:
1. افتح مشغل النظام
2. اختر "🔗 إدارة الاتصالات"
3. املأ بيانات الاتصال
4. اختبر الاتصال
5. احفظ الإعدادات

### استخدام محرر SQL:
1. اختر "📝 محرر SQL المتقدم"
2. اختر الاتصال من القائمة
3. اكتب الاستعلام
4. اضغط F5 للتنفيذ
5. اعرض النتائج وصدرها

### إنشاء نسخة احتياطية:
1. افتح النافذة الرئيسية
2. انتقل لتبويب "النسخ الاحتياطي"
3. اختر نوع النسخة
4. حدد الجداول
5. ابدأ النسخ الاحتياطي

## 🔍 استكشاف الأخطاء

### مشاكل الاتصال:
```
خطأ: ORA-12541: TNS:no listener
الحل: تأكد من تشغيل Oracle Listener
```

```
خطأ: ORA-01017: invalid username/password
الحل: تحقق من بيانات المستخدم
```

### مشاكل النصوص العربية:
```
خطأ: النصوص معكوسة
الحل: تأكد من تثبيت arabic-reshaper و python-bidi
```

### مشاكل الأداء:
```
خطأ: الاستعلام بطيء
الحل: استخدم LIMIT أو WHERE لتقليل البيانات
```

## 📊 الإحصائيات والمراقبة

### إحصائيات الاتصالات:
- عدد الاتصالات النشطة
- آخر وقت اتصال
- معدل نجاح الاتصالات

### إحصائيات النسخ الاحتياطي:
- عدد النسخ المكتملة
- إجمالي حجم النسخ
- معدل نجاح النسخ

### إحصائيات الاستعلامات:
- عدد الاستعلامات المنفذة
- متوسط وقت التنفيذ
- الاستعلامات الأكثر استخداماً

## 🛡️ الأمان

### حماية كلمات المرور:
- تشفير كلمات المرور في ملف الإعدادات
- دعم SSL/TLS للاتصالات
- انتهاء صلاحية الجلسات

### صلاحيات المستخدمين:
- تحديد صلاحيات قاعدة البيانات
- منع الاستعلامات الخطيرة
- تسجيل جميع العمليات

## 🔄 النسخ الاحتياطي والاستعادة

### أنواع النسخ المدعومة:
- **كاملة**: البنية + البيانات
- **البيانات فقط**: البيانات بدون البنية
- **البنية فقط**: الجداول والفهارس فقط
- **تزايدية**: التغييرات منذ آخر نسخة
- **تفاضلية**: التغييرات منذ آخر نسخة كاملة

### جدولة النسخ:
```python
# نسخة يومية في الساعة 2:00 صباحاً
backup_system.schedule_backup(backup_id, "daily", "02:00")

# نسخة أسبوعية يوم الأحد
backup_system.schedule_backup(backup_id, "weekly", "03:00")

# نسخة شهرية في أول الشهر
backup_system.schedule_backup(backup_id, "monthly", "01:00")
```

## 📈 التطوير والتحسين

### المساهمة في التطوير:
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. اختبار التغييرات
5. إرسال Pull Request

### خطة التطوير المستقبلية:
- [ ] دعم قواعد بيانات إضافية
- [ ] نظام تقارير متقدم
- [ ] واجهة ويب
- [ ] API للتكامل الخارجي
- [ ] نظام إشعارات

## 📞 الدعم والمساعدة

### الحصول على المساعدة:
- راجع هذا الدليل أولاً
- شغل اختبار النظام
- تحقق من ملفات السجلات
- تواصل مع فريق التطوير

### الإبلاغ عن المشاكل:
- وصف المشكلة بالتفصيل
- أرفق رسائل الخطأ
- اذكر إصدار النظام
- قدم خطوات إعادة الإنتاج

## 📄 الترخيص

هذا النظام مطور خصيصاً لتطبيق SHP ERP ومحمي بحقوق الطبع والنشر.

## 🎉 شكر وتقدير

تم تطوير هذا النظام بعناية فائقة لتوفير أفضل تجربة لإدارة قواعد البيانات باللغة العربية.

---

**🚢 SHP ERP - نظام إدارة قاعدة البيانات Oracle المتقدم**  
*نظام شامل ومتطور لإدارة قواعد البيانات مع واجهة عربية احترافية*
