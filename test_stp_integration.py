#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تكامل النظام رقم 10 (STP) مع النافذة الرئيسية
"""

import sys

def test_stp_integration():
    """اختبار تكامل النظام STP"""
    try:
        print("🚢 SHP ERP - اختبار تكامل النظام رقم 10 (STP)")
        print("=" * 70)
        
        # اختبار الاستيراد
        print("🔄 اختبار استيراد المكونات...")
        
        from PySide6.QtWidgets import QApplication
        print("✅ تم استيراد PySide6")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ تم إنشاء التطبيق")
        
        # اختبار استيراد النظام المبسط
        from simple_advanced_settings import SimpleAdvancedSettingsSystem
        print("✅ تم استيراد نظام الإعدادات المبسط")
        
        # اختبار استيراد النافذة الرئيسية
        from main_window import SHPERPMainWindow
        print("✅ تم استيراد النافذة الرئيسية")

        # إنشاء النافذة الرئيسية
        main_window = SHPERPMainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # اختبار دالة تشغيل النظام المتقدم
        if hasattr(main_window, 'launch_advanced_settings_system'):
            print("✅ دالة تشغيل النظام المتقدم موجودة")
        else:
            print("❌ دالة تشغيل النظام المتقدم غير موجودة")
            return False
        
        # إنشاء نظام الإعدادات المتقدم مباشرة
        settings_window = SimpleAdvancedSettingsSystem()
        print("✅ تم إنشاء نظام الإعدادات المتقدم")
        
        # عرض النوافذ
        main_window.show()
        settings_window.show()
        print("✅ تم عرض النوافذ")
        
        print("\n🎉 نجح الاختبار!")
        print("✅ النظام رقم 10 (STP) متكامل مع النافذة الرئيسية")
        print("🖥️ النوافذ مفتوحة الآن")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    success = test_stp_integration()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 تم إصلاح المشكلة!")
        print("✅ النظام رقم 10 (STP) يعمل الآن مع النافذة الرئيسية")
        
        print("\n📋 للاستخدام:")
        print("1️⃣ شغل النافذة الرئيسية: python main_window.py")
        print("2️⃣ ابحث عن: نظام الاعدادات العامة (STP)")
        print("3️⃣ انقر على النظام")
        print("4️⃣ انقر 'نعم' في رسالة التأكيد")
        print("5️⃣ ستفتح النافذة في وضع ملء الشاشة! ✨")
        
        print("\n🔧 أو تشغيل مباشر:")
        print("python simple_advanced_settings.py")
        
        print("\n🎨 الميزات المتاحة:")
        print("• إعدادات المظهر الشاملة")
        print("• إعدادات الشركة الشاملة")
        print("• إعدادات العملات المتقدمة")
        print("• إعدادات السنة المالية")
        print("• واجهة حديثة مع وضع ملء الشاشة")
        print("• حفظ وتحميل الإعدادات")
        
    else:
        print("❌ لا تزال هناك مشاكل تحتاج إلى حل")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
