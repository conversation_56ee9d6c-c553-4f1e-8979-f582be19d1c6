#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Final System Test
الاختبار النهائي للنظام
"""

import sys
import os

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        # اختبار وحدات قاعدة البيانات
        from database_connection import OracleConnection
        print("✅ database_connection")
        
        from erp_systems_manager import ERPSystemsManager
        print("✅ erp_systems_manager")
        
        from systems_tree_widget import SystemsTreeWidget
        print("✅ systems_tree_widget")
        
        from main_window import SHPERPMainWindow
        print("✅ main_window")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_database_basic():
    """اختبار أساسي لقاعدة البيانات"""
    print("\n🔍 اختبار قاعدة البيانات...")
    
    try:
        from database_connection import OracleConnection
        
        db = OracleConnection()
        if db.connect():
            print("✅ الاتصال ناجح")
            
            # اختبار بسيط
            result = db.execute_query("SELECT COUNT(*) as COUNT FROM S_ERP_SYSTEM")
            if result:
                count = result[0]['COUNT']
                print(f"✅ عدد الأنظمة: {count}")
            
            db.disconnect()
            return True
        else:
            print("❌ فشل الاتصال")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_systems_manager_basic():
    """اختبار أساسي لمدير الأنظمة"""
    print("\n🔍 اختبار مدير الأنظمة...")
    
    try:
        from erp_systems_manager import ERPSystemsManager
        
        manager = ERPSystemsManager()
        if manager.connect_to_database():
            print("✅ اتصال مدير الأنظمة ناجح")
            
            systems = manager.load_systems_data()
            print(f"✅ تم تحميل {len(systems)} نظام")
            
            tree = manager.build_systems_tree()
            root_count = len(tree.get('root_systems', []))
            print(f"✅ عدد الأنظمة الجذر: {root_count}")
            
            manager.disconnect()
            return True, systems, tree
        else:
            print("❌ فشل اتصال مدير الأنظمة")
            return False, [], {}
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False, [], {}

def create_summary_report(systems_data):
    """إنشاء تقرير ملخص"""
    print("\n📊 تقرير ملخص النظام:")
    print("=" * 50)
    
    # إحصائيات عامة
    total_systems = len(systems_data)
    root_systems = [s for s in systems_data if not s.get('PARENT_SYSTEM_ID') or s.get('PARENT_SYSTEM_ID') == 0]
    child_systems = [s for s in systems_data if s.get('PARENT_SYSTEM_ID') and s.get('PARENT_SYSTEM_ID') != 0]
    
    print(f"📈 إجمالي الأنظمة: {total_systems}")
    print(f"🌳 الأنظمة الجذر: {len(root_systems)}")
    print(f"🌿 الأنظمة الفرعية: {len(child_systems)}")
    
    # عرض الأنظمة الجذر
    print(f"\n🌳 الأنظمة الجذر:")
    for root in root_systems:
        print(f"  • {root['SYSTEM_ID']}: {root['SYSTEM_NAME']} [{root['SYSTEM_CODE']}]")
    
    # عرض بعض الأنظمة الفرعية
    print(f"\n🌿 عينة من الأنظمة الفرعية:")
    for child in child_systems[:5]:  # أول 5 أنظمة فرعية
        parent_id = child.get('PARENT_SYSTEM_ID')
        print(f"  • {child['SYSTEM_ID']}: {child['SYSTEM_NAME']} (Parent: {parent_id})")
    
    if len(child_systems) > 5:
        print(f"  ... و {len(child_systems) - 5} نظام فرعي آخر")

def main():
    """الدالة الرئيسية للاختبار النهائي"""
    print("🚢 SHP ERP - الاختبار النهائي للنظام")
    print("=" * 60)
    
    # اختبار الاستيراد
    if not test_imports():
        print("\n❌ فشل اختبار الاستيراد - توقف الاختبار")
        return
    
    # اختبار قاعدة البيانات
    if not test_database_basic():
        print("\n❌ فشل اختبار قاعدة البيانات - توقف الاختبار")
        return
    
    # اختبار مدير الأنظمة
    manager_success, systems_data, tree_data = test_systems_manager_basic()
    
    if not manager_success:
        print("\n❌ فشل اختبار مدير الأنظمة - توقف الاختبار")
        return
    
    # إنشاء تقرير ملخص
    create_summary_report(systems_data)
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎉 نجح الاختبار النهائي!")
    print("✅ النظام جاهز للاستخدام")
    print("🗃️ قاعدة البيانات Oracle متصلة ومتاحة")
    print("🌳 شجرة الأنظمة جاهزة للعرض")
    print("=" * 60)
    
    # تعليمات الاستخدام
    print("\n📋 تعليمات الاستخدام:")
    print("1️⃣ لتشغيل النظام الكامل: python run_shp_erp_new.py")
    print("2️⃣ لتشغيل الواجهة الرئيسية: python main_window.py")
    print("3️⃣ لاختبار مكون الشجرة: python test_systems_tree.py")
    print("4️⃣ لاختبار قاعدة البيانات: python test_connection.py")

if __name__ == "__main__":
    main()
