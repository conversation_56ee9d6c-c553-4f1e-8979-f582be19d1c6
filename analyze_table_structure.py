#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Table Structure Analysis
تحليل بنية جدول S_ERP_SYSTEM
"""

from database_connection import get_db_connection

def analyze_table_structure():
    """تحليل بنية جدول S_ERP_SYSTEM"""
    
    db = get_db_connection()
    
    try:
        # الاتصال بقاعدة البيانات
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return
        
        print("🔍 تحليل بنية جدول S_ERP_SYSTEM")
        print("=" * 50)
        
        # الحصول على معلومات الأعمدة
        columns_query = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            DATA_LENGTH,
            NULLABLE,
            DATA_DEFAULT
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'S_ERP_SYSTEM'
        ORDER BY COLUMN_ID
        """
        
        columns = db.execute_query(columns_query)
        
        if columns:
            print("📊 أعمدة الجدول:")
            for col in columns:
                nullable = "اختياري" if col['NULLABLE'] == 'Y' else "مطلوب"
                print(f"  • {col['COLUMN_NAME']} ({col['DATA_TYPE']}) - {nullable}")
        
        # الحصول على تعليقات الأعمدة
        comments_query = """
        SELECT 
            COLUMN_NAME,
            COMMENTS
        FROM USER_COL_COMMENTS 
        WHERE TABLE_NAME = 'S_ERP_SYSTEM'
        AND COMMENTS IS NOT NULL
        ORDER BY COLUMN_NAME
        """
        
        comments = db.execute_query(comments_query)
        
        if comments:
            print("\n📝 تعليقات الأعمدة:")
            for comment in comments:
                print(f"  • {comment['COLUMN_NAME']}: {comment['COMMENTS']}")
        
        # عرض عينة من البيانات
        sample_query = """
        SELECT * FROM (
            SELECT 
                SYS_NO,
                SYS_CODE,
                SYS_NAME,
                SYS_PARNT,
                ORDR_NO,
                INACTIVE,
                FORM_NO
            FROM S_ERP_SYSTEM
            ORDER BY SYS_NO
        ) WHERE ROWNUM <= 10
        """
        
        sample_data = db.execute_query(sample_query)
        
        if sample_data:
            print(f"\n📋 عينة من البيانات ({len(sample_data)} سجل):")
            for row in sample_data:
                sys_no = row.get('SYS_NO', '')
                sys_code = row.get('SYS_CODE', '')
                sys_name = row.get('SYS_NAME', '')
                sys_parent = row.get('SYS_PARNT', '')
                order_no = row.get('ORDR_NO', '')
                inactive = row.get('INACTIVE', '')
                form_no = row.get('FORM_NO', '')
                
                parent_text = f" (Parent: {sys_parent})" if sys_parent else " (Root)"
                status = "غير نشط" if inactive == 1 else "نشط"
                
                print(f"  • {sys_no}: {sys_name} [{sys_code}]{parent_text} - {status}")
        
        # تحليل الهيكل الهرمي
        hierarchy_query = """
        SELECT 
            COUNT(*) as TOTAL_SYSTEMS,
            COUNT(CASE WHEN SYS_PARNT IS NULL OR SYS_PARNT = 0 THEN 1 END) as ROOT_SYSTEMS,
            COUNT(CASE WHEN SYS_PARNT IS NOT NULL AND SYS_PARNT > 0 THEN 1 END) as CHILD_SYSTEMS,
            COUNT(CASE WHEN INACTIVE = 0 OR INACTIVE IS NULL THEN 1 END) as ACTIVE_SYSTEMS,
            COUNT(CASE WHEN INACTIVE = 1 THEN 1 END) as INACTIVE_SYSTEMS
        FROM S_ERP_SYSTEM
        """
        
        hierarchy = db.execute_query(hierarchy_query)
        
        if hierarchy:
            stats = hierarchy[0]
            print(f"\n📈 إحصائيات الهيكل:")
            print(f"  • إجمالي الأنظمة: {stats['TOTAL_SYSTEMS']}")
            print(f"  • الأنظمة الجذر: {stats['ROOT_SYSTEMS']}")
            print(f"  • الأنظمة الفرعية: {stats['CHILD_SYSTEMS']}")
            print(f"  • الأنظمة النشطة: {stats['ACTIVE_SYSTEMS']}")
            print(f"  • الأنظمة غير النشطة: {stats['INACTIVE_SYSTEMS']}")
        
        # عرض الأنظمة الجذر
        root_systems_query = """
        SELECT 
            SYS_NO,
            SYS_CODE,
            SYS_NAME,
            ORDR_NO
        FROM S_ERP_SYSTEM
        WHERE (SYS_PARNT IS NULL OR SYS_PARNT = 0)
        AND (INACTIVE = 0 OR INACTIVE IS NULL)
        ORDER BY ORDR_NO, SYS_NO
        """
        
        root_systems = db.execute_query(root_systems_query)
        
        if root_systems:
            print(f"\n🌳 الأنظمة الجذر ({len(root_systems)} نظام):")
            for root in root_systems:
                print(f"  • {root['SYS_NO']}: {root['SYS_NAME']} [{root['SYS_CODE']}] (Order: {root['ORDR_NO']})")
        
        db.disconnect()
        print("\n✅ تم تحليل بنية الجدول بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في تحليل الجدول: {e}")
        db.disconnect()

if __name__ == "__main__":
    analyze_table_structure()
