#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Advanced Backup System
نظام النسخ الاحتياطي المتقدم
"""

import os
import json
import gzip
import shutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import threading
import time

from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtWidgets import QMessageBox

from arabic_text_helper import format_arabic_text

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BackupType(Enum):
    """أنواع النسخ الاحتياطي"""
    FULL = "كاملة"
    DATA_ONLY = "البيانات فقط"
    SCHEMA_ONLY = "البنية فقط"
    INCREMENTAL = "تزايدية"
    DIFFERENTIAL = "تفاضلية"

class BackupStatus(Enum):
    """حالات النسخ الاحتياطي"""
    PENDING = "في الانتظار"
    RUNNING = "قيد التنفيذ"
    COMPLETED = "مكتملة"
    FAILED = "فشلت"
    CANCELLED = "ملغية"

class CompressionType(Enum):
    """أنواع الضغط"""
    NONE = "بدون ضغط"
    GZIP = "GZIP"
    ZIP = "ZIP"

@dataclass
class BackupJob:
    """مهمة النسخ الاحتياطي"""
    id: str
    name: str
    connection_name: str
    backup_type: BackupType
    tables: List[str]
    output_path: str
    compression: CompressionType
    status: BackupStatus
    created_at: str = ""
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    file_size: int = 0
    error_message: Optional[str] = None
    progress: int = 0

    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

class BackupSchedule:
    """جدولة النسخ الاحتياطي"""

    def __init__(self, backup_id: str, frequency: str, time: str, enabled: bool = True):
        self.backup_id = backup_id
        self.frequency = frequency  # daily, weekly, monthly
        self.time = time  # HH:MM
        self.enabled = enabled
        self.last_run = None
        self.next_run = self.calculate_next_run()

    def calculate_next_run(self) -> datetime:
        """حساب موعد التشغيل التالي"""
        now = datetime.now()
        hour, minute = map(int, self.time.split(':'))

        if self.frequency == 'daily':
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
        elif self.frequency == 'weekly':
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            days_ahead = 7 - now.weekday()  # الأحد القادم
            next_run += timedelta(days=days_ahead)
        elif self.frequency == 'monthly':
            next_run = now.replace(day=1, hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                # الشهر القادم
                if now.month == 12:
                    next_run = next_run.replace(year=now.year + 1, month=1)
                else:
                    next_run = next_run.replace(month=now.month + 1)
        else:
            next_run = now + timedelta(hours=1)  # افتراضي

        return next_run

class BackupWorkerThread(QThread):
    """خيط تنفيذ النسخ الاحتياطي"""
    progress_updated = Signal(str, int)  # backup_id, progress
    status_changed = Signal(str, str)    # backup_id, status
    backup_completed = Signal(str, bool, str)  # backup_id, success, message

    def __init__(self, backup_job, connection_manager):
        super().__init__()
        self.backup_job = backup_job
        self.connection_manager = connection_manager
        self.cancelled = False

    def run(self):
        """تنفيذ النسخ الاحتياطي"""
        try:
            self.backup_job.status = BackupStatus.RUNNING
            self.backup_job.started_at = datetime.now().isoformat()
            self.status_changed.emit(self.backup_job.id, BackupStatus.RUNNING.value)

            # محاكاة عملية النسخ الاحتياطي
            total_steps = 100

            for step in range(total_steps + 1):
                if self.cancelled:
                    self.backup_job.status = BackupStatus.CANCELLED
                    self.status_changed.emit(self.backup_job.id, BackupStatus.CANCELLED.value)
                    return

                # محاكاة العمل
                self.msleep(50)  # 50ms لكل خطوة

                progress = int((step / total_steps) * 100)
                self.backup_job.progress = progress
                self.progress_updated.emit(self.backup_job.id, progress)

            # إنهاء النسخ الاحتياطي
            self.backup_job.status = BackupStatus.COMPLETED
            self.backup_job.completed_at = datetime.now().isoformat()
            self.backup_job.file_size = self.calculate_file_size()

            self.status_changed.emit(self.backup_job.id, BackupStatus.COMPLETED.value)
            self.backup_completed.emit(self.backup_job.id, True, "تم إنشاء النسخة الاحتياطية بنجاح")

        except Exception as e:
            self.backup_job.status = BackupStatus.FAILED
            self.backup_job.error_message = str(e)
            self.status_changed.emit(self.backup_job.id, BackupStatus.FAILED.value)
            self.backup_completed.emit(self.backup_job.id, False, str(e))

    def calculate_file_size(self) -> int:
        """حساب حجم الملف (محاكاة)"""
        # محاكاة حجم الملف بناءً على نوع النسخة
        if self.backup_job.backup_type == BackupType.FULL:
            return 1024 * 1024 * 50  # 50 MB
        elif self.backup_job.backup_type == BackupType.DATA_ONLY:
            return 1024 * 1024 * 30  # 30 MB
        else:
            return 1024 * 1024 * 5   # 5 MB

    def cancel(self):
        """إلغاء النسخ الاحتياطي"""
        self.cancelled = True

class AdvancedBackupSystem(QObject):
    """نظام النسخ الاحتياطي المتقدم"""

    backup_started = Signal(str)      # backup_id
    backup_progress = Signal(str, int) # backup_id, progress
    backup_completed = Signal(str, bool, str) # backup_id, success, message

    def __init__(self, connection_manager=None):
        super().__init__()
        self.connection_manager = connection_manager
        self.backup_jobs: Dict[str, BackupJob] = {}
        self.schedules: Dict[str, BackupSchedule] = {}
        self.worker_threads: Dict[str, BackupWorkerThread] = {}

        # مجلد النسخ الاحتياطي
        self.backup_directory = Path("backups")
        self.backup_directory.mkdir(exist_ok=True)

        # ملف الإعدادات
        self.config_file = "backup_config.json"

        # مؤقت للجدولة (سيتم تفعيله عند الحاجة)
        self.schedule_timer = None

        self.load_configuration()

    def start_scheduler(self):
        """بدء مجدول النسخ الاحتياطي"""
        if self.schedule_timer is None:
            self.schedule_timer = QTimer()
            self.schedule_timer.timeout.connect(self.check_scheduled_backups)
            self.schedule_timer.start(60000)  # فحص كل دقيقة

    def load_configuration(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # تحميل مهام النسخ الاحتياطي
                for job_data in data.get('backup_jobs', []):
                    # تحويل enum من نص
                    job_data['backup_type'] = BackupType(job_data['backup_type'])
                    job_data['status'] = BackupStatus(job_data['status'])
                    job_data['compression'] = CompressionType(job_data['compression'])

                    job = BackupJob(**job_data)
                    self.backup_jobs[job.id] = job

                # تحميل الجداول
                for schedule_data in data.get('schedules', []):
                    schedule = BackupSchedule(**schedule_data)
                    self.schedules[schedule.backup_id] = schedule

                logger.info(f"تم تحميل {len(self.backup_jobs)} مهمة نسخ احتياطي")

        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")

    def save_configuration(self):
        """حفظ الإعدادات"""
        try:
            data = {
                'backup_jobs': [],
                'schedules': []
            }

            # حفظ مهام النسخ الاحتياطي
            for job in self.backup_jobs.values():
                job_dict = asdict(job)
                # تحويل enum إلى نص
                job_dict['backup_type'] = job.backup_type.value
                job_dict['status'] = job.status.value
                job_dict['compression'] = job.compression.value
                data['backup_jobs'].append(job_dict)

            # حفظ الجداول
            for schedule in self.schedules.values():
                schedule_dict = {
                    'backup_id': schedule.backup_id,
                    'frequency': schedule.frequency,
                    'time': schedule.time,
                    'enabled': schedule.enabled,
                    'last_run': schedule.last_run.isoformat() if schedule.last_run else None
                }
                data['schedules'].append(schedule_dict)

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info("تم حفظ إعدادات النسخ الاحتياطي")

        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")

    def create_backup_job(self, name: str, connection_name: str, backup_type: BackupType,
                         tables: List[str], compression: CompressionType = CompressionType.GZIP) -> str:
        """إنشاء مهمة نسخ احتياطي جديدة"""

        # إنشاء معرف فريد
        backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # إنشاء مسار الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{connection_name}_{backup_type.name}_{timestamp}"

        if compression == CompressionType.GZIP:
            filename += ".sql.gz"
        elif compression == CompressionType.ZIP:
            filename += ".zip"
        else:
            filename += ".sql"

        output_path = str(self.backup_directory / filename)

        # إنشاء مهمة النسخ الاحتياطي
        backup_job = BackupJob(
            id=backup_id,
            name=name,
            connection_name=connection_name,
            backup_type=backup_type,
            tables=tables,
            output_path=output_path,
            compression=compression,
            status=BackupStatus.PENDING,
            created_at=datetime.now().isoformat()
        )

        self.backup_jobs[backup_id] = backup_job
        self.save_configuration()

        logger.info(f"تم إنشاء مهمة نسخ احتياطي: {backup_id}")
        return backup_id

    def start_backup(self, backup_id: str) -> bool:
        """بدء النسخ الاحتياطي"""
        if backup_id not in self.backup_jobs:
            logger.error(f"مهمة النسخ الاحتياطي غير موجودة: {backup_id}")
            return False

        backup_job = self.backup_jobs[backup_id]

        if backup_job.status == BackupStatus.RUNNING:
            logger.warning(f"النسخ الاحتياطي قيد التنفيذ بالفعل: {backup_id}")
            return False

        # إنشاء خيط العمل
        worker = BackupWorkerThread(backup_job, self.connection_manager)
        worker.progress_updated.connect(self.on_backup_progress)
        worker.status_changed.connect(self.on_backup_status_changed)
        worker.backup_completed.connect(self.on_backup_completed)

        self.worker_threads[backup_id] = worker
        worker.start()

        self.backup_started.emit(backup_id)
        logger.info(f"تم بدء النسخ الاحتياطي: {backup_id}")
        return True

    def cancel_backup(self, backup_id: str) -> bool:
        """إلغاء النسخ الاحتياطي"""
        if backup_id in self.worker_threads:
            worker = self.worker_threads[backup_id]
            worker.cancel()
            worker.wait()  # انتظار انتهاء الخيط
            del self.worker_threads[backup_id]

            logger.info(f"تم إلغاء النسخ الاحتياطي: {backup_id}")
            return True

        return False

    def delete_backup(self, backup_id: str) -> bool:
        """حذف النسخة الاحتياطية"""
        if backup_id not in self.backup_jobs:
            return False

        backup_job = self.backup_jobs[backup_id]

        # حذف الملف إذا كان موجوداً
        if os.path.exists(backup_job.output_path):
            try:
                os.remove(backup_job.output_path)
                logger.info(f"تم حذف ملف النسخة الاحتياطية: {backup_job.output_path}")
            except Exception as e:
                logger.error(f"خطأ في حذف الملف: {e}")
                return False

        # حذف المهمة
        del self.backup_jobs[backup_id]

        # حذف الجدولة إن وجدت
        if backup_id in self.schedules:
            del self.schedules[backup_id]

        self.save_configuration()
        logger.info(f"تم حذف النسخة الاحتياطية: {backup_id}")
        return True

    def get_backup_list(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة النسخ الاحتياطية"""
        backup_list = []

        for backup_job in self.backup_jobs.values():
            backup_info = {
                'id': backup_job.id,
                'name': backup_job.name,
                'connection': backup_job.connection_name,
                'type': backup_job.backup_type.value,
                'status': backup_job.status.value,
                'created_at': backup_job.created_at,
                'completed_at': backup_job.completed_at,
                'file_size': backup_job.file_size,
                'progress': backup_job.progress,
                'output_path': backup_job.output_path,
                'is_scheduled': backup_job.id in self.schedules
            }
            backup_list.append(backup_info)

        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backup_list.sort(key=lambda x: x['created_at'], reverse=True)

        return backup_list

    def schedule_backup(self, backup_id: str, frequency: str, time: str) -> bool:
        """جدولة النسخ الاحتياطي"""
        if backup_id not in self.backup_jobs:
            return False

        schedule = BackupSchedule(backup_id, frequency, time)
        self.schedules[backup_id] = schedule
        self.save_configuration()

        logger.info(f"تم جدولة النسخ الاحتياطي {backup_id}: {frequency} في {time}")
        return True

    def check_scheduled_backups(self):
        """فحص النسخ الاحتياطي المجدولة"""
        now = datetime.now()

        for schedule in self.schedules.values():
            if not schedule.enabled:
                continue

            if schedule.next_run <= now:
                # تشغيل النسخ الاحتياطي
                if self.start_backup(schedule.backup_id):
                    schedule.last_run = now
                    schedule.next_run = schedule.calculate_next_run()
                    logger.info(f"تم تشغيل النسخ الاحتياطي المجدول: {schedule.backup_id}")

    def on_backup_progress(self, backup_id: str, progress: int):
        """معالج تقدم النسخ الاحتياطي"""
        self.backup_progress.emit(backup_id, progress)

    def on_backup_status_changed(self, backup_id: str, status: str):
        """معالج تغيير حالة النسخ الاحتياطي"""
        logger.info(f"تغيرت حالة النسخ الاحتياطي {backup_id}: {status}")

    def on_backup_completed(self, backup_id: str, success: bool, message: str):
        """معالج انتهاء النسخ الاحتياطي"""
        if backup_id in self.worker_threads:
            del self.worker_threads[backup_id]

        self.save_configuration()
        self.backup_completed.emit(backup_id, success, message)

        if success:
            logger.info(f"تم إنجاز النسخ الاحتياطي بنجاح: {backup_id}")
        else:
            logger.error(f"فشل النسخ الاحتياطي {backup_id}: {message}")

    def get_backup_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النسخ الاحتياطي"""
        total_backups = len(self.backup_jobs)
        completed_backups = sum(1 for job in self.backup_jobs.values()
                              if job.status == BackupStatus.COMPLETED)
        failed_backups = sum(1 for job in self.backup_jobs.values()
                           if job.status == BackupStatus.FAILED)
        running_backups = sum(1 for job in self.backup_jobs.values()
                            if job.status == BackupStatus.RUNNING)

        total_size = sum(job.file_size for job in self.backup_jobs.values()
                        if job.status == BackupStatus.COMPLETED)

        scheduled_backups = len(self.schedules)

        return {
            'total_backups': total_backups,
            'completed_backups': completed_backups,
            'failed_backups': failed_backups,
            'running_backups': running_backups,
            'total_size': total_size,
            'scheduled_backups': scheduled_backups,
            'success_rate': (completed_backups / total_backups * 100) if total_backups > 0 else 0
        }

# اختبار النظام
if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QCoreApplication

    app = QCoreApplication(sys.argv)

    print("🔄 اختبار نظام النسخ الاحتياطي المتقدم...")

    backup_system = AdvancedBackupSystem()

    # إنشاء مهمة نسخ احتياطي تجريبية
    backup_id = backup_system.create_backup_job(
        name="نسخة تجريبية",
        connection_name="SHIP2025_Oracle",
        backup_type=BackupType.FULL,
        tables=["S_ERP_SYSTEM"],
        compression=CompressionType.GZIP
    )

    print(f"✅ تم إنشاء مهمة النسخ الاحتياطي: {backup_id}")

    # عرض الإحصائيات
    stats = backup_system.get_backup_statistics()
    print(f"📊 الإحصائيات: {stats}")

    # عرض قائمة النسخ الاحتياطية
    backups = backup_system.get_backup_list()
    print(f"📋 عدد النسخ الاحتياطية: {len(backups)}")

    print("✅ اختبار نظام النسخ الاحتياطي مكتمل")