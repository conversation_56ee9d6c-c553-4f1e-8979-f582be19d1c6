#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للاتصال بقاعدة البيانات
"""

import sys

def test_connection():
    """اختبار الاتصال"""
    try:
        print("🔄 اختبار الاتصال بقاعدة البيانات...")
        
        import cx_Oracle
        print("✅ تم استيراد cx_Oracle")
        
        # معلومات الاتصال
        username = 'ship2025'
        password = 'ys123'
        dsn = 'localhost:1521/orcl'
        
        print(f"🔗 محاولة الاتصال: {username}@{dsn}")
        
        # الاتصال
        connection = cx_Oracle.connect(username, password, dsn)
        print("✅ نجح الاتصال!")
        
        # اختبار بسيط
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM S_ERP_SYSTEM")
        count = cursor.fetchone()[0]
        print(f"📊 عدد السجلات في S_ERP_SYSTEM: {count}")
        
        # إغلاق
        cursor.close()
        connection.close()
        print("✅ تم إغلاق الاتصال")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("🚢 SHP ERP - اختبار الاتصال البسيط")
    print("=" * 50)
    
    success = test_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 نجح الاتصال!")
    else:
        print("❌ فشل الاتصال!")
    print("=" * 50)
