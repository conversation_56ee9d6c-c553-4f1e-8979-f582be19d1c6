#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - General Settings Launcher
مشغل نظام الإعدادات العامة
"""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox

def launch_general_settings(system_code=None):
    """تشغيل نظام الإعدادات العامة"""
    try:
        from general_settings_system import GeneralSettingsSystem
        
        # إنشاء التطبيق إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
            
        # إعداد التطبيق للنصوص العربية
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
        
        # إنشاء النافذة
        window = GeneralSettingsSystem()
        
        # فتح القسم المحدد إذا تم تمرير رمز النظام
        if system_code:
            window.open_specific_section(system_code)
        
        window.show()
        
        print(f"🚀 تم تشغيل نظام الإعدادات العامة - القسم: {system_code or 'الرئيسي'}")
        
        # تشغيل التطبيق إذا لم يكن يعمل
        if not app.instance():
            sys.exit(app.exec())
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل نظام الإعدادات العامة: {e}")
        if 'app' in locals():
            QMessageBox.critical(None, "خطأ", f"فشل في تشغيل نظام الإعدادات العامة:\n{str(e)}")

def main():
    """الدالة الرئيسية"""
    # تحديد القسم بناءً على معامل سطر الأوامر
    system_code = None
    if len(sys.argv) > 1:
        system_code = sys.argv[1]
    
    launch_general_settings(system_code)

if __name__ == "__main__":
    main()
