#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لبيانات الأنظمة
"""

def test_systems_data():
    """اختبار بيانات الأنظمة"""
    try:
        from erp_systems_manager import ERPSystemsManager
        
        # إنشاء مدير الأنظمة
        manager = ERPSystemsManager()
        
        print("🔄 تحميل بيانات الأنظمة...")
        
        # تحميل البيانات
        systems_data = manager.load_systems_data()
        
        print(f"✅ تم تحميل {len(systems_data)} نظام")
        
        # البحث عن نظام الإعدادات العامة
        general_settings_found = False
        general_settings_subsystems = []
        
        print("\n📋 الأنظمة المحملة:")
        print("-" * 60)
        
        for system in systems_data:
            system_id = system.get('SYSTEM_ID', '')
            system_code = system.get('SYSTEM_CODE', '')
            system_name = system.get('SYSTEM_NAME', '')
            parent_id = system.get('PARENT_SYSTEM_ID')
            
            print(f"ID: {system_id:<3} | Code: {system_code:<15} | Name: {system_name}")
            
            if system_code == 'GENSETTINGS':
                general_settings_found = True
                print(f"   ✅ النظام الرئيسي للإعدادات العامة!")
            
            # البحث عن الأنظمة الفرعية
            if parent_id == 10:  # الأنظمة الفرعية للإعدادات العامة
                general_settings_subsystems.append({
                    'id': system_id,
                    'code': system_code,
                    'name': system_name
                })
                print(f"   📂 نظام فرعي للإعدادات العامة")
        
        print("-" * 60)
        
        if general_settings_found:
            print("🎉 نظام الإعدادات العامة موجود!")
            
            if general_settings_subsystems:
                print(f"\n📋 الأنظمة الفرعية ({len(general_settings_subsystems)}):")
                for subsystem in general_settings_subsystems:
                    print(f"   {subsystem['id']}: {subsystem['name']} [{subsystem['code']}]")
            else:
                print("⚠️ لا توجد أنظمة فرعية")
        else:
            print("❌ لم يتم العثور على نظام الإعدادات العامة")
        
        return general_settings_found, len(general_settings_subsystems)
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار بيانات الأنظمة")
    print("=" * 60)
    
    found, subsystems_count = test_systems_data()
    
    print("\n" + "=" * 60)
    print("📊 النتيجة:")
    print("=" * 60)
    
    if found:
        print("✅ نظام الإعدادات العامة متاح!")
        print(f"📂 عدد الأنظمة الفرعية: {subsystems_count}")
        
        print("\n🎯 الخطوات التالية:")
        print("1️⃣ شغل النافذة الرئيسية: python main_window.py")
        print("2️⃣ ابحث عن: ⚙️ نظام الإعدادات العامة الشامل")
        print("3️⃣ انقر على النظام لفتحه")
        
        if subsystems_count > 0:
            print("4️⃣ أو انقر على أي نظام فرعي:")
            print("   🎨 إعدادات المظهر")
            print("   🏢 بيانات الشركة") 
            print("   💰 إعدادات العملات")
            print("   📅 السنة المالية")
            print("   وغيرها...")
    else:
        print("❌ نظام الإعدادات العامة غير متاح")
        print("🔧 تحقق من إعدادات قاعدة البيانات")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
