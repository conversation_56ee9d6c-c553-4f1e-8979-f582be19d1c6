#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Database Connection Module
وحدة الاتصال بقاعدة البيانات Oracle
"""

import cx_Oracle
import os
from typing import Optional, List, Dict, Any

class OracleConnection:
    """فئة الاتصال بقاعدة بيانات Oracle"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
        
    def connect(self, username: str = "SHIP2025", password: str = None,
                host: str = "localhost", port: int = 1521,
                service_name: str = "orcl") -> bool:
        """
        الاتصال بقاعدة البيانات Oracle
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            host: عنوان الخادم
            port: منفذ الاتصال
            service_name: اسم الخدمة
            
        Returns:
            bool: True إذا نجح الاتصال، False إذا فشل
        """
        try:
            # طلب كلمة المرور إذا لم تكن محددة
            if password is None or password == "":
                import getpass
                password = getpass.getpass(f"أدخل كلمة مرور المستخدم {username}: ")

            # إنشاء DSN للاتصال
            dsn = cx_Oracle.makedsn(host, port, service_name=service_name)

            # محاولة الاتصال
            self.connection = cx_Oracle.connect(
                user=username,
                password=password,
                dsn=dsn,
                encoding="UTF-8"
            )
            
            self.cursor = self.connection.cursor()
            print(f"✅ تم الاتصال بقاعدة البيانات بنجاح للمستخدم: {username}")
            return True
            
        except cx_Oracle.Error as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            print("✅ تم قطع الاتصال بقاعدة البيانات")
        except Exception as e:
            print(f"❌ خطأ في قطع الاتصال: {e}")
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        تنفيذ استعلام SELECT وإرجاع النتائج
        
        Args:
            query: الاستعلام SQL
            params: معاملات الاستعلام
            
        Returns:
            List[Dict]: قائمة بالنتائج
        """
        try:
            if not self.cursor:
                raise Exception("لا يوجد اتصال بقاعدة البيانات")
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            # الحصول على أسماء الأعمدة
            columns = [desc[0] for desc in self.cursor.description]
            
            # الحصول على البيانات وتحويلها إلى قاموس
            rows = self.cursor.fetchall()
            result = []
            
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    row_dict[columns[i]] = value
                result.append(row_dict)
            
            return result
            
        except cx_Oracle.Error as e:
            print(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            return []
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            return []
    
    def execute_non_query(self, query: str, params: Optional[tuple] = None) -> bool:
        """
        تنفيذ استعلام INSERT/UPDATE/DELETE
        
        Args:
            query: الاستعلام SQL
            params: معاملات الاستعلام
            
        Returns:
            bool: True إذا نجح التنفيذ
        """
        try:
            if not self.cursor:
                raise Exception("لا يوجد اتصال بقاعدة البيانات")
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            self.connection.commit()
            return True
            
        except cx_Oracle.Error as e:
            print(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            return False
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def get_table_structure(self, table_name: str) -> List[Dict[str, Any]]:
        """
        الحصول على هيكل الجدول
        
        Args:
            table_name: اسم الجدول
            
        Returns:
            List[Dict]: معلومات الأعمدة
        """
        query = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            DATA_LENGTH,
            NULLABLE,
            DATA_DEFAULT
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = UPPER(:table_name)
        ORDER BY COLUMN_ID
        """
        
        return self.execute_query(query, (table_name,))
    
    def get_table_comments(self, table_name: str) -> Dict[str, str]:
        """
        الحصول على تعليقات الجدول والأعمدة
        
        Args:
            table_name: اسم الجدول
            
        Returns:
            Dict: تعليقات الأعمدة
        """
        # تعليقات الجدول
        table_comment_query = """
        SELECT COMMENTS 
        FROM USER_TAB_COMMENTS 
        WHERE TABLE_NAME = UPPER(:table_name)
        """
        
        # تعليقات الأعمدة
        column_comments_query = """
        SELECT COLUMN_NAME, COMMENTS 
        FROM USER_COL_COMMENTS 
        WHERE TABLE_NAME = UPPER(:table_name)
        AND COMMENTS IS NOT NULL
        """
        
        result = {"TABLE_COMMENT": ""}
        
        # الحصول على تعليق الجدول
        table_result = self.execute_query(table_comment_query, (table_name,))
        if table_result:
            result["TABLE_COMMENT"] = table_result[0].get("COMMENTS", "")
        
        # الحصول على تعليقات الأعمدة
        column_results = self.execute_query(column_comments_query, (table_name,))
        for row in column_results:
            result[row["COLUMN_NAME"]] = row["COMMENTS"]
        
        return result

# إنشاء مثيل عام للاتصال
db_connection = OracleConnection()

def get_db_connection() -> OracleConnection:
    """الحصول على اتصال قاعدة البيانات"""
    return db_connection

def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔄 اختبار الاتصال بقاعدة البيانات...")
    
    # محاولة الاتصال
    if db_connection.connect():
        print("✅ الاتصال ناجح!")
        
        # اختبار استعلام بسيط
        try:
            result = db_connection.execute_query("SELECT SYSDATE FROM DUAL")
            if result:
                print(f"📅 التاريخ الحالي: {result[0]['SYSDATE']}")
        except Exception as e:
            print(f"❌ خطأ في الاستعلام التجريبي: {e}")
        
        # قطع الاتصال
        db_connection.disconnect()
    else:
        print("❌ فشل الاتصال!")

if __name__ == "__main__":
    test_connection()
