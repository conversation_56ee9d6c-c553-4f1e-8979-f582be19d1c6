#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Quick Launch Script
سكريبت التشغيل السريع لنظام SHP ERP
"""

import os
import sys
import subprocess

def print_header():
    """طباعة رأس البرنامج"""
    print("🚢" + "=" * 58 + "🚢")
    print("🚢" + " " * 20 + "SHP ERP LAUNCHER" + " " * 21 + "🚢")
    print("🚢" + " " * 15 + "نظام تخطيط موارد المؤسسة البحرية" + " " * 15 + "🚢")
    print("🚢" + "=" * 58 + "🚢")
    print()

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = [
        'PySide6',
        'arabic_reshaper', 
        'bidi',
        'cx_Oracle'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    return missing_packages

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    try:
        # تغيير المجلد إلى SHP_ERP
        os.chdir(r'E:\SHP_ERP')
        
        # تثبيت المتطلبات
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع المتطلبات بنجاح!")
            return True
        else:
            print(f"❌ خطأ في التثبيت: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🗃️ اختبار الاتصال بقاعدة البيانات...")
    
    try:
        # تشغيل اختبار الاتصال
        os.chdir(r'E:\SHP_ERP')
        result = subprocess.run([
            sys.executable, 'test_connection.py'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print(f"تحذيرات: {result.stderr}")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def launch_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل SHP ERP...")
    
    try:
        # تغيير المجلد إلى SHP_ERP
        os.chdir(r'E:\SHP_ERP')
        
        # تشغيل التطبيق
        subprocess.run([sys.executable, 'main_window.py'])
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

def show_project_info():
    """عرض معلومات المشروع"""
    print("\n📋 معلومات المشروع:")
    print("=" * 40)
    print("📁 المسار: E:\\SHP_ERP")
    print("🗃️ قاعدة البيانات: Oracle")
    print("👤 المستخدم: SHIP2025")
    print("🌐 اللغة: العربية + الإنجليزية")
    print("🎨 الواجهة: PySide6 مع RTL")
    
    # فحص الملفات
    project_path = r'E:\SHP_ERP'
    if os.path.exists(project_path):
        files = os.listdir(project_path)
        print(f"\n📂 الملفات ({len(files)}):")
        for file in sorted(files):
            if file.endswith('.py'):
                print(f"  🐍 {file}")
            elif file.endswith('.txt'):
                print(f"  📄 {file}")
            elif file.endswith('.md'):
                print(f"  📝 {file}")
            else:
                print(f"  📁 {file}")
    else:
        print("❌ مجلد المشروع غير موجود!")

def main_menu():
    """القائمة الرئيسية"""
    while True:
        print_header()
        print("اختر العملية المطلوبة:")
        print()
        print("1️⃣  تشغيل SHP ERP")
        print("2️⃣  فحص المتطلبات")
        print("3️⃣  تثبيت المتطلبات")
        print("4️⃣  اختبار قاعدة البيانات")
        print("5️⃣  معلومات المشروع")
        print("6️⃣  تشغيل مع اختبار شامل")
        print("0️⃣  خروج")
        print()
        
        choice = input("👆 اختر رقم العملية: ").strip()
        
        if choice == '1':
            launch_application()
            
        elif choice == '2':
            missing = check_requirements()
            if not missing:
                print("\n✅ جميع المتطلبات متوفرة!")
            else:
                print(f"\n⚠️ المتطلبات المفقودة: {', '.join(missing)}")
                
        elif choice == '3':
            if install_requirements():
                print("\n✅ تم تثبيت المتطلبات بنجاح!")
            else:
                print("\n❌ فشل في تثبيت المتطلبات")
                
        elif choice == '4':
            test_database_connection()
            
        elif choice == '5':
            show_project_info()
            
        elif choice == '6':
            print("\n🔄 تشغيل مع اختبار شامل...")
            
            # فحص المتطلبات
            missing = check_requirements()
            if missing:
                print(f"⚠️ متطلبات مفقودة: {', '.join(missing)}")
                install_choice = input("هل تريد تثبيتها؟ (y/n): ").lower()
                if install_choice == 'y':
                    if not install_requirements():
                        print("❌ فشل التثبيت. لا يمكن المتابعة.")
                        continue
                else:
                    print("❌ لا يمكن التشغيل بدون المتطلبات.")
                    continue
            
            # اختبار قاعدة البيانات
            print("\n🗃️ اختبار قاعدة البيانات...")
            if test_database_connection():
                print("✅ اختبار قاعدة البيانات نجح!")
            else:
                print("⚠️ مشاكل في قاعدة البيانات، لكن يمكن المتابعة.")
            
            # تشغيل التطبيق
            launch_application()
            
        elif choice == '0':
            print("\n👋 شكراً لاستخدام SHP ERP!")
            break
            
        else:
            print("❌ خيار غير صحيح!")
            
        input("\n⏸️ اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main_menu()
