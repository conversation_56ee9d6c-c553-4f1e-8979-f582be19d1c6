#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Enhanced Main Window
الواجهة الرئيسية المحسنة مع نظام إدارة قاعدة البيانات المتقدم
"""

import sys
import time
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QProgressBar, QStatusBar, QMessageBox,
    QSplitter, QFrame, QTextEdit, QGroupBox
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QPalette, QColor

from enhanced_systems_manager import EnhancedSystemsManager
from systems_tree_widget import SystemsTreeWidget

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class ConnectionStatusWidget(QWidget):
    """مكون حالة الاتصال"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # مؤشر الحالة
        self.status_indicator = QLabel("●")
        self.status_indicator.setStyleSheet("color: red; font-size: 16px;")
        layout.addWidget(self.status_indicator)
        
        # نص الحالة
        self.status_text = QLabel("غير متصل")
        self.status_text.setFont(QFont("Tahoma", 10))
        layout.addWidget(self.status_text)
        
        layout.addStretch()
        
        # زر إعادة الاتصال
        self.reconnect_btn = QPushButton("إعادة الاتصال")
        self.reconnect_btn.setFont(QFont("Tahoma", 9))
        self.reconnect_btn.setMaximumWidth(100)
        layout.addWidget(self.reconnect_btn)
    
    def update_status(self, connected: bool, message: str = ""):
        """تحديث حالة الاتصال"""
        if connected:
            self.status_indicator.setStyleSheet("color: green; font-size: 16px;")
            self.status_text.setText("متصل")
            self.reconnect_btn.setEnabled(False)
        else:
            self.status_indicator.setStyleSheet("color: red; font-size: 16px;")
            self.status_text.setText("غير متصل")
            self.reconnect_btn.setEnabled(True)
        
        if message:
            self.status_text.setToolTip(message)

class ProgressWidget(QWidget):
    """مكون التقدم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # نص التقدم
        self.progress_text = QLabel("")
        self.progress_text.setFont(QFont("Tahoma", 10))
        self.progress_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.progress_text)
    
    def show_progress(self, text: str = ""):
        """عرض التقدم"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.progress_text.setText(text)
        self.progress_text.setVisible(True)
    
    def hide_progress(self):
        """إخفاء التقدم"""
        self.progress_bar.setVisible(False)
        self.progress_text.setVisible(False)
    
    def update_progress_text(self, text: str):
        """تحديث نص التقدم"""
        self.progress_text.setText(text)

class EnhancedMainWindow(QMainWindow):
    """الواجهة الرئيسية المحسنة"""
    
    def __init__(self):
        super().__init__()
        self.systems_manager = EnhancedSystemsManager()
        self.systems_tree_widget = None
        self.init_ui()
        self.setup_connections()
        self.start_connection()
        
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي"""
        if not ARABIC_SUPPORT or not text:
            return text
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text, base_dir='R')
        except:
            return text
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(self.format_arabic_text("SHP ERP - نظام إدارة قاعدة البيانات المتقدم"))
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        central_widget.setLayoutDirection(Qt.RightToLeft)
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط الحالة العلوي
        self.create_status_bar()
        main_layout.addWidget(self.status_frame)
        
        # مكون التقدم
        self.progress_widget = ProgressWidget()
        main_layout.addWidget(self.progress_widget)
        
        # المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter, 1)
        
        # شجرة الأنظمة
        self.systems_tree_widget = SystemsTreeWidget()
        splitter.addWidget(self.systems_tree_widget)
        
        # لوحة المعلومات
        self.create_info_panel()
        splitter.addWidget(self.info_panel)
        
        # تعيين النسب
        splitter.setSizes([800, 400])
        
        # شريط الحالة السفلي
        self.statusBar().showMessage(self.format_arabic_text("جاري التحميل..."))
        
    def create_status_bar(self):
        """إنشاء شريط الحالة العلوي"""
        self.status_frame = QFrame()
        self.status_frame.setFrameStyle(QFrame.StyledPanel)
        self.status_frame.setMaximumHeight(50)
        
        layout = QHBoxLayout(self.status_frame)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # عنوان التطبيق
        title_label = QLabel(self.format_arabic_text("🚢 SHP ERP - نظام إدارة قاعدة البيانات"))
        title_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # مكون حالة الاتصال
        self.connection_status = ConnectionStatusWidget()
        layout.addWidget(self.connection_status)
    
    def create_info_panel(self):
        """إنشاء لوحة المعلومات"""
        self.info_panel = QGroupBox(self.format_arabic_text("معلومات النظام"))
        self.info_panel.setFont(QFont("Tahoma", 11, QFont.Bold))
        
        layout = QVBoxLayout(self.info_panel)
        
        # معلومات الاتصال
        self.connection_info = QTextEdit()
        self.connection_info.setReadOnly(True)
        self.connection_info.setMaximumHeight(150)
        self.connection_info.setFont(QFont("Tahoma", 10))
        layout.addWidget(self.connection_info)
        
        # إحصائيات الأنظمة
        self.systems_stats = QTextEdit()
        self.systems_stats.setReadOnly(True)
        self.systems_stats.setMaximumHeight(150)
        self.systems_stats.setFont(QFont("Tahoma", 10))
        layout.addWidget(self.systems_stats)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton(self.format_arabic_text("تحديث البيانات"))
        self.refresh_btn.setFont(QFont("Tahoma", 10))
        self.refresh_btn.clicked.connect(self.refresh_data)
        buttons_layout.addWidget(self.refresh_btn)
        
        self.db_info_btn = QPushButton(self.format_arabic_text("معلومات قاعدة البيانات"))
        self.db_info_btn.setFont(QFont("Tahoma", 10))
        self.db_info_btn.clicked.connect(self.show_db_info)
        buttons_layout.addWidget(self.db_info_btn)
        
        layout.addLayout(buttons_layout)
        
        # تحديث المعلومات الأولية
        self.update_connection_info()
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        # اتصالات مدير الأنظمة
        self.systems_manager.connection_status_changed.connect(self.on_connection_status_changed)
        self.systems_manager.progress_updated.connect(self.on_progress_updated)
        self.systems_manager.systems_data_updated.connect(self.on_systems_data_updated)
        self.systems_manager.error_occurred.connect(self.on_error_occurred)
        
        # اتصالات مكون الحالة
        self.connection_status.reconnect_btn.clicked.connect(self.reconnect_database)
    
    def start_connection(self):
        """بدء الاتصال بقاعدة البيانات"""
        self.progress_widget.show_progress(self.format_arabic_text("جاري الاتصال بقاعدة البيانات..."))
        self.systems_manager.connect_to_database_async()
    
    def on_connection_status_changed(self, connected: bool, message: str):
        """معالج تغيير حالة الاتصال"""
        self.connection_status.update_status(connected, message)
        
        if connected:
            self.statusBar().showMessage(self.format_arabic_text("✅ متصل بقاعدة البيانات"))
        else:
            self.statusBar().showMessage(self.format_arabic_text(f"❌ {message}"))
            self.progress_widget.hide_progress()
        
        self.update_connection_info()
    
    def on_progress_updated(self, message: str):
        """معالج تحديث التقدم"""
        self.progress_widget.update_progress_text(self.format_arabic_text(message))
    
    def on_systems_data_updated(self, systems_data: list):
        """معالج تحديث بيانات الأنظمة"""
        self.progress_widget.hide_progress()
        
        # تحديث شجرة الأنظمة
        tree_data = self.systems_manager.systems_tree
        self.systems_tree_widget.load_systems_data(systems_data, tree_data)
        
        # تحديث الإحصائيات
        self.update_systems_stats(systems_data)
        
        # تحديث شريط الحالة
        count = len(systems_data)
        self.statusBar().showMessage(
            self.format_arabic_text(f"✅ تم تحميل {count} نظام من قاعدة البيانات")
        )
    
    def on_error_occurred(self, error_message: str):
        """معالج الأخطاء"""
        self.progress_widget.hide_progress()
        
        # عرض رسالة خطأ
        QMessageBox.critical(
            self,
            self.format_arabic_text("خطأ"),
            self.format_arabic_text(error_message)
        )
        
        self.statusBar().showMessage(self.format_arabic_text(f"❌ {error_message}"))
    
    def update_connection_info(self):
        """تحديث معلومات الاتصال"""
        stats = self.systems_manager.get_connection_stats()
        
        info_text = f"""
<h3>معلومات الاتصال</h3>
<table border="1" cellpadding="5" cellspacing="0" style="width:100%">
<tr><td><b>حالة الاتصال:</b></td><td>{'متصل' if stats['is_connected'] else 'غير متصل'}</td></tr>
<tr><td><b>مجموعة الاتصالات:</b></td><td>{'متاحة' if stats['has_connection_pool'] else 'غير متاحة'}</td></tr>
<tr><td><b>المستخدم:</b></td><td>SHIP2025</td></tr>
<tr><td><b>قاعدة البيانات:</b></td><td>Oracle (localhost:1521/orcl)</td></tr>
</table>
        """
        
        self.connection_info.setHtml(info_text)
    
    def update_systems_stats(self, systems_data: list):
        """تحديث إحصائيات الأنظمة"""
        root_systems = self.systems_manager.get_root_systems()
        
        stats_text = f"""
<h3>إحصائيات الأنظمة</h3>
<table border="1" cellpadding="5" cellspacing="0" style="width:100%">
<tr><td><b>إجمالي الأنظمة:</b></td><td>{len(systems_data)}</td></tr>
<tr><td><b>الأنظمة الجذر:</b></td><td>{len(root_systems)}</td></tr>
<tr><td><b>الأنظمة الفرعية:</b></td><td>{len(systems_data) - len(root_systems)}</td></tr>
<tr><td><b>آخر تحديث:</b></td><td>{time.strftime('%Y-%m-%d %H:%M:%S')}</td></tr>
</table>
        """
        
        self.systems_stats.setHtml(stats_text)
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.progress_widget.show_progress(self.format_arabic_text("جاري تحديث البيانات..."))
        self.systems_manager.refresh_systems_data()
    
    def reconnect_database(self):
        """إعادة الاتصال بقاعدة البيانات"""
        self.start_connection()
    
    def show_db_info(self):
        """عرض معلومات قاعدة البيانات"""
        table_info = self.systems_manager.get_table_info()
        
        if 'error' in table_info:
            QMessageBox.warning(
                self,
                self.format_arabic_text("تحذير"),
                self.format_arabic_text(f"لا يمكن الحصول على معلومات قاعدة البيانات: {table_info['error']}")
            )
            return
        
        # عرض معلومات الجدول
        info_dialog = QMessageBox()
        info_dialog.setWindowTitle(self.format_arabic_text("معلومات جدول S_ERP_SYSTEM"))
        
        columns = table_info.get('columns', [])
        info_text = f"الجدول يحتوي على {len(columns)} عمود:\n\n"
        
        for col in columns[:10]:  # أول 10 أعمدة
            info_text += f"• {col['COLUMN_NAME']} ({col['DATA_TYPE']})\n"
        
        if len(columns) > 10:
            info_text += f"... و {len(columns) - 10} عمود آخر"
        
        info_dialog.setText(info_text)
        info_dialog.exec()
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        self.systems_manager.disconnect()
        event.accept()

def main():
    """الدالة الرئيسية"""
    import time
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين خط افتراضي
    font = QFont("Tahoma", 11)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = EnhancedMainWindow()
    window.show()
    
    print("🚀 تم تشغيل SHP ERP المحسن")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
