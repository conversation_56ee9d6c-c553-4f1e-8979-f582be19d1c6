#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Database Connection Test
اختبار الاتصال بقاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(r'E:\SHP_ERP')

try:
    from database_connection import OracleConnection
    from erp_systems_manager import ERPSystemsManager
    print("✅ تم تحميل وحدات قاعدة البيانات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل الوحدات: {e}")
    sys.exit(1)

def test_oracle_connection():
    """اختبار الاتصال بقاعدة بيانات Oracle"""
    print("🔄 اختبار الاتصال بقاعدة بيانات Oracle...")
    print("=" * 50)
    
    # إنشاء اتصال
    db = OracleConnection()
    
    try:
        # محاولة الاتصال
        print("📡 محاولة الاتصال بالخادم...")
        success = db.connect(
            username="SHIP2025",
            password="",  # سيتم طلب كلمة المرور
            host="localhost",
            port=1521,
            service_name="orcl"
        )
        
        if not success:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
            
        print("✅ تم الاتصال بنجاح!")
        
        # اختبار استعلام بسيط
        print("\n🔍 اختبار استعلام بسيط...")
        result = db.execute_query("SELECT SYSDATE FROM DUAL")
        if result:
            print(f"📅 التاريخ الحالي: {result[0]['SYSDATE']}")
        
        # اختبار وجود الجدول
        print("\n🗃️ فحص جدول S_ERP_SYSTEM...")
        table_check = db.execute_query("""
            SELECT COUNT(*) as TABLE_COUNT 
            FROM USER_TABLES 
            WHERE TABLE_NAME = 'S_ERP_SYSTEM'
        """)
        
        if table_check and table_check[0]['TABLE_COUNT'] > 0:
            print("✅ جدول S_ERP_SYSTEM موجود")
            
            # فحص البيانات
            data_check = db.execute_query("SELECT COUNT(*) as ROW_COUNT FROM S_ERP_SYSTEM")
            if data_check:
                row_count = data_check[0]['ROW_COUNT']
                print(f"📊 عدد الأنظمة في الجدول: {row_count}")
                
                if row_count > 0:
                    # عرض عينة من البيانات
                    sample_data = db.execute_query("""
                        SELECT SYSTEM_ID, SYSTEM_NAME, SYSTEM_NAME_AR, SYSTEM_LEVEL
                        FROM S_ERP_SYSTEM 
                        WHERE ROWNUM <= 5
                        ORDER BY SYSTEM_LEVEL, SYSTEM_ORDER
                    """)
                    
                    print("\n📋 عينة من الأنظمة:")
                    for system in sample_data:
                        system_id = system.get('SYSTEM_ID', '')
                        system_name = system.get('SYSTEM_NAME', '')
                        system_name_ar = system.get('SYSTEM_NAME_AR', '')
                        level = system.get('SYSTEM_LEVEL', 0)
                        
                        print(f"  • {system_id} (Level {level}): {system_name_ar} | {system_name}")
                else:
                    print("⚠️ الجدول فارغ - لا توجد أنظمة")
        else:
            print("❌ جدول S_ERP_SYSTEM غير موجود")
            
        # قطع الاتصال
        db.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        db.disconnect()
        return False

def test_systems_manager():
    """اختبار مدير الأنظمة"""
    print("\n🔄 اختبار مدير الأنظمة...")
    print("=" * 50)
    
    try:
        # إنشاء مدير الأنظمة
        manager = ERPSystemsManager()
        
        # محاولة الاتصال
        if manager.connect_to_database():
            print("✅ تم الاتصال بنجاح عبر مدير الأنظمة")
            
            # الحصول على معلومات الجدول
            print("\n📋 معلومات الجدول:")
            table_info = manager.get_table_info()
            
            # تحميل الأنظمة
            print("\n🔄 تحميل الأنظمة...")
            systems = manager.load_systems_data()
            
            if systems:
                print(f"✅ تم تحميل {len(systems)} نظام")
                
                # بناء الشجرة
                tree = manager.build_systems_tree()
                root_count = len(tree.get('root_systems', []))
                print(f"🌳 عدد الأنظمة الجذر: {root_count}")
                
            # قطع الاتصال
            manager.disconnect()
            return True
        else:
            print("❌ فشل الاتصال عبر مدير الأنظمة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الأنظمة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚢 SHP ERP - اختبار الاتصال بقاعدة البيانات")
    print("=" * 60)
    
    # اختبار الاتصال الأساسي
    connection_success = test_oracle_connection()
    
    if connection_success:
        # اختبار مدير الأنظمة
        manager_success = test_systems_manager()
        
        if manager_success:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ النظام جاهز للتشغيل")
        else:
            print("\n⚠️ اختبار مدير الأنظمة فشل")
    else:
        print("\n❌ اختبار الاتصال الأساسي فشل")
        
    print("\n" + "=" * 60)
    print("انتهى الاختبار")

if __name__ == "__main__":
    main()
