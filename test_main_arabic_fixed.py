#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Test Main Window with Arabic Fix
اختبار النافذة الرئيسية مع إصلاح العربية
"""

import sys
import time

def test_arabic_helper():
    """اختبار مساعد النصوص العربية"""
    print("🔄 اختبار مساعد النصوص العربية...")
    
    try:
        from arabic_text_helper import format_arabic_text
        
        test_texts = [
            "مرحباً بك في نظام SHP ERP",
            "نظام تخطيط موارد المؤسسة",
            "قاعدة البيانات Oracle",
            "ship2025"
        ]
        
        for text in test_texts:
            formatted = format_arabic_text(text)
            print(f"الأصلي: {text}")
            print(f"المنسق: {formatted}")
            print("---")
        
        print("✅ مساعد النصوص العربية يعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مساعد النصوص: {e}")
        return False

def test_main_window_import():
    """اختبار استيراد النافذة الرئيسية"""
    print("\n🔄 اختبار استيراد النافذة الرئيسية...")
    
    try:
        from main_window import SHPERPMainWindow
        print("✅ تم استيراد النافذة الرئيسية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد النافذة: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔄 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        success = db.connect(
            username="ship2025",
            password="ys123",
            host="localhost",
            port=1521,
            service_name="orcl"
        )
        
        if success:
            print("✅ نجح الاتصال بقاعدة البيانات")
            
            # اختبار استعلام
            result = db.execute_query("SELECT COUNT(*) as COUNT FROM S_ERP_SYSTEM")
            if result:
                count = result[0]['COUNT']
                print(f"📊 عدد الأنظمة: {count}")
            
            db.disconnect()
            return True
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_systems_manager():
    """اختبار مدير الأنظمة"""
    print("\n🔄 اختبار مدير الأنظمة...")
    
    try:
        from erp_systems_manager import ERPSystemsManager
        
        manager = ERPSystemsManager()
        
        if manager.connect_to_database():
            print("✅ نجح اتصال مدير الأنظمة")
            
            systems_data = manager.load_systems_data()
            print(f"📋 تم تحميل {len(systems_data)} نظام")
            
            # عرض عينة مع النصوص المنسقة
            if systems_data:
                from arabic_text_helper import format_arabic_text
                print("📋 عينة من الأنظمة (مع تنسيق عربي):")
                for i, system in enumerate(systems_data[:3]):
                    name = format_arabic_text(system['SYSTEM_NAME'])
                    code = system['SYSTEM_CODE']
                    print(f"  {i+1}. {name} [{code}]")
            
            manager.disconnect()
            return True
        else:
            print("❌ فشل اتصال مدير الأنظمة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في مدير الأنظمة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار النافذة الرئيسية مع إصلاح العربية")
    print("=" * 70)
    
    tests = [
        ("مساعد النصوص العربية", test_arabic_helper),
        ("استيراد النافذة الرئيسية", test_main_window_import),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("مدير الأنظمة", test_systems_manager)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "="*70)
    print("📊 ملخص نتائج الاختبار:")
    print("="*70)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النافذة الرئيسية جاهزة مع إصلاح النصوص العربية")
        print("\n🚀 لتشغيل النافذة الرئيسية:")
        print("python main_window.py")
        print("\n📝 ملاحظة: النصوص العربية الآن تعرض بشكل صحيح")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("="*70)

if __name__ == "__main__":
    main()
