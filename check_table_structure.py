#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Check S_ERP_SYSTEM Table Structure
فحص بنية جدول S_ERP_SYSTEM
"""

def check_table_structure():
    """فحص بنية جدول S_ERP_SYSTEM"""
    print("🔄 فحص بنية جدول S_ERP_SYSTEM...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        success = db.connect(
            username="ship2025",
            password="ys123",
            host="localhost",
            port=1521,
            service_name="orcl"
        )
        
        if not success:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # فحص بنية الجدول
        structure_query = """
        SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'S_ERP_SYSTEM'
        ORDER BY COLUMN_ID
        """
        
        columns = db.execute_query(structure_query)
        if columns:
            print(f"\n📋 بنية جدول S_ERP_SYSTEM ({len(columns)} عمود):")
            print("-" * 60)
            for col in columns:
                nullable = "NULL" if col['NULLABLE'] == 'Y' else "NOT NULL"
                print(f"{col['COLUMN_NAME']:<20} {col['DATA_TYPE']:<15} {col['DATA_LENGTH']:<10} {nullable}")
        
        # عرض عينة من البيانات
        sample_query = "SELECT * FROM S_ERP_SYSTEM WHERE ROWNUM <= 3"
        sample_data = db.execute_query(sample_query)
        
        if sample_data:
            print(f"\n📊 عينة من البيانات:")
            print("-" * 60)
            for i, row in enumerate(sample_data):
                print(f"السجل {i+1}:")
                for key, value in row.items():
                    print(f"  {key}: {value}")
                print()
        
        db.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    check_table_structure()
