#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Complete System Test
اختبار شامل للنظام الكامل
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔄 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        if db.connect():
            print("✅ الاتصال بقاعدة البيانات ناجح")
            
            # اختبار استعلام بسيط
            result = db.execute_query("SELECT COUNT(*) as COUNT FROM S_ERP_SYSTEM")
            if result:
                count = result[0]['COUNT']
                print(f"📊 عدد الأنظمة في قاعدة البيانات: {count}")
            
            db.disconnect()
            return True
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_systems_manager():
    """اختبار مدير الأنظمة"""
    print("\n🔄 اختبار مدير الأنظمة...")
    
    try:
        from erp_systems_manager import ERPSystemsManager
        
        manager = ERPSystemsManager()
        
        if manager.connect_to_database():
            print("✅ اتصال مدير الأنظمة ناجح")
            
            # تحميل البيانات
            systems_data = manager.load_systems_data()
            print(f"📋 تم تحميل {len(systems_data)} نظام")
            
            # بناء الشجرة
            tree_data = manager.build_systems_tree()
            root_count = len(tree_data.get('root_systems', []))
            print(f"🌳 عدد الأنظمة الجذر: {root_count}")
            
            manager.disconnect()
            return True, systems_data, tree_data
        else:
            print("❌ فشل اتصال مدير الأنظمة")
            return False, [], {}
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الأنظمة: {e}")
        return False, [], {}

def test_systems_tree_widget(systems_data, tree_data):
    """اختبار مكون شجرة الأنظمة"""
    print("\n🔄 اختبار مكون شجرة الأنظمة...")
    
    try:
        from systems_tree_widget import SystemsTreeWidget
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء مكون الشجرة
        tree_widget = SystemsTreeWidget()
        tree_widget.setWindowTitle("اختبار شجرة الأنظمة - SHP ERP")
        tree_widget.resize(1000, 700)
        
        # تحميل البيانات
        tree_widget.load_systems_data(systems_data, tree_data)
        
        # عرض النافذة
        tree_widget.show()
        
        print("✅ مكون شجرة الأنظمة يعمل بنجاح")
        print(f"📊 تم عرض {len(systems_data)} نظام في الشجرة")
        
        return True, tree_widget
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكون الشجرة: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_main_window():
    """اختبار الواجهة الرئيسية"""
    print("\n🔄 اختبار الواجهة الرئيسية...")
    
    try:
        from main_window import SHPERPMainWindow
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        main_window = SHPERPMainWindow()
        main_window.show()
        
        print("✅ الواجهة الرئيسية تعمل بنجاح")
        
        return True, main_window
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرئيسية: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("🚢 SHP ERP - اختبار شامل للنظام الكامل")
    print("=" * 60)
    
    # اختبار قاعدة البيانات
    db_success = test_database_connection()
    
    if not db_success:
        print("\n❌ فشل اختبار قاعدة البيانات - توقف الاختبار")
        return
    
    # اختبار مدير الأنظمة
    manager_success, systems_data, tree_data = test_systems_manager()
    
    if not manager_success:
        print("\n❌ فشل اختبار مدير الأنظمة - توقف الاختبار")
        return
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # اختبار مكون الشجرة
    tree_success, tree_widget = test_systems_tree_widget(systems_data, tree_data)
    
    if tree_success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام")
        
        # عرض رسالة نجاح
        msg = QMessageBox()
        msg.setWindowTitle("نجح الاختبار")
        msg.setText("تم اختبار النظام بنجاح!")
        msg.setInformativeText(f"تم تحميل {len(systems_data)} نظام من قاعدة البيانات Oracle")
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setLayoutDirection(Qt.RightToLeft)
        
        # عرض الرسالة
        msg.show()
        
        print("\n🖥️ النوافذ مفتوحة - اضغط Ctrl+C للإغلاق")
        
        # تشغيل التطبيق
        try:
            sys.exit(app.exec())
        except KeyboardInterrupt:
            print("\n👋 تم إغلاق النظام")
    else:
        print("\n❌ فشل في اختبار مكون الشجرة")

if __name__ == "__main__":
    main()
