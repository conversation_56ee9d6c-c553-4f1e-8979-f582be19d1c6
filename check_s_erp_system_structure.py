#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية جدول S_ERP_SYSTEM
"""

import sys
from oracle_db_manager import OracleDBManager

def check_table_structure():
    """فحص بنية جدول S_ERP_SYSTEM"""
    try:
        # إنشاء مدير قاعدة البيانات
        db = OracleDBManager()
        
        # الاتصال بقاعدة البيانات
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # فحص بنية الجدول
        print("\n🔍 فحص بنية جدول S_ERP_SYSTEM:")
        
        structure_query = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            DATA_LENGTH,
            NULLABLE,
            DATA_DEFAULT
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = 'S_ERP_SYSTEM'
        ORDER BY COLUMN_ID
        """
        
        columns = db.execute_query(structure_query)
        
        if columns:
            print(f"📋 الجدول يحتوي على {len(columns)} عمود:")
            print("-" * 80)
            print(f"{'اسم العمود':<20} {'النوع':<15} {'الطول':<10} {'NULL':<8} {'افتراضي':<15}")
            print("-" * 80)
            
            for col in columns:
                column_name = col.get('COLUMN_NAME', '')
                data_type = col.get('DATA_TYPE', '')
                data_length = str(col.get('DATA_LENGTH', ''))
                nullable = col.get('NULLABLE', '')
                default_val = str(col.get('DATA_DEFAULT', '') or '')
                
                print(f"{column_name:<20} {data_type:<15} {data_length:<10} {nullable:<8} {default_val:<15}")
        else:
            print("❌ لم يتم العثور على الجدول أو لا يحتوي على أعمدة")
        
        # فحص البيانات الموجودة
        print(f"\n📊 فحص البيانات الموجودة:")
        
        data_query = "SELECT COUNT(*) as TOTAL_RECORDS FROM S_ERP_SYSTEM"
        result = db.execute_query(data_query)
        
        if result:
            total_records = result[0].get('TOTAL_RECORDS', 0)
            print(f"📈 إجمالي السجلات: {total_records}")
        
        # عرض عينة من البيانات
        if result and result[0].get('TOTAL_RECORDS', 0) > 0:
            print(f"\n📋 عينة من البيانات (أول 10 سجلات):")
            
            sample_query = """
            SELECT * FROM (
                SELECT * FROM S_ERP_SYSTEM 
                ORDER BY ROWNUM
            ) WHERE ROWNUM <= 10
            """
            
            sample_data = db.execute_query(sample_query)
            
            if sample_data:
                # عرض أسماء الأعمدة
                if sample_data:
                    column_names = list(sample_data[0].keys())
                    print("الأعمدة الموجودة:", ", ".join(column_names))
                    
                    print("\nالبيانات:")
                    for i, record in enumerate(sample_data, 1):
                        print(f"\nسجل {i}:")
                        for key, value in record.items():
                            print(f"  {key}: {value}")
        
        # قطع الاتصال
        db.disconnect()
        print("\n✅ تم قطع الاتصال")
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🔍 SHP ERP - فحص بنية جدول S_ERP_SYSTEM")
    print("=" * 60)
    
    check_table_structure()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
