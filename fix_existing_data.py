#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح البيانات المدرجة خطأ
"""

import cx_Oracle

def main():
    """إصلاح البيانات"""
    try:
        print("🔄 الاتصال بقاعدة البيانات...")
        conn = cx_Oracle.connect('ship2025', 'ys123', 'localhost:1521/orcl')
        cursor = conn.cursor()
        print("✅ تم الاتصال")
        
        # فحص البيانات الخاطئة
        print("\n🔍 فحص البيانات الخاطئة:")
        cursor.execute("""
            SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, FORM_NO
            FROM S_ERP_SYSTEM 
            WHERE SYS_NO >= 207
            ORDER BY SYS_NO
        """)
        
        wrong_data = cursor.fetchall()
        print(f"📊 تم العثور على {len(wrong_data)} سجل خاطئ:")
        
        for row in wrong_data:
            print(f"   • {row[0]}: {row[1]} - NAME: {row[2]} - ORDR: {row[4]}")
        
        # حذف البيانات الخاطئة
        print(f"\n🗑️ حذف البيانات الخاطئة...")
        cursor.execute("DELETE FROM S_ERP_SYSTEM WHERE SYS_NO >= 207")
        deleted_count = cursor.rowcount
        print(f"✅ تم حذف {deleted_count} سجل")
        
        # إدراج البيانات الصحيحة
        print(f"\n✅ إدراج البيانات الصحيحة:")
        
        # النظام الرئيسي
        cursor.execute("""
            INSERT INTO S_ERP_SYSTEM (SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE, FORM_NO)
            VALUES (207, 'GENST', 'نظام الإعدادات العامة الشامل', 1, 207, 0, NULL)
        """)
        print("   ✅ النظام الرئيسي: نظام الإعدادات العامة الشامل")
        
        # الأنظمة الفرعية
        subsystems = [
            (208, 'APPR', 'إعدادات المظهر', 1),
            (209, 'COMP', 'بيانات الشركة', 2),
            (210, 'CURR', 'إعدادات العملات', 3),
            (211, 'FISC', 'السنة المالية', 4),
            (212, 'LANG', 'إعدادات اللغة', 5),
            (213, 'SECR', 'إعدادات الأمان', 6),
            (214, 'MAIL', 'إعدادات البريد الإلكتروني', 7),
            (215, 'PRNT', 'إعدادات الطباعة', 8),
            (216, 'REPT', 'إعدادات التقارير', 9),
            (217, 'BKUP', 'إعدادات النسخ الاحتياطي', 10),
            (218, 'SYST', 'إعدادات النظام', 11),
            (219, 'NOTF', 'إعدادات الإشعارات', 12)
        ]
        
        for sys_no, sys_code, sys_name, order_no in subsystems:
            cursor.execute("""
                INSERT INTO S_ERP_SYSTEM (SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE, FORM_NO)
                VALUES (:1, :2, :3, :4, :5, :6, :7)
            """, (sys_no, sys_code, sys_name, 207, order_no, 0, None))
            
            print(f"   ✅ {order_no:2d}. {sys_name} [{sys_code}]")
        
        # حفظ التغييرات
        conn.commit()
        print(f"\n💾 تم حفظ جميع التغييرات")
        
        # التحقق من الإدراج الصحيح
        print(f"\n🔍 التحقق من الإدراج الصحيح:")
        cursor.execute("""
            SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO
            FROM S_ERP_SYSTEM 
            WHERE SYS_NO >= 207
            ORDER BY SYS_NO
        """)
        
        correct_data = cursor.fetchall()
        print(f"✅ تم إدراج {len(correct_data)} نظام بشكل صحيح:")
        
        for row in correct_data:
            sys_no, sys_code, sys_name, sys_parnt, ordr_no = row
            if sys_parnt == 207:
                print(f"   📂 {sys_no}: {sys_name} [{sys_code}] (فرعي - ترتيب: {ordr_no})")
            else:
                print(f"   🏗️ {sys_no}: {sys_name} [{sys_code}] (رئيسي - ترتيب: {ordr_no})")
        
        cursor.close()
        conn.close()
        print(f"\n✅ تم الانتهاء بنجاح")
        
        print(f"\n🎉 تم إصلاح وإدراج نظام الإعدادات العامة بنجاح!")
        print(f"🚀 النظام متاح الآن في شجرة الأنظمة")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
