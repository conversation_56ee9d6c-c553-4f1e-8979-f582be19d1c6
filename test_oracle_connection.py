#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Oracle Connection Test
اختبار الاتصال بقاعدة بيانات Oracle مع تجربة بيانات مختلفة
"""

import cx_Oracle
import sys

def test_connection_variations():
    """اختبار الاتصال بتنسيقات مختلفة"""
    
    # قائمة التنسيقات المختلفة للاختبار
    test_configs = [
        {
            "username": "SHIP2025",
            "password": "ys123@yemensoft",
            "host": "localhost",
            "port": 1521,
            "service_name": "orcl"
        },
        {
            "username": "ship2025",
            "password": "ys123@yemensoft", 
            "host": "localhost",
            "port": 1521,
            "service_name": "orcl"
        },
        {
            "username": "SHIP2025",
            "password": "ys123@yemensoft",
            "host": "localhost", 
            "port": 1521,
            "service_name": "OR<PERSON>"
        },
        {
            "username": "SHIP2025",
            "password": "ys123@yemensoft",
            "host": "127.0.0.1",
            "port": 1521,
            "service_name": "orcl"
        }
    ]
    
    print("🔄 اختبار الاتصال بقاعدة بيانات Oracle...")
    print("=" * 60)
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n🧪 اختبار التكوين {i}:")
        print(f"   المستخدم: {config['username']}")
        print(f"   الخادم: {config['host']}:{config['port']}")
        print(f"   الخدمة: {config['service_name']}")
        
        try:
            # إنشاء DSN
            dsn = cx_Oracle.makedsn(
                config['host'], 
                config['port'], 
                service_name=config['service_name']
            )
            
            print(f"   DSN: {dsn}")
            
            # محاولة الاتصال
            connection = cx_Oracle.connect(
                user=config['username'],
                password=config['password'],
                dsn=dsn,
                encoding="UTF-8"
            )
            
            print("   ✅ نجح الاتصال!")
            
            # اختبار استعلام بسيط
            cursor = connection.cursor()
            cursor.execute("SELECT SYSDATE FROM DUAL")
            result = cursor.fetchone()
            print(f"   📅 التاريخ الحالي: {result[0]}")
            
            # إغلاق الاتصال
            cursor.close()
            connection.close()
            
            print(f"   🎉 التكوين {i} يعمل بنجاح!")
            return config
            
        except cx_Oracle.Error as e:
            error_obj, = e.args
            print(f"   ❌ فشل: {error_obj.message}")
            
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {e}")
    
    print("\n❌ جميع التكوينات فشلت!")
    return None

def test_manual_connection():
    """اختبار الاتصال بإدخال يدوي"""
    print("\n🔧 اختبار الاتصال اليدوي:")
    print("=" * 40)
    
    try:
        username = input("اسم المستخدم: ").strip()
        password = input("كلمة المرور: ").strip()
        host = input("الخادم (افتراضي: localhost): ").strip() or "localhost"
        port = input("المنفذ (افتراضي: 1521): ").strip() or "1521"
        service_name = input("اسم الخدمة (افتراضي: orcl): ").strip() or "orcl"
        
        port = int(port)
        
        print(f"\n🔗 محاولة الاتصال بـ: {username}@{host}:{port}/{service_name}")
        
        # إنشاء DSN
        dsn = cx_Oracle.makedsn(host, port, service_name=service_name)
        print(f"📡 DSN: {dsn}")
        
        # محاولة الاتصال
        connection = cx_Oracle.connect(
            user=username,
            password=password,
            dsn=dsn,
            encoding="UTF-8"
        )
        
        print("✅ نجح الاتصال!")
        
        # اختبار استعلام
        cursor = connection.cursor()
        cursor.execute("SELECT SYSDATE FROM DUAL")
        result = cursor.fetchone()
        print(f"📅 التاريخ الحالي: {result[0]}")
        
        # إغلاق الاتصال
        cursor.close()
        connection.close()
        
        return {
            "username": username,
            "password": password,
            "host": host,
            "port": port,
            "service_name": service_name
        }
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار الاتصال بقاعدة بيانات Oracle")
    print("=" * 60)
    
    # اختبار التكوينات المحددة مسبقاً
    working_config = test_connection_variations()
    
    if working_config:
        print(f"\n🎉 تم العثور على تكوين يعمل!")
        print("يمكنك استخدام هذا التكوين في النظام.")
    else:
        print("\n🔧 لم تنجح التكوينات المحددة مسبقاً.")
        
        choice = input("\nهل تريد تجربة الإدخال اليدوي؟ (y/n): ").strip().lower()
        if choice in ['y', 'yes', 'نعم']:
            working_config = test_manual_connection()
            
            if working_config:
                print(f"\n🎉 نجح الاتصال اليدوي!")
                print("يمكنك استخدام هذا التكوين في النظام.")

if __name__ == "__main__":
    main()
