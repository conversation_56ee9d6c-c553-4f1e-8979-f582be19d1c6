#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Add Database Administration System to Main Systems Tree
إضافة نظام إدارة قاعدة البيانات إلى شجرة الأنظمة الرئيسية
"""

import sys
from datetime import datetime

def add_database_admin_system():
    """إضافة نظام إدارة قاعدة البيانات إلى جدول S_ERP_SYSTEM"""
    print("🔄 إضافة نظام إدارة قاعدة البيانات إلى الأنظمة الرئيسية...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        
        # الاتصال بقاعدة البيانات
        success = db.connect(
            username="ship2025",
            password="ys123",
            host="localhost",
            port=1521,
            service_name="orcl"
        )
        
        if not success:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # التحقق من وجود النظام مسبقاً
        check_query = """
        SELECT COUNT(*) as COUNT 
        FROM S_ERP_SYSTEM 
        WHERE SYS_CODE = 'DB_ADMIN' OR SYS_NAME LIKE '%إدارة قاعدة البيانات%'
        """
        
        result = db.execute_query(check_query)
        if result and result[0]['COUNT'] > 0:
            print("⚠️ نظام إدارة قاعدة البيانات موجود مسبقاً")
            
            # عرض النظام الموجود
            existing_query = """
            SELECT SYS_NO, SYS_CODE, SYS_NAME 
            FROM S_ERP_SYSTEM 
            WHERE SYS_CODE = 'DB_ADMIN' OR SYS_NAME LIKE '%إدارة قاعدة البيانات%'
            """
            existing_systems = db.execute_query(existing_query)
            if existing_systems:
                for system in existing_systems:
                    print(f"  📋 {system['SYS_NO']}: {system['SYS_NAME']} [{system['SYS_CODE']}]")
            
            db.disconnect()
            return True
        
        # الحصول على أعلى رقم نظام
        max_query = "SELECT NVL(MAX(SYS_NO), 0) as MAX_NO FROM S_ERP_SYSTEM"
        max_result = db.execute_query(max_query)
        next_sys_no = (max_result[0]['MAX_NO'] if max_result else 0) + 1
        
        print(f"📊 رقم النظام الجديد: {next_sys_no}")
        
        # إدراج نظام إدارة قاعدة البيانات
        insert_query = """
        INSERT INTO S_ERP_SYSTEM (
            SYS_NO,
            SYS_CODE,
            SYS_NAME,
            SYS_PARNT,
            SYS_LEVEL,
            SYS_ORDER,
            SYS_ACTIVE,
            SYS_ICON,
            SYS_DESC,
            SYS_MODULE,
            SYS_PATH,
            CREATED_DATE,
            CREATED_BY
        ) VALUES (
            :sys_no,
            :sys_code,
            :sys_name,
            :sys_parent,
            :sys_level,
            :sys_order,
            :sys_active,
            :sys_icon,
            :sys_desc,
            :sys_module,
            :sys_path,
            SYSDATE,
            :created_by
        )
        """
        
        # بيانات النظام الجديد
        system_data = {
            'sys_no': next_sys_no,
            'sys_code': 'DB_ADMIN',
            'sys_name': 'نظام إدارة قاعدة البيانات Oracle',
            'sys_parent': 1,  # تحت النظام الجذر (ERP)
            'sys_level': 2,
            'sys_order': 999,  # في النهاية
            'sys_active': 'Y',
            'sys_icon': '🗃️',
            'sys_desc': 'نظام شامل لإدارة قواعد البيانات المتعددة مع نسخ احتياطي ومحرر SQL متقدم',
            'sys_module': 'DATABASE_ADMIN',
            'sys_path': 'run_database_admin.py',
            'created_by': 'SYSTEM'
        }
        
        # تنفيذ الإدراج
        cursor = db.connection.cursor()
        cursor.execute(insert_query, system_data)
        db.connection.commit()
        cursor.close()
        
        print("✅ تم إدراج نظام إدارة قاعدة البيانات بنجاح")
        print(f"📋 رقم النظام: {next_sys_no}")
        print(f"🏷️ رمز النظام: {system_data['sys_code']}")
        print(f"📝 اسم النظام: {system_data['sys_name']}")
        
        # إضافة الأنظمة الفرعية
        subsystems = [
            {
                'code': 'DB_CONN',
                'name': 'إدارة الاتصالات',
                'desc': 'إدارة اتصالات قواعد البيانات المتعددة',
                'icon': '🔗',
                'path': 'database_connection_dialog.py',
                'order': 10
            },
            {
                'code': 'SQL_EDITOR',
                'name': 'محرر SQL المتقدم',
                'desc': 'محرر SQL مع تلوين الصيغة والإكمال التلقائي',
                'icon': '📝',
                'path': 'advanced_sql_editor.py',
                'order': 20
            },
            {
                'code': 'DB_BACKUP',
                'name': 'النسخ الاحتياطي',
                'desc': 'نظام النسخ الاحتياطي المتقدم مع جدولة',
                'icon': '💾',
                'path': 'advanced_backup_system.py',
                'order': 30
            },
            {
                'code': 'DB_STATS',
                'name': 'إحصائيات قاعدة البيانات',
                'desc': 'إحصائيات وتقارير شاملة لقاعدة البيانات',
                'icon': '📊',
                'path': 'database_admin_window.py?tab=stats',
                'order': 40
            }
        ]
        
        print("\n🔄 إضافة الأنظمة الفرعية...")
        
        for i, subsystem in enumerate(subsystems):
            sub_sys_no = next_sys_no + i + 1
            
            sub_system_data = {
                'sys_no': sub_sys_no,
                'sys_code': subsystem['code'],
                'sys_name': subsystem['name'],
                'sys_parent': next_sys_no,  # تحت نظام إدارة قاعدة البيانات
                'sys_level': 3,
                'sys_order': subsystem['order'],
                'sys_active': 'Y',
                'sys_icon': subsystem['icon'],
                'sys_desc': subsystem['desc'],
                'sys_module': 'DATABASE_ADMIN',
                'sys_path': subsystem['path'],
                'created_by': 'SYSTEM'
            }
            
            cursor = db.connection.cursor()
            cursor.execute(insert_query, sub_system_data)
            db.connection.commit()
            cursor.close()
            
            print(f"  ✅ {subsystem['icon']} {subsystem['name']} - رقم {sub_sys_no}")
        
        print(f"\n🎉 تم إضافة نظام إدارة قاعدة البيانات مع {len(subsystems)} نظام فرعي")
        
        # التحقق من الإدراج
        verify_query = """
        SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, SYS_LEVEL, SYS_ICON
        FROM S_ERP_SYSTEM 
        WHERE SYS_NO >= :start_no
        ORDER BY SYS_NO
        """
        
        verification = db.execute_query(verify_query, {'start_no': next_sys_no})
        if verification:
            print("\n📋 الأنظمة المضافة:")
            for system in verification:
                level_indent = "  " * (system['SYS_LEVEL'] - 2)
                parent_info = f" (تحت {system['SYS_PARNT']})" if system['SYS_PARNT'] != 1 else ""
                print(f"{level_indent}{system['SYS_ICON']} {system['SYS_NO']}: {system['SYS_NAME']} [{system['SYS_CODE']}]{parent_info}")
        
        db.disconnect()
        print("\n✅ تم إنجاز العملية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة النظام: {e}")
        return False

def update_main_window_integration():
    """تحديث النافذة الرئيسية لدعم تشغيل نظام إدارة قاعدة البيانات"""
    print("\n🔄 تحديث النافذة الرئيسية لدعم نظام إدارة قاعدة البيانات...")
    
    try:
        # قراءة ملف النافذة الرئيسية
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود معالج النظام
        if 'def launch_database_admin' in content:
            print("✅ معالج نظام إدارة قاعدة البيانات موجود مسبقاً")
            return True
        
        # إضافة معالج تشغيل نظام إدارة قاعدة البيانات
        handler_code = '''
    def launch_database_admin(self, system_data=None):
        """تشغيل نظام إدارة قاعدة البيانات"""
        try:
            import subprocess
            import os
            
            # تحديد الملف المراد تشغيله
            if system_data:
                sys_path = system_data.get('SYS_PATH', 'run_database_admin.py')
            else:
                sys_path = 'run_database_admin.py'
            
            # التحقق من وجود الملف
            if not os.path.exists(sys_path):
                self.statusBar().showMessage(
                    self.format_arabic_text(f"⚠️ الملف غير موجود: {sys_path}")
                )
                return
            
            # تشغيل النظام
            subprocess.Popen([sys.executable, sys_path], 
                           cwd=os.getcwd(),
                           creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
            
            self.statusBar().showMessage(
                self.format_arabic_text("🚀 تم تشغيل نظام إدارة قاعدة البيانات")
            )
            
        except Exception as e:
            self.statusBar().showMessage(
                self.format_arabic_text(f"❌ خطأ في تشغيل النظام: {str(e)}")
            )
'''
        
        # البحث عن مكان مناسب لإضافة المعالج
        if 'def on_system_selected' in content:
            # إضافة المعالج قبل on_system_selected
            insertion_point = content.find('    def on_system_selected')
            if insertion_point != -1:
                new_content = content[:insertion_point] + handler_code + '\n' + content[insertion_point:]
                
                # كتابة الملف المحدث
                with open('main_window.py', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ تم إضافة معالج نظام إدارة قاعدة البيانات")
                return True
        
        print("⚠️ لم يتم العثور على مكان مناسب لإضافة المعالج")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تحديث النافذة الرئيسية: {e}")
        return False

def test_system_integration():
    """اختبار تكامل النظام مع الشجرة الرئيسية"""
    print("\n🔄 اختبار تكامل النظام مع الشجرة الرئيسية...")
    
    try:
        from database_connection import get_db_connection
        
        db = get_db_connection()
        success = db.connect(
            username="ship2025",
            password="ys123",
            host="localhost",
            port=1521,
            service_name="orcl"
        )
        
        if not success:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # البحث عن نظام إدارة قاعدة البيانات
        search_query = """
        SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, SYS_LEVEL, SYS_ICON, SYS_PATH
        FROM S_ERP_SYSTEM 
        WHERE SYS_CODE LIKE 'DB_%' OR SYS_NAME LIKE '%قاعدة البيانات%'
        ORDER BY SYS_NO
        """
        
        systems = db.execute_query(search_query)
        if systems:
            print(f"✅ تم العثور على {len(systems)} نظام متعلق بقاعدة البيانات:")
            for system in systems:
                level_indent = "  " * (system['SYS_LEVEL'] - 1)
                print(f"{level_indent}{system['SYS_ICON']} {system['SYS_NO']}: {system['SYS_NAME']} [{system['SYS_CODE']}]")
                if system['SYS_PATH']:
                    print(f"{level_indent}   📁 المسار: {system['SYS_PATH']}")
        else:
            print("⚠️ لم يتم العثور على أنظمة قاعدة البيانات")
        
        db.disconnect()
        return len(systems) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - إضافة نظام إدارة قاعدة البيانات إلى الأنظمة الرئيسية")
    print("=" * 80)
    
    tasks = [
        ("إضافة نظام إدارة قاعدة البيانات", add_database_admin_system),
        ("تحديث النافذة الرئيسية", update_main_window_integration),
        ("اختبار التكامل", test_system_integration)
    ]
    
    results = {}
    
    for task_name, task_func in tasks:
        print(f"\n{'='*10} {task_name} {'='*10}")
        try:
            results[task_name] = task_func()
        except Exception as e:
            print(f"❌ خطأ في {task_name}: {e}")
            results[task_name] = False
    
    # ملخص النتائج
    print("\n" + "="*80)
    print("📊 ملخص النتائج:")
    print("="*80)
    
    passed = 0
    total = len(tasks)
    
    for task_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {task_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} مهمة نجحت")
    
    if passed == total:
        print("\n🎉 تم دمج نظام إدارة قاعدة البيانات بنجاح!")
        print("✅ النظام متاح الآن في شجرة الأنظمة الرئيسية")
        print("\n📋 للوصول للنظام:")
        print("1️⃣ شغل التطبيق الرئيسي: python main_window.py")
        print("2️⃣ ابحث عن 🗃️ نظام إدارة قاعدة البيانات Oracle")
        print("3️⃣ انقر على النظام أو أحد الأنظمة الفرعية")
        
        print("\n🔧 الأنظمة الفرعية المتاحة:")
        print("  🔗 إدارة الاتصالات")
        print("  📝 محرر SQL المتقدم")
        print("  💾 النسخ الاحتياطي")
        print("  📊 إحصائيات قاعدة البيانات")
    else:
        print(f"\n⚠️ {total - passed} مهمة فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("="*80)

if __name__ == "__main__":
    main()
