#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Enhanced Systems Manager
مدير الأنظمة المحسن مع إدارة قاعدة البيانات المتقدمة
"""

import threading
import time
from typing import List, Dict, Any, Optional, Callable
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from oracle_db_manager import get_db_manager

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class DatabaseConnectionThread(QThread):
    """خيط الاتصال بقاعدة البيانات المحسن"""
    
    # الإشارات
    connection_status = Signal(bool, str)  # حالة الاتصال
    progress_update = Signal(str)  # تحديث التقدم
    systems_loaded = Signal(list)  # الأنظمة المحملة
    error_occurred = Signal(str)  # خطأ حدث
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = get_db_manager()
        self.should_stop = False
        
    def run(self):
        """تشغيل عملية الاتصال والتحميل"""
        try:
            self.progress_update.emit("بدء الاتصال بقاعدة البيانات...")
            
            # اختبار الاتصال أولاً
            success, message = self.db_manager.test_connection()
            if not success:
                self.connection_status.emit(False, message)
                return
            
            self.progress_update.emit("نجح الاتصال، جاري تهيئة مجموعة الاتصالات...")
            
            # تهيئة مجموعة الاتصالات
            if not self.db_manager.initialize_connection_pool():
                self.connection_status.emit(False, "فشل في تهيئة مجموعة الاتصالات")
                return
            
            self.connection_status.emit(True, "تم الاتصال بنجاح")
            self.progress_update.emit("جاري تحميل بيانات الأنظمة...")
            
            # تحميل بيانات الأنظمة
            success, systems_data = self.db_manager.get_systems_data()
            if success:
                self.systems_loaded.emit(systems_data)
                self.progress_update.emit(f"تم تحميل {len(systems_data)} نظام بنجاح")
            else:
                self.error_occurred.emit(f"فشل في تحميل الأنظمة: {systems_data}")
                
        except Exception as e:
            self.error_occurred.emit(f"خطأ غير متوقع: {str(e)}")
    
    def stop(self):
        """إيقاف العملية"""
        self.should_stop = True
        self.quit()
        self.wait()

class EnhancedSystemsManager(QObject):
    """مدير الأنظمة المحسن"""
    
    # الإشارات
    connection_status_changed = Signal(bool, str)
    systems_data_updated = Signal(list)
    progress_updated = Signal(str)
    error_occurred = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = get_db_manager()
        self.systems_data = []
        self.systems_tree = {}
        self.connection_thread = None
        self.is_connected = False
        
        # مؤقت لمراقبة الاتصال
        self.connection_monitor = QTimer()
        self.connection_monitor.timeout.connect(self.check_connection_health)
        self.connection_monitor.setInterval(30000)  # كل 30 ثانية
        
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي"""
        if not ARABIC_SUPPORT or not text:
            return text
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text, base_dir='R')
        except:
            return text
    
    def connect_to_database_async(self):
        """الاتصال بقاعدة البيانات بشكل غير متزامن"""
        if self.connection_thread and self.connection_thread.isRunning():
            return
        
        self.connection_thread = DatabaseConnectionThread()
        self.connection_thread.connection_status.connect(self.on_connection_status)
        self.connection_thread.progress_update.connect(self.on_progress_update)
        self.connection_thread.systems_loaded.connect(self.on_systems_loaded)
        self.connection_thread.error_occurred.connect(self.on_error_occurred)
        
        self.connection_thread.start()
    
    def on_connection_status(self, success: bool, message: str):
        """معالج حالة الاتصال"""
        self.is_connected = success
        self.connection_status_changed.emit(success, message)
        
        if success:
            self.connection_monitor.start()
        else:
            self.connection_monitor.stop()
    
    def on_progress_update(self, message: str):
        """معالج تحديث التقدم"""
        self.progress_updated.emit(message)
    
    def on_systems_loaded(self, systems_data: List[Dict]):
        """معالج تحميل الأنظمة"""
        self.systems_data = systems_data
        self.build_systems_tree()
        self.systems_data_updated.emit(systems_data)
    
    def on_error_occurred(self, error_message: str):
        """معالج الأخطاء"""
        self.error_occurred.emit(error_message)
    
    def build_systems_tree(self):
        """بناء شجرة الأنظمة"""
        if not self.systems_data:
            return
        
        # تجميع الأنظمة حسب الأب
        systems_by_parent = {}
        root_systems = []
        
        for system in self.systems_data:
            parent_id = system.get('PARENT_SYSTEM_ID')
            if not parent_id or parent_id == 0:
                root_systems.append(system)
            else:
                if parent_id not in systems_by_parent:
                    systems_by_parent[parent_id] = []
                systems_by_parent[parent_id].append(system)
        
        # ترتيب الأنظمة
        root_systems.sort(key=lambda x: x.get('SYSTEM_ORDER', 0) or 0)
        for children in systems_by_parent.values():
            children.sort(key=lambda x: x.get('SYSTEM_ORDER', 0) or 0)
        
        self.systems_tree = {
            'root_systems': root_systems,
            'systems_by_parent': systems_by_parent
        }
    
    def get_child_systems(self, parent_id: int) -> List[Dict]:
        """الحصول على الأنظمة الفرعية"""
        if not self.systems_tree:
            return []
        return self.systems_tree.get('systems_by_parent', {}).get(parent_id, [])
    
    def get_root_systems(self) -> List[Dict]:
        """الحصول على الأنظمة الجذر"""
        if not self.systems_tree:
            return []
        return self.systems_tree.get('root_systems', [])
    
    def get_system_by_id(self, system_id: int) -> Optional[Dict]:
        """الحصول على نظام بواسطة المعرف"""
        for system in self.systems_data:
            if system.get('SYSTEM_ID') == system_id:
                return system
        return None
    
    def refresh_systems_data(self):
        """تحديث بيانات الأنظمة"""
        if not self.is_connected:
            self.error_occurred.emit("لا يوجد اتصال بقاعدة البيانات")
            return
        
        self.progress_updated.emit("جاري تحديث بيانات الأنظمة...")
        
        success, systems_data = self.db_manager.get_systems_data()
        if success:
            self.systems_data = systems_data
            self.build_systems_tree()
            self.systems_data_updated.emit(systems_data)
            self.progress_updated.emit(f"تم تحديث {len(systems_data)} نظام")
        else:
            self.error_occurred.emit(f"فشل في تحديث الأنظمة: {systems_data}")
    
    def check_connection_health(self):
        """فحص صحة الاتصال"""
        if not self.is_connected:
            return
        
        success, message = self.db_manager.test_connection()
        if not success:
            self.is_connected = False
            self.connection_monitor.stop()
            self.connection_status_changed.emit(False, f"فقد الاتصال: {message}")
    
    def get_table_info(self, table_name: str = "S_ERP_SYSTEM") -> Dict:
        """الحصول على معلومات الجدول"""
        if not self.is_connected:
            return {"error": "لا يوجد اتصال بقاعدة البيانات"}
        
        success, info = self.db_manager.get_table_info(table_name)
        return info if success else {"error": info}
    
    def execute_custom_query(self, query: str, params: tuple = None) -> tuple[bool, Any]:
        """تنفيذ استعلام مخصص"""
        if not self.is_connected:
            return False, "لا يوجد اتصال بقاعدة البيانات"
        
        return self.db_manager.execute_query_safe(query, params)
    
    def disconnect(self):
        """قطع الاتصال"""
        if self.connection_thread and self.connection_thread.isRunning():
            self.connection_thread.stop()
        
        self.connection_monitor.stop()
        self.db_manager.close_connections()
        self.is_connected = False
        self.connection_status_changed.emit(False, "تم قطع الاتصال")
    
    def get_connection_stats(self) -> Dict:
        """الحصول على إحصائيات الاتصال"""
        return {
            'is_connected': self.is_connected,
            'systems_count': len(self.systems_data),
            'root_systems_count': len(self.get_root_systems()),
            'has_connection_pool': self.db_manager.connection_pool is not None
        }
