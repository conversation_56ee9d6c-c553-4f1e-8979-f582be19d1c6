#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق النهائي من قاعدة البيانات
"""

import cx_Oracle

def main():
    """التحقق النهائي"""
    try:
        print("🚢 SHP ERP - التحقق النهائي من قاعدة البيانات")
        print("=" * 60)
        
        # الاتصال
        conn = cx_Oracle.connect('ship2025', 'ys123', 'localhost:1521/orcl')
        cursor = conn.cursor()
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # البحث عن نظام الإعدادات العامة
        print("\n🔍 البحث عن نظام الإعدادات العامة:")
        cursor.execute("""
            SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO
            FROM S_ERP_SYSTEM 
            WHERE SYS_CODE = 'GENST' OR SYS_PARNT IN (
                SELECT SYS_NO FROM S_ERP_SYSTEM WHERE SYS_CODE = 'GENST'
            )
            ORDER BY SYS_NO
        """)
        
        systems = cursor.fetchall()
        
        if systems:
            print(f"✅ تم العثور على {len(systems)} نظام:")
            
            main_system = None
            subsystems = []
            
            for row in systems:
                sys_no, sys_code, sys_name, sys_parnt, ordr_no = row
                
                if sys_code == 'GENST':
                    main_system = row
                    print(f"   🏗️ {sys_no}: {sys_name} [{sys_code}] (رئيسي)")
                else:
                    subsystems.append(row)
                    print(f"   📂 {sys_no}: {sys_name} [{sys_code}] (فرعي)")
            
            if main_system:
                print(f"\n✅ النظام الرئيسي موجود: {main_system[2]}")
                print(f"📂 عدد الأنظمة الفرعية: {len(subsystems)}")
                
                # اختبار النافذة الرئيسية
                print(f"\n🔄 اختبار تحميل البيانات في النافذة الرئيسية...")
                
                # محاكاة تحميل البيانات
                from erp_systems_manager import ERPSystemsManager
                manager = ERPSystemsManager()
                
                if manager.connect_to_database():
                    print("✅ نجح الاتصال من مدير الأنظمة")
                    
                    systems_data = manager.load_systems_data()
                    print(f"✅ تم تحميل {len(systems_data)} نظام")
                    
                    # البحث عن نظام الإعدادات العامة
                    found_main = False
                    found_subs = 0
                    
                    for system in systems_data:
                        sys_code = system.get('SYSTEM_CODE', '')
                        sys_name = system.get('SYSTEM_NAME', '')
                        
                        if sys_code == 'GENST':
                            found_main = True
                            print(f"   ✅ تم العثور على النظام الرئيسي: {sys_name}")
                        elif sys_code in ['APPR', 'COMP', 'CURR', 'FISC', 'LANG', 'SECR', 
                                         'MAIL', 'PRNT', 'REPT', 'BKUP', 'SYST', 'NOTF']:
                            found_subs += 1
                            print(f"   ✅ نظام فرعي: {sys_name} [{sys_code}]")
                    
                    if found_main and found_subs > 0:
                        print(f"\n🎉 نجح الاختبار!")
                        print(f"✅ النظام الرئيسي موجود")
                        print(f"✅ {found_subs} نظام فرعي موجود")
                        print(f"🚀 النظام جاهز للاستخدام!")
                        
                        print(f"\n📋 للاستخدام:")
                        print(f"1️⃣ شغل: python main_window.py")
                        print(f"2️⃣ ابحث عن: نظام الإعدادات العامة الشامل")
                        print(f"3️⃣ انقر على النظام")
                        print(f"4️⃣ ستفتح نافذة الإعدادات! ✨")
                        
                        return True
                    else:
                        print(f"❌ لم يتم العثور على الأنظمة في مدير الأنظمة")
                        return False
                else:
                    print("❌ فشل الاتصال من مدير الأنظمة")
                    return False
            else:
                print("❌ النظام الرئيسي غير موجود")
                return False
        else:
            print("❌ لم يتم العثور على أي أنظمة إعدادات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            cursor.close()
            conn.close()
            print("\n✅ تم إغلاق الاتصال")
        except:
            pass

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم حل المشكلة الجذرية بنجاح!")
        print("✅ نظام الإعدادات العامة مدرج في قاعدة البيانات")
        print("✅ النظام متاح في شجرة الأنظمة")
        print("🚀 يمكن استخدام النظام الآن!")
    else:
        print("❌ لا تزال هناك مشاكل تحتاج إلى حل")
    print("=" * 60)
