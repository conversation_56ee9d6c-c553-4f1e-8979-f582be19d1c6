#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام رقم 10 (STP) في قاعدة البيانات
"""

import cx_Oracle

def main():
    """فحص النظام STP"""
    try:
        print("🚢 SHP ERP - فحص النظام رقم 10 (STP)")
        print("=" * 60)
        
        # الاتصال
        conn = cx_Oracle.connect('ship2025', 'ys123', 'localhost:1521/orcl')
        cursor = conn.cursor()
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # البحث عن النظام رقم 10
        print("\n🔍 البحث عن النظام رقم 10:")
        cursor.execute("""
            SELECT SYS_NO, SYS_CODE, SYS_NAME, SYS_PARNT, ORDR_NO, INACTIVE
            FROM S_ERP_SYSTEM 
            WHERE SYS_NO = 10
        """)
        
        system_10 = cursor.fetchone()
        
        if system_10:
            sys_no, sys_code, sys_name, sys_parnt, ordr_no, inactive = system_10
            print(f"✅ تم العثور على النظام:")
            print(f"   📊 رقم النظام: {sys_no}")
            print(f"   🔤 رمز النظام: {sys_code}")
            print(f"   📝 اسم النظام: {sys_name}")
            print(f"   👆 النظام الأب: {sys_parnt}")
            print(f"   📋 ترتيب: {ordr_no}")
            print(f"   🔄 حالة: {'نشط' if inactive == 0 else 'غير نشط'}")
            
            # البحث عن الأنظمة الفرعية
            print(f"\n🔍 البحث عن الأنظمة الفرعية للنظام 10:")
            cursor.execute("""
                SELECT SYS_NO, SYS_CODE, SYS_NAME, ORDR_NO, INACTIVE
                FROM S_ERP_SYSTEM 
                WHERE SYS_PARNT = 10
                ORDER BY ORDR_NO, SYS_NO
            """)
            
            subsystems = cursor.fetchall()
            
            if subsystems:
                print(f"✅ تم العثور على {len(subsystems)} نظام فرعي:")
                for sub in subsystems:
                    sub_no, sub_code, sub_name, sub_order, sub_inactive = sub
                    status = 'نشط' if sub_inactive == 0 else 'غير نشط'
                    print(f"   📂 {sub_no}: {sub_name} [{sub_code}] - ترتيب: {sub_order} - {status}")
            else:
                print("⚠️ لا توجد أنظمة فرعية للنظام 10")
            
            return True
            
        else:
            print("❌ لم يتم العثور على النظام رقم 10")
            return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False
    
    finally:
        try:
            cursor.close()
            conn.close()
            print("\n✅ تم إغلاق الاتصال")
        except:
            pass

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ تم العثور على النظام رقم 10")
        print("🚀 يمكن الآن تطوير النظام الشامل والمتقدم")
    else:
        print("❌ النظام رقم 10 غير موجود")
    print("=" * 60)
