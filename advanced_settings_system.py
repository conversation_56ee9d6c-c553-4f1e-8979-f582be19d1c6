#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - نظام الإعدادات الشامل والمتقدم (STP)
النظام رقم 10 - نظام إعدادات شامل ومتقدم بوضع ملء الشاشة
"""

import sys
import json
import os
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTabWidget, QScrollArea, QFrame, QLabel, QPushButton, QLineEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QTextEdit,
    QGroupBox, QGridLayout, QFormLayout, QFileDialog, QColorDialog,
    QFontDialog, QMessageBox, QSplitter, QTreeWidget, QTreeWidgetItem,
    QTableWidget, QTableWidgetItem, Q<PERSON>eaderView, QDateEdit, QTimeEdit,
    QSlider, QProgressBar, QToolBar, QStatusBar, QMenuBar, QMenu,
    QAction, QSizePolicy, QSpacerItem
)
from PySide6.QtCore import Qt, QTimer, QDate, QTime, QSize, Signal
from PySide6.QtGui import (
    QFont, QColor, QPalette, QPixmap, QIcon, QPainter, 
    QLinearGradient, QBrush, QPen, QAction as QGuiAction
)

# استيراد مساعد النصوص العربية
try:
    from arabic_text_helper import format_arabic_text, setup_arabic_widget
except ImportError:
    def format_arabic_text(text):
        return text
    def setup_arabic_widget(widget):
        pass

class AdvancedSettingsSystem(QMainWindow):
    """نظام الإعدادات الشامل والمتقدم - وضع ملء الشاشة"""
    
    def __init__(self):
        super().__init__()
        
        # إعداد النافذة الرئيسية
        self.setWindowTitle("🚢 SHP ERP - نظام الإعدادات الشامل والمتقدم")
        self.setWindowState(Qt.WindowMaximized)  # ملء الشاشة
        self.setMinimumSize(1200, 800)
        
        # إعداد دعم النصوص العربية
        setup_arabic_widget(self)
        
        # متغيرات النظام
        self.settings_file = "advanced_settings.json"
        self.current_settings = {}
        self.unsaved_changes = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # تحميل الإعدادات
        self.load_settings()
        
        # تطبيق الأنماط
        self.apply_advanced_styles()
        
        print("✅ تم تشغيل نظام الإعدادات الشامل والمتقدم")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # الشريط الجانبي للتنقل
        self.navigation_panel = self.create_navigation_panel()
        splitter.addWidget(self.navigation_panel)
        
        # منطقة المحتوى الرئيسية
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # تعيين نسب المقسم
        splitter.setSizes([300, 900])  # 25% للتنقل، 75% للمحتوى
        
    def create_navigation_panel(self):
        """إنشاء لوحة التنقل الجانبية"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMaximumWidth(350)
        panel.setMinimumWidth(280)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # عنوان اللوحة
        title_label = QLabel("🎛️ لوحة التحكم الشاملة")
        title_label.setFont(QFont("Tahoma", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # شجرة التنقل
        self.navigation_tree = QTreeWidget()
        self.navigation_tree.setHeaderHidden(True)
        self.navigation_tree.setRootIsDecorated(True)
        self.navigation_tree.setAlternatingRowColors(True)
        self.navigation_tree.itemClicked.connect(self.on_navigation_item_clicked)
        
        # إضافة عناصر التنقل
        self.populate_navigation_tree()
        
        layout.addWidget(self.navigation_tree)
        
        # معلومات سريعة
        info_frame = self.create_quick_info_panel()
        layout.addWidget(info_frame)
        
        return panel
    
    def populate_navigation_tree(self):
        """ملء شجرة التنقل بالعناصر"""
        # 1. إعدادات المظهر
        appearance_item = QTreeWidgetItem(["🎨 إعدادات المظهر الشاملة"])
        appearance_item.setData(0, Qt.UserRole, "appearance")
        
        appearance_sub_items = [
            ("🌈 الألوان والثيمات", "appearance_colors"),
            ("🔤 الخطوط والنصوص", "appearance_fonts"),
            ("🖼️ الخلفيات والصور", "appearance_backgrounds"),
            ("🎭 التأثيرات البصرية", "appearance_effects"),
            ("📱 تخطيط الواجهة", "appearance_layout")
        ]
        
        for text, data in appearance_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            appearance_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(appearance_item)
        
        # 2. إعدادات الشركة
        company_item = QTreeWidgetItem(["🏢 إعدادات الشركة الشاملة"])
        company_item.setData(0, Qt.UserRole, "company")
        
        company_sub_items = [
            ("📋 البيانات الأساسية", "company_basic"),
            ("📍 العناوين والمواقع", "company_addresses"),
            ("📞 معلومات الاتصال", "company_contact"),
            ("🏛️ البيانات القانونية", "company_legal"),
            ("🖼️ الشعارات والهوية", "company_branding"),
            ("🌐 المواقع الإلكترونية", "company_websites")
        ]
        
        for text, data in company_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            company_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(company_item)
        
        # 3. إعدادات العملات
        currency_item = QTreeWidgetItem(["💰 إعدادات العملات المتقدمة"])
        currency_item.setData(0, Qt.UserRole, "currency")
        
        currency_sub_items = [
            ("💱 العملات الأساسية", "currency_basic"),
            ("📈 أسعار الصرف", "currency_rates"),
            ("🔄 التحديث التلقائي", "currency_auto_update"),
            ("📊 التقريب والحسابات", "currency_calculations"),
            ("📜 سجل التغييرات", "currency_history")
        ]
        
        for text, data in currency_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            currency_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(currency_item)
        
        # 4. إعدادات السنة المالية
        fiscal_item = QTreeWidgetItem(["📅 إعدادات السنة المالية"])
        fiscal_item.setData(0, Qt.UserRole, "fiscal")
        
        fiscal_sub_items = [
            ("📆 السنة المالية الحالية", "fiscal_current"),
            ("📋 الفترات المالية", "fiscal_periods"),
            ("🔒 إغلاق الفترات", "fiscal_closing"),
            ("📊 التقارير المالية", "fiscal_reports"),
            ("🔄 الترحيلات", "fiscal_transfers")
        ]
        
        for text, data in fiscal_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            fiscal_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(fiscal_item)
        
        # 5. إعدادات النظام المتقدمة
        system_item = QTreeWidgetItem(["⚙️ إعدادات النظام المتقدمة"])
        system_item.setData(0, Qt.UserRole, "system")
        
        system_sub_items = [
            ("🔐 الأمان والصلاحيات", "system_security"),
            ("📧 البريد الإلكتروني", "system_email"),
            ("🖨️ الطباعة والتقارير", "system_printing"),
            ("💾 النسخ الاحتياطي", "system_backup"),
            ("🌐 اللغة والترجمة", "system_language"),
            ("📱 الإشعارات", "system_notifications")
        ]
        
        for text, data in system_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            system_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(system_item)
        
        # 6. إعدادات الأداء والتحسين
        performance_item = QTreeWidgetItem(["🚀 إعدادات الأداء والتحسين"])
        performance_item.setData(0, Qt.UserRole, "performance")
        
        performance_sub_items = [
            ("💾 إدارة الذاكرة", "performance_memory"),
            ("🗄️ قاعدة البيانات", "performance_database"),
            ("🌐 الشبكة والاتصال", "performance_network"),
            ("📊 مراقبة الأداء", "performance_monitoring"),
            ("🔧 التحسين التلقائي", "performance_optimization")
        ]
        
        for text, data in performance_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            performance_item.addChild(sub_item)
        
        self.navigation_tree.addTopLevelItem(performance_item)
        
        # توسيع العناصر الرئيسية
        self.navigation_tree.expandAll()
    
    def create_quick_info_panel(self):
        """إنشاء لوحة المعلومات السريعة"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMaximumHeight(200)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان
        title = QLabel("📊 معلومات سريعة")
        title.setFont(QFont("Tahoma", 10, QFont.Bold))
        layout.addWidget(title)
        
        # معلومات النظام
        info_text = QLabel()
        info_text.setWordWrap(True)
        info_text.setText(
            "🕒 آخر تحديث: غير محدد\n"
            "💾 حالة الحفظ: محفوظ\n"
            "👤 المستخدم: مدير النظام\n"
            "🔧 الإصدار: 1.0.0"
        )
        layout.addWidget(info_text)
        
        # زر الحفظ السريع
        quick_save_btn = QPushButton("💾 حفظ سريع")
        quick_save_btn.clicked.connect(self.quick_save)
        layout.addWidget(quick_save_btn)
        
        return frame
    
    def create_content_area(self):
        """إنشاء منطقة المحتوى الرئيسية"""
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(content_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان المحتوى
        self.content_title = QLabel("🎛️ مرحباً بك في نظام الإعدادات الشامل والمتقدم")
        self.content_title.setFont(QFont("Tahoma", 16, QFont.Bold))
        self.content_title.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.content_title)
        
        # منطقة التمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # ويدجت المحتوى
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)
        
        # إضافة محتوى ترحيبي افتراضي
        self.show_welcome_content()
        
        scroll_area.setWidget(self.content_widget)
        layout.addWidget(scroll_area)
        
        return content_frame

    def show_welcome_content(self):
        """عرض المحتوى الترحيبي"""
        # مسح المحتوى الحالي
        self.clear_content_layout()

        # بطاقة ترحيبية
        welcome_card = self.create_welcome_card()
        self.content_layout.addWidget(welcome_card)

        # بطاقات الإحصائيات
        stats_layout = QHBoxLayout()

        stats_data = [
            ("🎨", "إعدادات المظهر", "25 خيار متاح", "#FF6B6B"),
            ("🏢", "بيانات الشركة", "15 حقل", "#4ECDC4"),
            ("💰", "العملات", "8 عملات", "#45B7D1"),
            ("📅", "السنة المالية", "12 فترة", "#96CEB4")
        ]

        for icon, title, subtitle, color in stats_data:
            card = self.create_stat_card(icon, title, subtitle, color)
            stats_layout.addWidget(card)

        self.content_layout.addLayout(stats_layout)

        # مساحة مرنة
        self.content_layout.addStretch()

    def create_welcome_card(self):
        """إنشاء بطاقة ترحيبية"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                color: white;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(15)

        # العنوان الرئيسي
        title = QLabel("🚢 نظام الإعدادات الشامل والمتقدم")
        title.setFont(QFont("Tahoma", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white;")
        layout.addWidget(title)

        # الوصف
        description = QLabel(
            "مرحباً بك في نظام الإعدادات الأكثر تقدماً وشمولية في SHP ERP.\n"
            "يمكنك من هنا إدارة جميع إعدادات النظام بطريقة احترافية ومتقدمة."
        )
        description.setFont(QFont("Tahoma", 12))
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        description.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        layout.addWidget(description)

        # أزرار سريعة
        buttons_layout = QHBoxLayout()

        quick_buttons = [
            ("🎨 إعدادات المظهر", "appearance"),
            ("🏢 بيانات الشركة", "company"),
            ("💰 العملات", "currency"),
            ("📅 السنة المالية", "fiscal")
        ]

        for text, action in quick_buttons:
            btn = QPushButton(text)
            btn.setFont(QFont("Tahoma", 10))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: rgba(255, 255, 255, 0.2);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 8px;
                    padding: 10px 15px;
                    color: white;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.3);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 0.1);
                }
            """)
            btn.clicked.connect(lambda checked, a=action: self.show_section_content(a))
            buttons_layout.addWidget(btn)

        layout.addLayout(buttons_layout)

        return card

    def create_stat_card(self, icon, title, subtitle, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 10px;
                color: white;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(5)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Tahoma", 24))
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Tahoma", 11, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(QFont("Tahoma", 9))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.8);")
        layout.addWidget(subtitle_label)

        return card

    def clear_content_layout(self):
        """مسح محتوى التخطيط"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def on_navigation_item_clicked(self, item, column):
        """معالج النقر على عنصر التنقل"""
        section = item.data(0, Qt.UserRole)
        if section:
            self.show_section_content(section)

    def show_section_content(self, section):
        """عرض محتوى القسم المحدد"""
        # تحديث عنوان المحتوى
        section_titles = {
            "appearance": "🎨 إعدادات المظهر الشاملة",
            "appearance_colors": "🌈 الألوان والثيمات",
            "appearance_fonts": "🔤 الخطوط والنصوص",
            "appearance_backgrounds": "🖼️ الخلفيات والصور",
            "appearance_effects": "🎭 التأثيرات البصرية",
            "appearance_layout": "📱 تخطيط الواجهة",

            "company": "🏢 إعدادات الشركة الشاملة",
            "company_basic": "📋 البيانات الأساسية",
            "company_addresses": "📍 العناوين والمواقع",
            "company_contact": "📞 معلومات الاتصال",
            "company_legal": "🏛️ البيانات القانونية",
            "company_branding": "🖼️ الشعارات والهوية",
            "company_websites": "🌐 المواقع الإلكترونية",

            "currency": "💰 إعدادات العملات المتقدمة",
            "currency_basic": "💱 العملات الأساسية",
            "currency_rates": "📈 أسعار الصرف",
            "currency_auto_update": "🔄 التحديث التلقائي",
            "currency_calculations": "📊 التقريب والحسابات",
            "currency_history": "📜 سجل التغييرات",

            "fiscal": "📅 إعدادات السنة المالية",
            "fiscal_current": "📆 السنة المالية الحالية",
            "fiscal_periods": "📋 الفترات المالية",
            "fiscal_closing": "🔒 إغلاق الفترات",
            "fiscal_reports": "📊 التقارير المالية",
            "fiscal_transfers": "🔄 الترحيلات",

            "system": "⚙️ إعدادات النظام المتقدمة",
            "system_security": "🔐 الأمان والصلاحيات",
            "system_email": "📧 البريد الإلكتروني",
            "system_printing": "🖨️ الطباعة والتقارير",
            "system_backup": "💾 النسخ الاحتياطي",
            "system_language": "🌐 اللغة والترجمة",
            "system_notifications": "📱 الإشعارات",

            "performance": "🚀 إعدادات الأداء والتحسين",
            "performance_memory": "💾 إدارة الذاكرة",
            "performance_database": "🗄️ قاعدة البيانات",
            "performance_network": "🌐 الشبكة والاتصال",
            "performance_monitoring": "📊 مراقبة الأداء",
            "performance_optimization": "🔧 التحسين التلقائي"
        }

        title = section_titles.get(section, "⚙️ إعدادات النظام")
        self.content_title.setText(title)

        # مسح المحتوى الحالي
        self.clear_content_layout()

        # عرض المحتوى حسب القسم
        if section.startswith("appearance"):
            self.show_appearance_content(section)
        elif section.startswith("company"):
            self.show_company_content(section)
        elif section.startswith("currency"):
            self.show_currency_content(section)
        elif section.startswith("fiscal"):
            self.show_fiscal_content(section)
        elif section.startswith("system"):
            self.show_system_content(section)
        elif section.startswith("performance"):
            self.show_performance_content(section)
        else:
            self.show_welcome_content()

    def show_appearance_content(self, section):
        """عرض محتوى إعدادات المظهر"""
        if section == "appearance" or section == "appearance_colors":
            # إعدادات الألوان والثيمات
            colors_group = QGroupBox("🌈 إعدادات الألوان والثيمات")
            colors_layout = QGridLayout(colors_group)

            # اختيار الثيم
            colors_layout.addWidget(QLabel("🎨 الثيم الحالي:"), 0, 0)
            theme_combo = QComboBox()
            theme_combo.addItems(["فاتح", "داكن", "تلقائي", "أزرق", "أخضر", "بنفسجي"])
            colors_layout.addWidget(theme_combo, 0, 1)

            # اللون الأساسي
            colors_layout.addWidget(QLabel("🎯 اللون الأساسي:"), 1, 0)
            primary_color_btn = QPushButton("اختيار اللون")
            primary_color_btn.clicked.connect(lambda: self.choose_color("primary"))
            colors_layout.addWidget(primary_color_btn, 1, 1)

            # اللون الثانوي
            colors_layout.addWidget(QLabel("🎨 اللون الثانوي:"), 2, 0)
            secondary_color_btn = QPushButton("اختيار اللون")
            secondary_color_btn.clicked.connect(lambda: self.choose_color("secondary"))
            colors_layout.addWidget(secondary_color_btn, 2, 1)

            # شفافية النوافذ
            colors_layout.addWidget(QLabel("👻 شفافية النوافذ:"), 3, 0)
            opacity_slider = QSlider(Qt.Horizontal)
            opacity_slider.setRange(50, 100)
            opacity_slider.setValue(100)
            colors_layout.addWidget(opacity_slider, 3, 1)

            self.content_layout.addWidget(colors_group)

        if section == "appearance" or section == "appearance_fonts":
            # إعدادات الخطوط
            fonts_group = QGroupBox("🔤 إعدادات الخطوط والنصوص")
            fonts_layout = QGridLayout(fonts_group)

            # الخط الأساسي
            fonts_layout.addWidget(QLabel("📝 الخط الأساسي:"), 0, 0)
            font_btn = QPushButton("اختيار الخط")
            font_btn.clicked.connect(self.choose_font)
            fonts_layout.addWidget(font_btn, 0, 1)

            # حجم الخط
            fonts_layout.addWidget(QLabel("📏 حجم الخط:"), 1, 0)
            font_size_spin = QSpinBox()
            font_size_spin.setRange(8, 24)
            font_size_spin.setValue(11)
            fonts_layout.addWidget(font_size_spin, 1, 1)

            # اتجاه النص
            fonts_layout.addWidget(QLabel("↔️ اتجاه النص:"), 2, 0)
            direction_combo = QComboBox()
            direction_combo.addItems(["من اليمين لليسار", "من اليسار لليمين", "تلقائي"])
            fonts_layout.addWidget(direction_combo, 2, 1)

            self.content_layout.addWidget(fonts_group)

        # مساحة مرنة
        self.content_layout.addStretch()

    def show_company_content(self, section):
        """عرض محتوى إعدادات الشركة"""
        if section == "company" or section == "company_basic":
            # البيانات الأساسية
            basic_group = QGroupBox("📋 البيانات الأساسية للشركة")
            basic_layout = QFormLayout(basic_group)

            # اسم الشركة
            company_name_ar = QLineEdit()
            company_name_ar.setPlaceholderText("اسم الشركة بالعربية")
            basic_layout.addRow("🏢 اسم الشركة (عربي):", company_name_ar)

            company_name_en = QLineEdit()
            company_name_en.setPlaceholderText("Company Name in English")
            basic_layout.addRow("🏢 اسم الشركة (إنجليزي):", company_name_en)

            # نوع النشاط
            activity_type = QComboBox()
            activity_type.addItems([
                "تجارة عامة", "صناعة", "خدمات", "مقاولات",
                "استيراد وتصدير", "تكنولوجيا", "أخرى"
            ])
            basic_layout.addRow("🏭 نوع النشاط:", activity_type)

            # تاريخ التأسيس
            establishment_date = QDateEdit()
            establishment_date.setDate(QDate.currentDate())
            establishment_date.setCalendarPopup(True)
            basic_layout.addRow("📅 تاريخ التأسيس:", establishment_date)

            # رأس المال
            capital = QDoubleSpinBox()
            capital.setRange(0, *********)
            capital.setSuffix(" ريال")
            basic_layout.addRow("💰 رأس المال:", capital)

            self.content_layout.addWidget(basic_group)

        if section == "company" or section == "company_contact":
            # معلومات الاتصال
            contact_group = QGroupBox("📞 معلومات الاتصال")
            contact_layout = QFormLayout(contact_group)

            # الهاتف الرئيسي
            main_phone = QLineEdit()
            main_phone.setPlaceholderText("+966 11 234 5678")
            contact_layout.addRow("📞 الهاتف الرئيسي:", main_phone)

            # الجوال
            mobile = QLineEdit()
            mobile.setPlaceholderText("+966 50 123 4567")
            contact_layout.addRow("📱 الجوال:", mobile)

            # البريد الإلكتروني
            email = QLineEdit()
            email.setPlaceholderText("<EMAIL>")
            contact_layout.addRow("📧 البريد الإلكتروني:", email)

            # الموقع الإلكتروني
            website = QLineEdit()
            website.setPlaceholderText("https://www.company.com")
            contact_layout.addRow("🌐 الموقع الإلكتروني:", website)

            self.content_layout.addWidget(contact_group)

        # مساحة مرنة
        self.content_layout.addStretch()

    def show_currency_content(self, section):
        """عرض محتوى إعدادات العملات"""
        if section == "currency" or section == "currency_basic":
            # العملات الأساسية
            currency_group = QGroupBox("💱 إدارة العملات")
            currency_layout = QVBoxLayout(currency_group)

            # العملة الأساسية
            base_currency_layout = QHBoxLayout()
            base_currency_layout.addWidget(QLabel("💰 العملة الأساسية:"))

            base_currency_combo = QComboBox()
            base_currency_combo.addItems([
                "ريال سعودي (SAR)", "دولار أمريكي (USD)", "يورو (EUR)",
                "جنيه إسترليني (GBP)", "ين ياباني (JPY)"
            ])
            base_currency_layout.addWidget(base_currency_combo)
            base_currency_layout.addStretch()

            currency_layout.addLayout(base_currency_layout)

            # جدول العملات
            currencies_table = QTableWidget(5, 4)
            currencies_table.setHorizontalHeaderLabels([
                "العملة", "الرمز", "سعر الصرف", "آخر تحديث"
            ])

            # بيانات تجريبية
            currencies_data = [
                ("ريال سعودي", "SAR", "1.0000", "2025-07-13"),
                ("دولار أمريكي", "USD", "3.7500", "2025-07-13"),
                ("يورو", "EUR", "4.1200", "2025-07-13"),
                ("جنيه إسترليني", "GBP", "4.8500", "2025-07-13"),
                ("ين ياباني", "JPY", "0.0250", "2025-07-13")
            ]

            for row, (currency, code, rate, date) in enumerate(currencies_data):
                currencies_table.setItem(row, 0, QTableWidgetItem(currency))
                currencies_table.setItem(row, 1, QTableWidgetItem(code))
                currencies_table.setItem(row, 2, QTableWidgetItem(rate))
                currencies_table.setItem(row, 3, QTableWidgetItem(date))

            currencies_table.horizontalHeader().setStretchLastSection(True)
            currency_layout.addWidget(currencies_table)

            # أزرار الإدارة
            buttons_layout = QHBoxLayout()

            add_currency_btn = QPushButton("➕ إضافة عملة")
            edit_currency_btn = QPushButton("✏️ تعديل")
            delete_currency_btn = QPushButton("🗑️ حذف")
            update_rates_btn = QPushButton("🔄 تحديث الأسعار")

            buttons_layout.addWidget(add_currency_btn)
            buttons_layout.addWidget(edit_currency_btn)
            buttons_layout.addWidget(delete_currency_btn)
            buttons_layout.addWidget(update_rates_btn)
            buttons_layout.addStretch()

            currency_layout.addLayout(buttons_layout)

            self.content_layout.addWidget(currency_group)

        # مساحة مرنة
        self.content_layout.addStretch()

    def show_fiscal_content(self, section):
        """عرض محتوى إعدادات السنة المالية"""
        if section == "fiscal" or section == "fiscal_current":
            # السنة المالية الحالية
            fiscal_group = QGroupBox("📆 السنة المالية الحالية")
            fiscal_layout = QFormLayout(fiscal_group)

            # بداية السنة المالية
            fiscal_start = QDateEdit()
            fiscal_start.setDate(QDate(2025, 1, 1))
            fiscal_start.setCalendarPopup(True)
            fiscal_layout.addRow("📅 بداية السنة المالية:", fiscal_start)

            # نهاية السنة المالية
            fiscal_end = QDateEdit()
            fiscal_end.setDate(QDate(2025, 12, 31))
            fiscal_end.setCalendarPopup(True)
            fiscal_layout.addRow("📅 نهاية السنة المالية:", fiscal_end)

            # الفترة الحالية
            current_period = QComboBox()
            current_period.addItems([
                "يناير 2025", "فبراير 2025", "مارس 2025", "أبريل 2025",
                "مايو 2025", "يونيو 2025", "يوليو 2025", "أغسطس 2025",
                "سبتمبر 2025", "أكتوبر 2025", "نوفمبر 2025", "ديسمبر 2025"
            ])
            current_period.setCurrentText("يوليو 2025")
            fiscal_layout.addRow("📋 الفترة الحالية:", current_period)

            # حالة السنة المالية
            fiscal_status = QComboBox()
            fiscal_status.addItems(["مفتوحة", "مغلقة", "مؤقتة"])
            fiscal_layout.addRow("🔒 حالة السنة المالية:", fiscal_status)

            self.content_layout.addWidget(fiscal_group)

        # مساحة مرنة
        self.content_layout.addStretch()

    def show_system_content(self, section):
        """عرض محتوى إعدادات النظام"""
        if section == "system" or section == "system_security":
            # إعدادات الأمان
            security_group = QGroupBox("🔐 إعدادات الأمان والصلاحيات")
            security_layout = QFormLayout(security_group)

            # مدة انتهاء الجلسة
            session_timeout = QSpinBox()
            session_timeout.setRange(5, 480)
            session_timeout.setValue(30)
            session_timeout.setSuffix(" دقيقة")
            security_layout.addRow("⏰ انتهاء الجلسة:", session_timeout)

            # تعقيد كلمة المرور
            password_complexity = QCheckBox("تفعيل تعقيد كلمة المرور")
            password_complexity.setChecked(True)
            security_layout.addRow("🔒 كلمة المرور:", password_complexity)

            # تسجيل العمليات
            audit_log = QCheckBox("تسجيل جميع العمليات")
            audit_log.setChecked(True)
            security_layout.addRow("📝 سجل العمليات:", audit_log)

            self.content_layout.addWidget(security_group)

        # مساحة مرنة
        self.content_layout.addStretch()

    def show_performance_content(self, section):
        """عرض محتوى إعدادات الأداء"""
        if section == "performance" or section == "performance_memory":
            # إعدادات الذاكرة
            memory_group = QGroupBox("💾 إدارة الذاكرة والأداء")
            memory_layout = QFormLayout(memory_group)

            # حجم الذاكرة المؤقتة
            cache_size = QSpinBox()
            cache_size.setRange(64, 2048)
            cache_size.setValue(512)
            cache_size.setSuffix(" MB")
            memory_layout.addRow("🗄️ حجم الذاكرة المؤقتة:", cache_size)

            # تنظيف تلقائي
            auto_cleanup = QCheckBox("تنظيف تلقائي للذاكرة")
            auto_cleanup.setChecked(True)
            memory_layout.addRow("🧹 التنظيف التلقائي:", auto_cleanup)

            self.content_layout.addWidget(memory_group)

        # مساحة مرنة
        self.content_layout.addStretch()

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu("📁 ملف")

        # حفظ الإعدادات
        save_action = QAction("💾 حفظ الإعدادات", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        file_menu.addAction(save_action)

        # تصدير الإعدادات
        export_action = QAction("📤 تصدير الإعدادات", self)
        export_action.triggered.connect(self.export_settings)
        file_menu.addAction(export_action)

        # استيراد الإعدادات
        import_action = QAction("📥 استيراد الإعدادات", self)
        import_action.triggered.connect(self.import_settings)
        file_menu.addAction(import_action)

        file_menu.addSeparator()

        # إعادة تعيين
        reset_action = QAction("🔄 إعادة تعيين", self)
        reset_action.triggered.connect(self.reset_settings)
        file_menu.addAction(reset_action)

        file_menu.addSeparator()

        # خروج
        exit_action = QAction("🚪 خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة العرض
        view_menu = menubar.addMenu("👁️ عرض")

        # ملء الشاشة
        fullscreen_action = QAction("🖥️ ملء الشاشة", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # إخفاء/إظهار الشريط الجانبي
        toggle_sidebar_action = QAction("📋 إخفاء/إظهار الشريط الجانبي", self)
        toggle_sidebar_action.setShortcut("Ctrl+B")
        toggle_sidebar_action.triggered.connect(self.toggle_sidebar)
        view_menu.addAction(toggle_sidebar_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("❓ مساعدة")

        # حول البرنامج
        about_action = QAction("ℹ️ حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)

        # حفظ سريع
        save_action = QAction("💾", self)
        save_action.setToolTip("حفظ سريع (Ctrl+S)")
        save_action.triggered.connect(self.save_settings)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # تصدير
        export_action = QAction("📤", self)
        export_action.setToolTip("تصدير الإعدادات")
        export_action.triggered.connect(self.export_settings)
        toolbar.addAction(export_action)

        # استيراد
        import_action = QAction("📥", self)
        import_action.setToolTip("استيراد الإعدادات")
        import_action.triggered.connect(self.import_settings)
        toolbar.addAction(import_action)

        toolbar.addSeparator()

        # إعادة تعيين
        reset_action = QAction("🔄", self)
        reset_action.setToolTip("إعادة تعيين الإعدادات")
        reset_action.triggered.connect(self.reset_settings)
        toolbar.addAction(reset_action)

        toolbar.addSeparator()

        # معاينة
        preview_action = QAction("👁️", self)
        preview_action.setToolTip("معاينة التغييرات")
        preview_action.triggered.connect(self.preview_changes)
        toolbar.addAction(preview_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()

        # رسالة الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)

        # مؤشر التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.progress_bar)

        # معلومات المستخدم
        user_label = QLabel("👤 مدير النظام")
        status_bar.addPermanentWidget(user_label)

        # الوقت
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)

        # تحديث الوقت كل ثانية
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

    def update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"🕒 {current_time}")

    def apply_advanced_styles(self):
        """تطبيق الأنماط المتقدمة"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }

            QFrame[frameShape="4"] {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                background-color: white;
                margin: 5px;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #6c757d;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
            }

            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }

            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #004085, stop:1 #002752);
            }

            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit {
                border: 2px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
                selection-background-color: #007bff;
            }

            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #007bff;
                outline: none;
            }

            QTreeWidget {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                selection-color: white;
            }

            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }

            QTreeWidget::item:hover {
                background-color: #e3f2fd;
            }

            QTreeWidget::item:selected {
                background-color: #007bff;
                color: white;
            }

            QTableWidget {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                gridline-color: #e9ecef;
                selection-background-color: #007bff;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c757d, stop:1 #495057);
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }

            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background: #6c757d;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background: #495057;
            }

            QCheckBox {
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #6c757d;
                border-radius: 3px;
                background-color: white;
            }

            QCheckBox::indicator:checked {
                background-color: #007bff;
                border-color: #007bff;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEwIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik04LjUgMUwzLjUgNkwxLjUgNCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }

            QSlider::groove:horizontal {
                border: 1px solid #bbb;
                background: white;
                height: 10px;
                border-radius: 4px;
            }

            QSlider::sub-page:horizontal {
                background: #007bff;
                border: 1px solid #777;
                height: 10px;
                border-radius: 4px;
            }

            QSlider::handle:horizontal {
                background: white;
                border: 2px solid #007bff;
                width: 18px;
                margin: -4px 0;
                border-radius: 9px;
            }

            QSlider::handle:horizontal:hover {
                background: #e3f2fd;
            }
        """)

    # وظائف المساعدة والحفظ
    def choose_color(self, color_type):
        """اختيار لون"""
        color = QColorDialog.getColor(Qt.blue, self, f"اختيار {color_type}")
        if color.isValid():
            self.current_settings[f"{color_type}_color"] = color.name()
            self.mark_unsaved_changes()
            self.status_label.setText(f"تم تغيير لون {color_type}")

    def choose_font(self):
        """اختيار خط"""
        font, ok = QFontDialog.getFont(QFont("Tahoma", 11), self, "اختيار الخط")
        if ok:
            self.current_settings["font_family"] = font.family()
            self.current_settings["font_size"] = font.pointSize()
            self.mark_unsaved_changes()
            self.status_label.setText(f"تم تغيير الخط إلى {font.family()}")

    def mark_unsaved_changes(self):
        """تمييز التغييرات غير المحفوظة"""
        self.unsaved_changes = True
        if not self.windowTitle().endswith("*"):
            self.setWindowTitle(self.windowTitle() + "*")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)

            # محاكاة عملية الحفظ
            for i in range(101):
                self.progress_bar.setValue(i)
                QApplication.processEvents()
                if i % 20 == 0:
                    import time
                    time.sleep(0.1)

            # حفظ في ملف JSON
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, ensure_ascii=False, indent=2)

            self.unsaved_changes = False
            title = self.windowTitle().replace("*", "")
            self.setWindowTitle(title)

            self.progress_bar.setVisible(False)
            self.status_label.setText("✅ تم حفظ الإعدادات بنجاح")

            QMessageBox.information(self, "نجح الحفظ", "تم حفظ جميع الإعدادات بنجاح!")

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.status_label.setText(f"❌ فشل في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الإعدادات:\n{str(e)}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.current_settings = json.load(f)
                self.status_label.setText("✅ تم تحميل الإعدادات")
            else:
                # إعدادات افتراضية
                self.current_settings = {
                    "theme": "فاتح",
                    "primary_color": "#007bff",
                    "secondary_color": "#6c757d",
                    "font_family": "Tahoma",
                    "font_size": 11,
                    "language": "العربية",
                    "company_name_ar": "",
                    "company_name_en": "",
                    "base_currency": "ريال سعودي (SAR)",
                    "fiscal_year_start": "2025-01-01",
                    "fiscal_year_end": "2025-12-31"
                }
                self.status_label.setText("تم تحميل الإعدادات الافتراضية")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في تحميل الإعدادات: {str(e)}")
            self.current_settings = {}

    def export_settings(self):
        """تصدير الإعدادات"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الإعدادات", "settings_backup.json",
                "JSON Files (*.json);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.current_settings, f, ensure_ascii=False, indent=2)

                self.status_label.setText(f"✅ تم تصدير الإعدادات إلى {file_path}")
                QMessageBox.information(self, "نجح التصدير", f"تم تصدير الإعدادات بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.status_label.setText(f"❌ فشل في تصدير الإعدادات: {str(e)}")
            QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير الإعدادات:\n{str(e)}")

    def import_settings(self):
        """استيراد الإعدادات"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "استيراد الإعدادات", "",
                "JSON Files (*.json);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_settings = json.load(f)

                # دمج الإعدادات المستوردة
                self.current_settings.update(imported_settings)
                self.mark_unsaved_changes()

                self.status_label.setText(f"✅ تم استيراد الإعدادات من {file_path}")
                QMessageBox.information(self, "نجح الاستيراد", f"تم استيراد الإعدادات بنجاح من:\n{file_path}")

        except Exception as e:
            self.status_label.setText(f"❌ فشل في استيراد الإعدادات: {str(e)}")
            QMessageBox.critical(self, "خطأ في الاستيراد", f"فشل في استيراد الإعدادات:\n{str(e)}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "تأكيد إعادة التعيين",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n"
            "سيتم فقدان جميع التغييرات الحالية!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.current_settings.clear()
            self.load_settings()  # تحميل الإعدادات الافتراضية
            self.mark_unsaved_changes()
            self.status_label.setText("🔄 تم إعادة تعيين الإعدادات")
            QMessageBox.information(self, "تم إعادة التعيين", "تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية")

    def quick_save(self):
        """حفظ سريع"""
        self.save_settings()

    def preview_changes(self):
        """معاينة التغييرات"""
        preview_text = "📋 معاينة التغييرات الحالية:\n\n"

        for key, value in self.current_settings.items():
            preview_text += f"• {key}: {value}\n"

        if not self.current_settings:
            preview_text += "لا توجد تغييرات للمعاينة"

        QMessageBox.information(self, "معاينة التغييرات", preview_text)

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.isFullScreen():
            self.showMaximized()
            self.status_label.setText("تم الخروج من وضع ملء الشاشة")
        else:
            self.showFullScreen()
            self.status_label.setText("تم تفعيل وضع ملء الشاشة")

    def toggle_sidebar(self):
        """إخفاء/إظهار الشريط الجانبي"""
        if self.navigation_panel.isVisible():
            self.navigation_panel.hide()
            self.status_label.setText("تم إخفاء الشريط الجانبي")
        else:
            self.navigation_panel.show()
            self.status_label.setText("تم إظهار الشريط الجانبي")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
        🚢 SHP ERP - نظام الإعدادات الشامل والمتقدم

        الإصدار: 1.0.0
        تاريخ الإصدار: 2025-07-13

        نظام إعدادات شامل ومتقدم يوفر:
        • إعدادات المظهر الشاملة
        • إدارة بيانات الشركة
        • إعدادات العملات المتقدمة
        • إدارة السنة المالية
        • إعدادات النظام والأداء

        تم التطوير بواسطة: فريق SHP ERP
        """

        QMessageBox.about(self, "حول البرنامج", about_text)

    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        if self.unsaved_changes:
            reply = QMessageBox.question(
                self, "تأكيد الإغلاق",
                "توجد تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                QMessageBox.Save
            )

            if reply == QMessageBox.Save:
                self.save_settings()
                event.accept()
            elif reply == QMessageBox.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """الدالة الرئيسية"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    # إعداد التطبيق للنصوص العربية
    try:
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
    except ImportError:
        pass

    # إنشاء وعرض النافذة
    window = AdvancedSettingsSystem()
    window.show()

    # تشغيل التطبيق إذا لم يكن يعمل
    if not app.instance():
        sys.exit(app.exec())

if __name__ == "__main__":
    main()
