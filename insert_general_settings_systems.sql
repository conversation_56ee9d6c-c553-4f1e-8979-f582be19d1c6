-- إدراج نظام الإعدادات العامة والأنظمة الفرعية في جدول S_ERP_SYSTEM
-- SHP ERP - General Settings System

-- التحقق من وجود النظام الرئيسي للإعدادات العامة
SELECT COUNT(*) FROM S_ERP_SYSTEM WHERE SYSTEM_ID = 10;

-- تحديث النظام الرئيسي للإعدادات العامة (إذا كان موجوداً)
UPDATE S_ERP_SYSTEM SET
    SYSTEM_NAME = 'نظام الإعدادات العامة الشامل',
    SYSTEM_CODE = 'GENSETTINGS',
    SYSTEM_ICON = '⚙️',
    SYSTEM_DESCRIPTION = 'نظام شامل ومتقدم لإدارة جميع إعدادات النظام والشركة',
    SYSTEM_PATH = 'general_settings_system.py',
    UPDATED_DATE = SYSDATE,
    UPDATED_BY = 'SYSTEM'
WHERE SYSTEM_ID = 10;

-- إدراج النظام الرئيسي إذا لم يكن موجوداً
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, 
    SYSTEM_NAME, 
    SYSTEM_CODE, 
    PARENT_ID, 
    SYSTEM_ORDER, 
    IS_ACTIVE, 
    CREATED_DATE, 
    CREATED_BY,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION,
    SYSTEM_PATH
)
SELECT 
    10,
    'نظام الإعدادات العامة الشامل',
    'GENSETTINGS',
    1,  -- تحت النظام الرئيسي ERP
    10,
    'Y',
    SYSDATE,
    'SYSTEM',
    '⚙️',
    'نظام شامل ومتقدم لإدارة جميع إعدادات النظام والشركة',
    'general_settings_system.py'
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM S_ERP_SYSTEM WHERE SYSTEM_ID = 10);

-- حذف الأنظمة الفرعية القديمة إن وجدت
DELETE FROM S_ERP_SYSTEM WHERE PARENT_ID = 10 AND SYSTEM_ID BETWEEN 101 AND 120;

-- إدراج الأنظمة الفرعية للإعدادات العامة

-- 1. إعدادات المظهر
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    101, 'إعدادات المظهر', 'APPEARANCE', 10, 1, 'Y', SYSDATE, 'SYSTEM',
    '🎨', 'إعدادات شاملة للمظهر والألوان والخطوط والثيمات', 'general_settings_system.py'
);

-- 2. بيانات الشركة
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    102, 'بيانات الشركة', 'COMPANY', 10, 2, 'Y', SYSDATE, 'SYSTEM',
    '🏢', 'إدارة شاملة لبيانات الشركة والمعلومات القانونية والشعار', 'general_settings_system.py'
);

-- 3. إعدادات العملات
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    103, 'إعدادات العملات', 'CURRENCIES', 10, 3, 'Y', SYSDATE, 'SYSTEM',
    '💰', 'إدارة متقدمة للعملات وأسعار الصرف والتقريب', 'general_settings_system.py'
);

-- 4. السنة المالية
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    104, 'السنة المالية', 'FISCALYEAR', 10, 4, 'Y', SYSDATE, 'SYSTEM',
    '📅', 'إدارة السنة المالية والفترات المالية وإغلاق الفترات', 'general_settings_system.py'
);

-- 5. إعدادات اللغة
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    105, 'إعدادات اللغة', 'LANGUAGE', 10, 5, 'Y', SYSDATE, 'SYSTEM',
    '🌐', 'إعدادات اللغة والترجمة وتنسيق التاريخ والوقت', 'general_settings_system.py'
);

-- 6. إعدادات الأمان
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    106, 'إعدادات الأمان', 'SECURITY', 10, 6, 'Y', SYSDATE, 'SYSTEM',
    '🔐', 'إعدادات الأمان وكلمات المرور والجلسات', 'general_settings_system.py'
);

-- 7. إعدادات البريد الإلكتروني
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    107, 'إعدادات البريد الإلكتروني', 'EMAIL', 10, 7, 'Y', SYSDATE, 'SYSTEM',
    '📧', 'إعدادات خادم البريد الإلكتروني SMTP وإرسال الرسائل', 'general_settings_system.py'
);

-- 8. إعدادات الطباعة
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    108, 'إعدادات الطباعة', 'PRINTING', 10, 8, 'Y', SYSDATE, 'SYSTEM',
    '🖨️', 'إعدادات الطابعات وأحجام الورق وجودة الطباعة', 'general_settings_system.py'
);

-- 9. إعدادات التقارير
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    109, 'إعدادات التقارير', 'REPORTS', 10, 9, 'Y', SYSDATE, 'SYSTEM',
    '📊', 'إعدادات التقارير وتنسيقات التصدير والعرض', 'general_settings_system.py'
);

-- 10. إعدادات النسخ الاحتياطي
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    110, 'إعدادات النسخ الاحتياطي', 'BACKUPSETTINGS', 10, 10, 'Y', SYSDATE, 'SYSTEM',
    '🔄', 'إعدادات النسخ الاحتياطي التلقائي والجدولة', 'general_settings_system.py'
);

-- 11. إعدادات النظام
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    111, 'إعدادات النظام', 'SYSTEMSETTINGS', 10, 11, 'Y', SYSDATE, 'SYSTEM',
    '⚙️', 'إعدادات الأداء والذاكرة المؤقتة والحفظ التلقائي', 'general_settings_system.py'
);

-- 12. إعدادات الإشعارات
INSERT INTO S_ERP_SYSTEM (
    SYSTEM_ID, SYSTEM_NAME, SYSTEM_CODE, PARENT_ID, SYSTEM_ORDER, 
    IS_ACTIVE, CREATED_DATE, CREATED_BY, SYSTEM_ICON, SYSTEM_DESCRIPTION, SYSTEM_PATH
) VALUES (
    112, 'إعدادات الإشعارات', 'NOTIFICATIONS', 10, 12, 'Y', SYSDATE, 'SYSTEM',
    '📱', 'إعدادات الإشعارات والتنبيهات والأصوات', 'general_settings_system.py'
);

-- تأكيد الإدراج
COMMIT;

-- التحقق من الإدراج
SELECT 
    SYSTEM_ID,
    SYSTEM_NAME,
    SYSTEM_CODE,
    PARENT_ID,
    SYSTEM_ORDER,
    IS_ACTIVE,
    SYSTEM_ICON,
    SYSTEM_DESCRIPTION
FROM S_ERP_SYSTEM 
WHERE PARENT_ID = 10 OR SYSTEM_ID = 10
ORDER BY SYSTEM_ORDER;

-- عرض الهيكل الشجري لنظام الإعدادات العامة
SELECT 
    LEVEL,
    LPAD(' ', (LEVEL-1)*4) || SYSTEM_ICON || ' ' || SYSTEM_NAME AS SYSTEM_TREE,
    SYSTEM_CODE,
    SYSTEM_ID
FROM S_ERP_SYSTEM
START WITH SYSTEM_ID = 10
CONNECT BY PRIOR SYSTEM_ID = PARENT_ID
ORDER SIBLINGS BY SYSTEM_ORDER;

-- إحصائيات الأنظمة المدرجة
SELECT 
    'النظام الرئيسي' AS TYPE,
    COUNT(*) AS COUNT
FROM S_ERP_SYSTEM 
WHERE SYSTEM_ID = 10
UNION ALL
SELECT 
    'الأنظمة الفرعية' AS TYPE,
    COUNT(*) AS COUNT
FROM S_ERP_SYSTEM 
WHERE PARENT_ID = 10;

PROMPT 'تم إدراج نظام الإعدادات العامة الشامل بنجاح في جدول S_ERP_SYSTEM';
PROMPT 'النظام الرئيسي: نظام الإعدادات العامة الشامل (ID: 10)';
PROMPT 'الأنظمة الفرعية: 12 نظام (IDs: 101-112)';
PROMPT 'الأنظمة المتاحة:';
PROMPT '  🎨 إعدادات المظهر';
PROMPT '  🏢 بيانات الشركة';
PROMPT '  💰 إعدادات العملات';
PROMPT '  📅 السنة المالية';
PROMPT '  🌐 إعدادات اللغة';
PROMPT '  🔐 إعدادات الأمان';
PROMPT '  📧 إعدادات البريد الإلكتروني';
PROMPT '  🖨️ إعدادات الطباعة';
PROMPT '  📊 إعدادات التقارير';
PROMPT '  🔄 إعدادات النسخ الاحتياطي';
PROMPT '  ⚙️ إعدادات النظام';
PROMPT '  📱 إعدادات الإشعارات';
PROMPT 'يمكنك الآن رؤية هذه الأنظمة في قائمة الأنظمة في التطبيق';
