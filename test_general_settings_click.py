#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النقر على نظام الإعدادات العامة
"""

import sys
from PySide6.QtWidgets import QApplication

def test_general_settings_click():
    """اختبار النقر على نظام الإعدادات العامة"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إعداد التطبيق للنصوص العربية
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
        
        print("✅ تم إعداد التطبيق")
        
        # اختبار استيراد نظام الإعدادات العامة
        print("🔄 اختبار استيراد نظام الإعدادات العامة...")
        
        try:
            from general_settings_system import GeneralSettingsSystem
            print("✅ تم استيراد GeneralSettingsSystem بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد GeneralSettingsSystem: {e}")
            return False
        
        # اختبار إنشاء النافذة
        print("🔄 اختبار إنشاء نافذة الإعدادات العامة...")
        
        try:
            settings_window = GeneralSettingsSystem()
            print("✅ تم إنشاء نافذة الإعدادات العامة")
            
            # اختبار عرض النافذة
            settings_window.show()
            print("✅ تم عرض النافذة")
            
            # اختبار فتح قسم محدد
            print("🔄 اختبار فتح قسم إعدادات المظهر...")
            settings_window.open_specific_section('APPEARANCE')
            print("✅ تم فتح قسم إعدادات المظهر")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء أو عرض النافذة: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # اختبار النافذة الرئيسية
        print("\n🔄 اختبار النافذة الرئيسية...")
        
        try:
            from main_window import SHPERPMainWindow
            main_window = SHPERPMainWindow()
            print("✅ تم إنشاء النافذة الرئيسية")
            
            # اختبار معالج النقر
            print("🔄 اختبار معالج النقر...")
            
            # محاكاة بيانات النظام
            system_data = {
                'SYSTEM_CODE': 'GENSETTINGS',
                'SYSTEM_NAME': 'نظام الإعدادات العامة الشامل'
            }
            
            # اختبار المعالج
            main_window.launch_general_settings(system_data)
            print("✅ تم اختبار معالج النقر للنظام الرئيسي")
            
            # اختبار الأنظمة الفرعية
            subsystems = [
                {'SYSTEM_CODE': 'APPEARANCE', 'SYSTEM_NAME': 'إعدادات المظهر'},
                {'SYSTEM_CODE': 'COMPANY', 'SYSTEM_NAME': 'بيانات الشركة'},
                {'SYSTEM_CODE': 'CURRENCIES', 'SYSTEM_NAME': 'إعدادات العملات'}
            ]
            
            for subsystem in subsystems:
                main_window.launch_general_settings(subsystem)
                print(f"✅ تم اختبار معالج النقر لـ {subsystem['SYSTEM_NAME']}")
            
        except Exception as e:
            print(f"❌ فشل في اختبار النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار النقر على نظام الإعدادات العامة")
    print("=" * 70)
    
    success = test_general_settings_click()
    
    print("\n" + "=" * 70)
    print("📊 النتيجة:")
    print("=" * 70)
    
    if success:
        print("✅ جميع الاختبارات نجحت!")
        print("🎯 نظام الإعدادات العامة يعمل بشكل صحيح")
        
        print("\n🚀 للاستخدام:")
        print("1️⃣ شغل: python main_window.py")
        print("2️⃣ انقر على: ⚙️ نظام الإعدادات العامة")
        print("3️⃣ يجب أن تفتح النافذة الآن! ✨")
        
        print("\n🔧 إذا لم تفتح النافذة:")
        print("• تأكد من أن النافذة لم تفتح خلف النافذة الرئيسية")
        print("• جرب النقر على أيقونة التطبيق في شريط المهام")
        print("• أو جرب Alt+Tab للتنقل بين النوافذ")
        
    else:
        print("❌ فشلت بعض الاختبارات!")
        print("🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
