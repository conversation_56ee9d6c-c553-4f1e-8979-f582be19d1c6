#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Quick Test Main Window
اختبار سريع للنافذة الرئيسية
"""

import sys
import time

def test_main_window_creation():
    """اختبار إنشاء النافذة الرئيسية"""
    print("🔄 اختبار إنشاء النافذة الرئيسية...")

    try:
        from PySide6.QtWidgets import QApplication
        from database_admin_window import DatabaseAdminWindow

        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # اختبار إنشاء النافذة (بدون عرضها)
        window = DatabaseAdminWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار الخصائص الأساسية
        if hasattr(window, 'db_manager'):
            print("✅ مدير قاعدة البيانات متاح")
        
        if hasattr(window, 'main_tabs'):
            print(f"✅ التبويبات متاحة: {window.main_tabs.count()} تبويب")
        
        if hasattr(window, 'connections_tree'):
            print("✅ شجرة الاتصالات متاحة")
        
        # اختبار تحميل الاتصالات
        window.load_connections_list()
        print("✅ تم تحميل قائمة الاتصالات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة الرئيسية: {e}")
        return False

def test_launcher():
    """اختبار المشغل"""
    print("\n🔄 اختبار المشغل...")

    try:
        from PySide6.QtWidgets import QApplication
        from run_database_admin import DatabaseAdminLauncher

        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # اختبار إنشاء المشغل (بدون عرضه)
        launcher = DatabaseAdminLauncher()
        print("✅ تم إنشاء المشغل بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المشغل: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔄 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from multi_database_manager import MultiDatabaseManager
        
        manager = MultiDatabaseManager()
        connections = manager.get_connections_list()
        
        print(f"📋 عدد الاتصالات المحفوظة: {len(connections)}")
        
        if connections:
            # اختبار الاتصال الأول
            conn_name = connections[0]['name']
            success, message = manager.test_connection(conn_name)
            
            if success:
                print(f"✅ نجح اختبار الاتصال: {conn_name}")
                print(f"📝 الرسالة: {message}")
            else:
                print(f"⚠️ فشل اختبار الاتصال: {conn_name}")
                print(f"📝 الرسالة: {message}")
        else:
            print("⚠️ لا توجد اتصالات محفوظة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار سريع للنافذة الرئيسية")
    print("=" * 60)
    
    tests = [
        ("إنشاء النافذة الرئيسية", test_main_window_creation),
        ("إنشاء المشغل", test_launcher),
        ("اختبار الاتصال بقاعدة البيانات", test_database_connection)
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار السريع:")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    print(f"⏱️ وقت التنفيذ: {duration:.2f} ثانية")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النافذة الرئيسية جاهزة للاستخدام")
        print("\n🚀 لتشغيل النظام:")
        print("python run_database_admin.py")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("="*60)

if __name__ == "__main__":
    main()
