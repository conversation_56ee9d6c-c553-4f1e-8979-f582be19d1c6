#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حقيقي لظهور البيانات في شجرة الأنظمة
"""

import sys
import time
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

def test_tree_data():
    """اختبار ظهور البيانات في الشجرة"""
    try:
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إعداد التطبيق للنصوص العربية
        from arabic_text_helper import setup_arabic_application
        setup_arabic_application(app)
        
        print("✅ تم إعداد التطبيق")
        
        # إنشاء النافذة الرئيسية
        from main_window import SHPERPMainWindow
        window = SHPERPMainWindow()
        
        print("✅ تم إنشاء النافذة الرئيسية")
        
        # عرض النافذة
        window.show()
        
        # انتظار قليل لتحميل البيانات
        print("🔄 انتظار تحميل البيانات...")
        
        def check_data():
            """فحص البيانات بعد التحميل"""
            print("\n📋 فحص البيانات المحملة:")
            
            # فحص البيانات في النافذة الرئيسية
            if hasattr(window, 'systems_data') and window.systems_data:
                print(f"✅ تم العثور على {len(window.systems_data)} نظام في النافذة الرئيسية")
                
                # البحث عن نظام الإعدادات العامة
                general_settings_found = False
                for system in window.systems_data:
                    system_code = system.get('SYSTEM_CODE', '')
                    system_name = system.get('SYSTEM_NAME', '')
                    
                    if system_code == 'GENSETTINGS':
                        general_settings_found = True
                        print(f"✅ تم العثور على: {system_name} [{system_code}]")
                        break
                
                if not general_settings_found:
                    print("❌ لم يتم العثور على نظام الإعدادات العامة في البيانات")
                
            else:
                print("❌ لا توجد بيانات أنظمة في النافذة الرئيسية")
            
            # فحص شجرة الأنظمة
            if hasattr(window, 'systems_tree_widget') and window.systems_tree_widget:
                print("✅ تم العثور على مكون شجرة الأنظمة")
                
                # فحص البيانات في الشجرة
                if hasattr(window.systems_tree_widget, 'systems_data') and window.systems_tree_widget.systems_data:
                    print(f"✅ الشجرة تحتوي على {len(window.systems_tree_widget.systems_data)} نظام")
                else:
                    print("❌ الشجرة لا تحتوي على بيانات")
                
                # فحص عناصر الشجرة المرئية
                tree_widget = window.systems_tree_widget.tree_widget
                root_count = tree_widget.topLevelItemCount()
                print(f"📊 عدد العناصر الجذر في الشجرة: {root_count}")
                
                # عرض العناصر الجذر
                for i in range(root_count):
                    item = tree_widget.topLevelItem(i)
                    item_text = item.text(0)
                    print(f"   📂 {i+1}: {item_text}")
                    
                    # فحص العناصر الفرعية
                    child_count = item.childCount()
                    if child_count > 0:
                        print(f"      📁 العناصر الفرعية ({child_count}):")
                        for j in range(min(child_count, 5)):  # عرض أول 5 فقط
                            child = item.child(j)
                            child_text = child.text(0)
                            print(f"         • {child_text}")
                        if child_count > 5:
                            print(f"         ... و {child_count - 5} عنصر آخر")
                
            else:
                print("❌ لم يتم العثور على مكون شجرة الأنظمة")
            
            # محاولة النقر على نظام الإعدادات العامة
            print("\n🔄 محاولة النقر على نظام الإعدادات العامة...")
            
            if hasattr(window, 'systems_data') and window.systems_data:
                for system in window.systems_data:
                    if system.get('SYSTEM_CODE') == 'GENSETTINGS':
                        try:
                            window.handle_system_selection(system)
                            print("✅ تم محاكاة النقر على نظام الإعدادات العامة")
                        except Exception as e:
                            print(f"❌ فشل في محاكاة النقر: {e}")
                        break
            
            print("\n🎯 النتيجة:")
            if hasattr(window, 'systems_data') and window.systems_data:
                print("✅ البيانات محملة في النافذة الرئيسية")
                if hasattr(window, 'systems_tree_widget') and window.systems_tree_widget:
                    if hasattr(window.systems_tree_widget, 'systems_data') and window.systems_tree_widget.systems_data:
                        print("✅ البيانات محملة في الشجرة")
                        print("🎉 النظام يجب أن يعمل الآن!")
                    else:
                        print("❌ البيانات غير محملة في الشجرة")
                else:
                    print("❌ مكون الشجرة غير موجود")
            else:
                print("❌ البيانات غير محملة في النافذة الرئيسية")
        
        # تشغيل الفحص بعد 3 ثوان
        QTimer.singleShot(3000, check_data)
        
        # تشغيل التطبيق
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚢 SHP ERP - اختبار حقيقي لشجرة الأنظمة")
    print("=" * 60)
    
    result = test_tree_data()
    
    print("\n" + "=" * 60)
    print("📊 انتهى الاختبار")
    print("=" * 60)

if __name__ == "__main__":
    main()
