#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Arabic Text Helper
مساعد النصوص العربية
"""

def format_arabic_text(text):
    """
    تنسيق النص العربي للعرض الصحيح في Qt

    Args:
        text (str): النص المراد تنسيقه

    Returns:
        str: النص المنسق
    """
    if not text:
        return text

    # للنصوص العربية في Qt، نحتاج أحياناً لإضافة علامة اتجاه
    # إضافة Right-to-Left Mark (RLM) في بداية النص العربي
    if any('\u0600' <= char <= '\u06FF' for char in text):
        # النص يحتوي على أحرف عربية
        return '\u200F' + text  # RLM + النص

    return text

def setup_arabic_widget(widget):
    """
    إعداد ويدجت لدعم النصوص العربية
    
    Args:
        widget: الويدجت المراد إعداده
    """
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont
    
    # ضبط الاتجاه من اليمين لليسار
    widget.setLayoutDirection(Qt.RightToLeft)
    
    # ضبط خط مناسب للعربية
    font = QFont("Tahoma", 11)
    font.setStyleHint(QFont.SansSerif)
    widget.setFont(font)

def setup_arabic_application(app):
    """
    إعداد التطبيق لدعم النصوص العربية

    Args:
        app: تطبيق Qt
    """
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont

    # ضبط الاتجاه العام للتطبيق
    app.setLayoutDirection(Qt.RightToLeft)

    # ضبط الخط الافتراضي مع خطوط بديلة للعربية
    font = QFont()

    # قائمة الخطوط المفضلة للعربية
    arabic_fonts = ["Tahoma", "Arial Unicode MS", "Segoe UI", "Arial"]

    for font_name in arabic_fonts:
        font.setFamily(font_name)
        if font.exactMatch():
            break

    font.setPointSize(11)
    font.setStyleHint(QFont.SansSerif)
    font.setStyleStrategy(QFont.PreferAntialias)

    app.setFont(font)

# اختبار بسيط
if __name__ == "__main__":
    test_text = "مرحبا بك في نظام SHP ERP"
    formatted = format_arabic_text(test_text)
    print(f"النص الأصلي: {test_text}")
    print(f"النص المنسق: {formatted}")
    print("✅ مساعد النصوص العربية يعمل بشكل صحيح")
