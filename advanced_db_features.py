#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SHP ERP - Advanced Database Features
الميزات المتقدمة لإدارة قاعدة البيانات
"""

import os
import json
import csv
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from oracle_db_manager import get_db_manager

class BackupManager(QObject):
    """مدير النسخ الاحتياطي"""
    
    backup_progress = Signal(int, str)  # التقدم والرسالة
    backup_completed = Signal(bool, str)  # النجاح والرسالة
    
    def __init__(self):
        super().__init__()
        self.db_manager = get_db_manager()
        self.backup_dir = "backups"
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطي"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, table_name: str = "S_ERP_SYSTEM", 
                     backup_type: str = "full") -> bool:
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{table_name}_{backup_type}_{timestamp}"
            
            self.backup_progress.emit(10, "بدء عملية النسخ الاحتياطي...")
            
            if backup_type == "full":
                return self._create_full_backup(table_name, backup_filename)
            elif backup_type == "structure":
                return self._create_structure_backup(table_name, backup_filename)
            elif backup_type == "data":
                return self._create_data_backup(table_name, backup_filename)
            
        except Exception as e:
            self.backup_completed.emit(False, f"خطأ في النسخ الاحتياطي: {str(e)}")
            return False
    
    def _create_full_backup(self, table_name: str, filename: str) -> bool:
        """إنشاء نسخة احتياطية كاملة"""
        try:
            # الحصول على بنية الجدول
            self.backup_progress.emit(30, "جاري نسخ بنية الجدول...")
            
            structure_query = """
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                DATA_LENGTH,
                DATA_PRECISION,
                DATA_SCALE,
                NULLABLE,
                DATA_DEFAULT
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = :table_name
            ORDER BY COLUMN_ID
            """
            
            success, structure = self.db_manager.execute_query_safe(
                structure_query, (table_name,)
            )
            
            if not success:
                self.backup_completed.emit(False, f"فشل في الحصول على بنية الجدول: {structure}")
                return False
            
            # الحصول على البيانات
            self.backup_progress.emit(60, "جاري نسخ البيانات...")
            
            data_query = f"SELECT * FROM {table_name}"
            success, data = self.db_manager.execute_query_safe(data_query)
            
            if not success:
                self.backup_completed.emit(False, f"فشل في الحصول على البيانات: {data}")
                return False
            
            # حفظ النسخة الاحتياطية
            self.backup_progress.emit(80, "جاري حفظ النسخة الاحتياطية...")
            
            backup_data = {
                'metadata': {
                    'table_name': table_name,
                    'backup_type': 'full',
                    'timestamp': datetime.now().isoformat(),
                    'record_count': len(data)
                },
                'structure': structure,
                'data': data
            }
            
            backup_path = os.path.join(self.backup_dir, f"{filename}.json")
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
            
            # إنشاء نسخة CSV أيضاً
            csv_path = os.path.join(self.backup_dir, f"{filename}.csv")
            if data:
                with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
            
            self.backup_progress.emit(100, "تم إنشاء النسخة الاحتياطية بنجاح")
            self.backup_completed.emit(True, f"تم حفظ النسخة الاحتياطية في: {backup_path}")
            return True
            
        except Exception as e:
            self.backup_completed.emit(False, f"خطأ في النسخ الاحتياطي الكامل: {str(e)}")
            return False
    
    def _create_structure_backup(self, table_name: str, filename: str) -> bool:
        """إنشاء نسخة احتياطية للبنية فقط"""
        # تنفيذ مشابه للنسخ الكامل لكن بدون البيانات
        pass
    
    def _create_data_backup(self, table_name: str, filename: str) -> bool:
        """إنشاء نسخة احتياطية للبيانات فقط"""
        # تنفيذ مشابه للنسخ الكامل لكن بدون البنية
        pass
    
    def list_backups(self) -> List[Dict]:
        """قائمة النسخ الاحتياطية"""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for filename in os.listdir(self.backup_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.backup_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)
                        metadata = backup_data.get('metadata', {})
                        
                        file_stats = os.stat(filepath)
                        
                        backups.append({
                            'filename': filename,
                            'filepath': filepath,
                            'table_name': metadata.get('table_name', 'غير معروف'),
                            'backup_type': metadata.get('backup_type', 'غير معروف'),
                            'timestamp': metadata.get('timestamp', ''),
                            'record_count': metadata.get('record_count', 0),
                            'file_size': file_stats.st_size,
                            'created_date': datetime.fromtimestamp(file_stats.st_ctime)
                        })
                except Exception as e:
                    print(f"خطأ في قراءة النسخة الاحتياطية {filename}: {e}")
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['created_date'], reverse=True)
        return backups

class PerformanceMonitor(QObject):
    """مراقب الأداء"""
    
    performance_update = Signal(dict)  # تحديث بيانات الأداء
    
    def __init__(self):
        super().__init__()
        self.db_manager = get_db_manager()
        self.monitoring_active = False
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.collect_performance_data)
        self.performance_history = []
        self.max_history_size = 100
    
    def start_monitoring(self, interval_seconds: int = 30):
        """بدء مراقبة الأداء"""
        self.monitoring_active = True
        self.monitor_timer.start(interval_seconds * 1000)
    
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.monitoring_active = False
        self.monitor_timer.stop()
    
    def collect_performance_data(self):
        """جمع بيانات الأداء"""
        if not self.monitoring_active:
            return
        
        try:
            performance_data = {
                'timestamp': datetime.now(),
                'connection_status': self._check_connection_status(),
                'query_performance': self._measure_query_performance(),
                'system_stats': self._get_system_stats()
            }
            
            # إضافة للتاريخ
            self.performance_history.append(performance_data)
            
            # الحفاظ على حجم التاريخ
            if len(self.performance_history) > self.max_history_size:
                self.performance_history.pop(0)
            
            # إرسال التحديث
            self.performance_update.emit(performance_data)
            
        except Exception as e:
            print(f"خطأ في جمع بيانات الأداء: {e}")
    
    def _check_connection_status(self) -> Dict:
        """فحص حالة الاتصال"""
        start_time = time.time()
        success, message = self.db_manager.test_connection()
        response_time = (time.time() - start_time) * 1000  # بالميلي ثانية
        
        return {
            'connected': success,
            'response_time_ms': response_time,
            'message': message
        }
    
    def _measure_query_performance(self) -> Dict:
        """قياس أداء الاستعلامات"""
        test_queries = [
            ("simple_select", "SELECT COUNT(*) FROM S_ERP_SYSTEM"),
            ("complex_select", """
                SELECT s1.SYS_NAME, COUNT(s2.SYS_NO) as CHILD_COUNT
                FROM S_ERP_SYSTEM s1
                LEFT JOIN S_ERP_SYSTEM s2 ON s1.SYS_NO = s2.SYS_PARNT
                GROUP BY s1.SYS_NO, s1.SYS_NAME
                ORDER BY CHILD_COUNT DESC
            """)
        ]
        
        query_performance = {}
        
        for query_name, query in test_queries:
            start_time = time.time()
            success, result = self.db_manager.execute_query_safe(query)
            execution_time = (time.time() - start_time) * 1000
            
            query_performance[query_name] = {
                'success': success,
                'execution_time_ms': execution_time,
                'result_count': len(result) if success and isinstance(result, list) else 0
            }
        
        return query_performance
    
    def _get_system_stats(self) -> Dict:
        """الحصول على إحصائيات النظام"""
        stats_queries = {
            'total_systems': "SELECT COUNT(*) as count FROM S_ERP_SYSTEM",
            'active_systems': "SELECT COUNT(*) as count FROM S_ERP_SYSTEM WHERE (INACTIVE = 0 OR INACTIVE IS NULL)",
            'root_systems': "SELECT COUNT(*) as count FROM S_ERP_SYSTEM WHERE (SYS_PARNT IS NULL OR SYS_PARNT = 0)"
        }
        
        system_stats = {}
        
        for stat_name, query in stats_queries.items():
            success, result = self.db_manager.execute_query_safe(query, fetch_all=False)
            if success and result:
                system_stats[stat_name] = result['COUNT'] if isinstance(result, dict) else result[0]
            else:
                system_stats[stat_name] = 0
        
        return system_stats
    
    def get_performance_summary(self) -> Dict:
        """الحصول على ملخص الأداء"""
        if not self.performance_history:
            return {}
        
        recent_data = self.performance_history[-10:]  # آخر 10 قياسات
        
        # حساب المتوسطات
        avg_response_time = sum(
            data['connection_status']['response_time_ms'] 
            for data in recent_data
        ) / len(recent_data)
        
        avg_query_time = sum(
            data['query_performance']['simple_select']['execution_time_ms']
            for data in recent_data
            if data['query_performance']['simple_select']['success']
        ) / len([
            data for data in recent_data 
            if data['query_performance']['simple_select']['success']
        ]) if any(data['query_performance']['simple_select']['success'] for data in recent_data) else 0
        
        # حساب معدل النجاح
        success_rate = sum(
            1 for data in recent_data 
            if data['connection_status']['connected']
        ) / len(recent_data) * 100
        
        return {
            'monitoring_duration': len(self.performance_history),
            'avg_response_time_ms': avg_response_time,
            'avg_query_time_ms': avg_query_time,
            'success_rate_percent': success_rate,
            'last_update': self.performance_history[-1]['timestamp'] if self.performance_history else None
        }

class DataValidator(QObject):
    """مدقق البيانات"""
    
    validation_progress = Signal(int, str)
    validation_completed = Signal(bool, dict)
    
    def __init__(self):
        super().__init__()
        self.db_manager = get_db_manager()
    
    def validate_systems_data(self):
        """التحقق من صحة بيانات الأنظمة"""
        try:
            self.validation_progress.emit(10, "بدء التحقق من البيانات...")
            
            validation_results = {
                'total_systems': 0,
                'issues': [],
                'warnings': [],
                'recommendations': []
            }
            
            # الحصول على جميع الأنظمة
            success, systems = self.db_manager.execute_query_safe(
                "SELECT * FROM S_ERP_SYSTEM ORDER BY SYS_NO"
            )
            
            if not success:
                self.validation_completed.emit(False, {'error': systems})
                return
            
            validation_results['total_systems'] = len(systems)
            self.validation_progress.emit(30, f"فحص {len(systems)} نظام...")
            
            # فحص البيانات المفقودة
            self._check_missing_data(systems, validation_results)
            self.validation_progress.emit(50, "فحص البيانات المفقودة...")
            
            # فحص العلاقات الهرمية
            self._check_hierarchical_relationships(systems, validation_results)
            self.validation_progress.emit(70, "فحص العلاقات الهرمية...")
            
            # فحص التكرارات
            self._check_duplicates(systems, validation_results)
            self.validation_progress.emit(90, "فحص التكرارات...")
            
            # إنهاء التحقق
            self.validation_progress.emit(100, "تم التحقق من البيانات")
            self.validation_completed.emit(True, validation_results)
            
        except Exception as e:
            self.validation_completed.emit(False, {'error': str(e)})
    
    def _check_missing_data(self, systems: List[Dict], results: Dict):
        """فحص البيانات المفقودة"""
        for system in systems:
            sys_no = system.get('SYS_NO')
            
            # فحص الحقول المطلوبة
            if not system.get('SYS_CODE'):
                results['issues'].append(f"النظام {sys_no}: رمز النظام مفقود")
            
            if not system.get('SYS_NAME'):
                results['warnings'].append(f"النظام {sys_no}: اسم النظام مفقود")
    
    def _check_hierarchical_relationships(self, systems: List[Dict], results: Dict):
        """فحص العلاقات الهرمية"""
        system_ids = {system['SYS_NO'] for system in systems}
        
        for system in systems:
            sys_no = system.get('SYS_NO')
            parent_id = system.get('SYS_PARNT')
            
            if parent_id and parent_id != 0:
                if parent_id not in system_ids:
                    results['issues'].append(f"النظام {sys_no}: النظام الأب {parent_id} غير موجود")
                elif parent_id == sys_no:
                    results['issues'].append(f"النظام {sys_no}: يشير لنفسه كنظام أب")
    
    def _check_duplicates(self, systems: List[Dict], results: Dict):
        """فحص التكرارات"""
        seen_codes = {}
        seen_names = {}
        
        for system in systems:
            sys_no = system.get('SYS_NO')
            sys_code = system.get('SYS_CODE')
            sys_name = system.get('SYS_NAME')
            
            # فحص تكرار الرموز
            if sys_code:
                if sys_code in seen_codes:
                    results['issues'].append(
                        f"رمز النظام '{sys_code}' مكرر في الأنظمة {seen_codes[sys_code]} و {sys_no}"
                    )
                else:
                    seen_codes[sys_code] = sys_no
            
            # فحص تكرار الأسماء
            if sys_name:
                if sys_name in seen_names:
                    results['warnings'].append(
                        f"اسم النظام '{sys_name}' مكرر في الأنظمة {seen_names[sys_name]} و {sys_no}"
                    )
                else:
                    seen_names[sys_name] = sys_no
